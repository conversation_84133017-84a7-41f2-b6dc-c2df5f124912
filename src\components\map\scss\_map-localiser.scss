.localiser-content {
    .title {
        @include breakpoint(small down) {
            display: none;
        }
    }

    &.is-full-width {
        .map-localiser {
            @include full-width-block;
        }
    }
}

.map-localiser {
    $this: &;

    &__button-wrapper {
        display: none;

        @include breakpoint(small down) {
            display: flex;
        }
    }

    &__button[class] {
        font-size: 1.4rem !important;
        margin: 0 auto;
        min-height: 80px !important;
        padding: 2.1em 4.9em !important;

        .single-event-page & {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }
    }

    &__wrapper {
        @include breakpoint(medium) {
            display: block !important;
        }

        @include breakpoint(small down) {
            @include full-width-block;
            display: none;
        }
    }

    // Single consultation page
    &.is-popup {
        height: 100%;

        .map {
            height: 100%;
        }

        #{$this}__button {
            display: none;
        }

        #{$this}__wrapper {
            display: block;
            height: 100%;
        }
    }
}
