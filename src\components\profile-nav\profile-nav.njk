{%- from 'views/core-components/icon.njk' import Icon -%}

 {% set profileNavLinks = [
     'Jeunes',
     'Famille',
     'Séniors',
     'Nouveaux arrivants',
     'En situation de handicap',
     'Entrepreneurs',
     'Touristes'
 ] %}

{%- macro ProfileNav(
    useToggle = false,
    links = profileNavLinks
) -%}
    <div class="profile-nav {{ 'js-dropdown' if not useToggle }}">
        <button type="button" class="profile-nav__toggle {{ 'js-dropdown-toggle' if not useToggle }}" {{ 'data-sd-toggle=profile-nav' if useToggle }}>
            <span class="profile-nav__toggle-icon" aria-hidden="true"></span>
            <span class="profile-nav__toggle-text">VOUS ÊTES</span>
        </button>
        <div class="profile-nav__block {{ 'js-dropdown-block' if not useToggle }}" {{ 'data-sd-content=profile-nav' if useToggle }}>
            <nav class="profile-nav__menu">
                <ul class="profile-nav__menu-links">
                    {% for link in links %}
                        <li class="profile-nav__item">
                            <a href="#" class="profile-nav__link">
                                {{ Icon('fas fa-chevron-circle-right') }}
                                <span class="profile-nav__link-text">{{ link }}</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </nav>
        </div>
    </div>
{%- endmacro -%}
