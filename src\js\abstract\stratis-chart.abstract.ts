import Chart from 'chart.js';
import StratisElementAbstract from '../abstract/stratis-element.abstract';
import { IGlobalOptions } from '../interfaces/stratis-element.interface';
import OnInit from '../decorators/init-method.decorator';
import { serializeForm } from '@core/utils/form.utils';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';

export interface IStratisChartOptions extends IGlobalOptions<StratisChart> {
    chartType: string;
    chartLegend: boolean;
    chartCutoutPercentage: number;
    chartBarThickness: number;
}

/**
 * StratisChart plugin.
 */
export default class StratisChart extends StratisElementAbstract {
    public static defaultOptions: IStratisChartOptions = {
        classList: {
            hiddenClass: 'is-hidden',
            chartDataList: '[data-chart-data]',
            chartDataItem: 'js-chart-item',
            chartDataLabel: 'js-chart-label',
            chartDataColorBox: 'js-chart-data-colorbox',
            chartDataContainer: 'js-chart-data-container',
            chartResultsContainer: 'js-chart-results-container',
            chartWrapper: 'js-chart-wrapper',
            chartHideQuantity: 'data-chart-hide-quantity',
            chartUnit: 'data-chart-unit',
            voteContainer: 'js-chart-vote',
        },
        chartType: 'doughnut',
        chartLegend: false,
        chartCutoutPercentage: 70,
        chartBarThickness: 10,
        dataset: {},
        DOMElements: {},
    };
    public canvas: HTMLCanvasElement | null = null;
    public chartScaleMaxWidth: any;
    protected options: IStratisChartOptions = StratisChart.defaultOptions;
    protected chart: any = {};
    protected chartData: any = {};

    public constructor(selector: HTMLElement, options: Partial<IStratisChartOptions>) {
        super(selector, options);
    }

    /**
     * Set data to options
     * @param {object} chartData - object with chart data.
     */
    public setChartData(chartData): any {
        const { chartBarThickness } = this.options;
        const { values, labels, backgroundColors } = chartData;
        const datasets = [{
            data: values,
            backgroundColor: backgroundColors,
            borderWidth: 0,
            barThickness: chartBarThickness,
        }];

        return {
            labels,
            datasets,
        };
    }

    /**
     * Get chart values
     * @return {array} - array with chart values.
     */
    public getChartValues(chartDataItems): any {
        return chartDataItems.map((chartItem: any) => {
            const { value } = chartItem.dataset;

            return Number(value);
        });
    }

    public getChartUnits(chartDataItems): any {
        return chartDataItems.map((chartItem: any) => {
            const { chartItemUnit } = chartItem.dataset;

            return chartItemUnit;
        });
    }

    @OnInit()
    protected beforeDrawChart(): any {
        window.addEventListener('responsive', () => {
            this.canvas = this.element.querySelector('canvas');

            if (this.canvas) {
                this.canvas.removeAttribute('style');
                this.canvas.removeAttribute('width');
                this.canvas.removeAttribute('height');
                const { chartWrapper, hiddenClass } = this.options.classList;
                const chartWrapperBlock = this.element.querySelector(`.${chartWrapper}`) as HTMLElement;
                const chartData = this.getChartData(this.element);
                const chartDataValuesSum: number = chartData.values.reduce((sum, nextEl) => {
                    return sum + nextEl;
                }, 0);

                if (chartDataValuesSum === 0 && chartWrapperBlock) {
                    addClass(chartWrapperBlock, hiddenClass);
                }

                if (chartData) {
                    this.chart = this.drawChart(chartData);

                    this.setChartFormHandler();
                }
            }
        });
    }

    /**
     * Draw chart doughnut
     * @param {object} chartData - object with chart data.
     */
    protected drawChart(chartData): any {
        // eslint-disable-next-line @typescript-eslint/no-this-alias,no-shadow
        const self = this;
        const context = this.canvas!.getContext('2d');
        const { chartType, chartLegend, chartCutoutPercentage } = this.options;

        const chart = new Chart(context, {
            // The type of chart we want to create
            type: chartType,
            // The data for our dataset
            data: self.setChartData(chartData),
            // Configuration options go here
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: chartLegend,
                },
                cutoutPercentage: chartCutoutPercentage,
                scales: self.hideChartGrid(),
                tooltips: {
                    // Disable the on-canvas tooltip
                    enabled: false,
                    custom(tooltipModel): any {
                        self.customTooltip(tooltipModel, chart);
                    },
                },
            },
        });

        return chart;
    }

    /**
     * Hide chart grid
     */
    protected hideChartGrid(): any {
        return {
            xAxes: [{
                gridLines: {
                    display: false,
                    lineWidth: 0,
                    tickMarkLength: 0,
                },
                ticks: {
                    display: false,
                    min: 0,
                    max: this.chartScaleMaxWidth,
                },
            }],
            yAxes: [{
                gridLines: {
                    display: false,
                    lineWidth: 0,
                    tickMarkLength: 0,
                },
                ticks: {
                    display: false,
                    min: 0,
                    max: this.chartScaleMaxWidth,
                },
            }],
        };
    }

    // eslint-disable-next-line sonarjs/cognitive-complexity
    protected getTextForTooltip(tooltipModel): string {
        let innerHTML = '';

        function getBody(bodyItem): any {
            return bodyItem.lines;
        }

        if (tooltipModel.body) {
            const bodyLines = tooltipModel.body.map(getBody);
            bodyLines.forEach((body, i) => {
                let title = '';
                if (!isNaN(body) && tooltipModel.title.length) {
                    [title] = tooltipModel.title;
                }
                const color = tooltipModel.labelColors[i];
                let text = title ? `${title}: ${body}` : body;
                text = typeof text === 'string' ? text : text[0];

                if (this.options.chartType === 'bar') {
                    const { chartUnit } = this.options.classList;
                    const chartUnitAll = this.element.querySelector(`[${chartUnit}]`);
                    text = `${text}${chartUnitAll!.getAttribute(chartUnit)}`;
                } else {
                    text = text.replace(/: \d+$/gm, '');
                }

                text = text.replace(/%/g, '%&nbsp;&nbsp;');
                innerHTML += `<span class="chart-tooltip"><span style="background-color: ${color.backgroundColor}"></span>${text}</span>`;
            });
        }

        return innerHTML;
    }

    /**
     * Custom Tooltip
     * @param {object} tooltipModel - object with tooltip parameters.
     * @param {object} chart - canvas in form for which will apply handler.
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    protected customTooltip(tooltipModel, chart): any {
        const { chartType } = this.options;

        // Tooltip Element
        let tooltipEl = document.getElementById(`js-chart-${chartType}-tooltip`);

        // Create element on first render
        if (!tooltipEl) {
            tooltipEl = document.createElement('div');
            tooltipEl.id = `js-chart-${chartType}-tooltip`;
            document.body.appendChild(tooltipEl);
        }

        // Hide if no tooltip
        if (tooltipModel.opacity === 0) {
            tooltipEl.style.opacity = '0';
            return;
        }

        // Set caret Position
        tooltipEl.classList.remove('above', 'below', 'no-transform');
        if (tooltipModel.yAlign) {
            tooltipEl.classList.add(tooltipModel.yAlign);
        } else {
            tooltipEl.classList.add('no-transform');
        }

        const innerHtml = this.getTextForTooltip(tooltipModel);

        // Set Text

        if (tooltipEl) {
            tooltipEl.innerHTML = innerHtml;
        }

        const position = chart.canvas.getBoundingClientRect();

        // Display, position, and set styles for font
        tooltipEl.style.opacity = '1';
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.left = `${position.left + window.pageXOffset + tooltipModel.caretX}px`;
        tooltipEl.style.top = `${position.top + window.pageYOffset + tooltipModel.caretY}px`;
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.zIndex = '100';
        tooltipEl.style.transition = '500ms';
    }

    /**
     * Get chart data
     * @param {HTMLElement} element - element which data will be get.
     * @return {object} - object with chart data.
     */
    protected getChartData(element: HTMLElement): any {
        const { chartDataList, chartDataItem } = this.options.classList;
        const chartList = element.querySelector(chartDataList);

        if (!chartList) {
            return;
        }

        const chartDataItems = [...chartList.querySelectorAll(`.${chartDataItem}`)];

        // Set options for object
        if (this.chartData) {
            this.chartData.backgroundColors = this.getChartColors(chartDataItems);
            this.chartData.values = this.getChartValues(chartDataItems);
            this.chartData.itemsUnit = this.getChartUnits(chartDataItems);
            this.chartData.labels =
                this.options.chartType === 'bar' || this.options.chartType === 'bubble' ? this.getChartLabels() : this.createTooltipText(this.chartData, element);
        }

        return this.chartData;
    }

    /**
     * Get chart labels
     * @return {array} - array with chart labels.
     */
    protected getChartLabels(): any {
        const { chartDataLabel } = this.options.classList;

        return [...this.element.querySelectorAll(`.${chartDataLabel}`)]
            .map(label => label.textContent);
    }

    /**
     * Get chart colors
     * @return {array} - array with chart colors.
     */
    protected getChartColors(chartDataItems): any {
        const { chartDataColorBox } = this.options.classList;

        return chartDataItems.map((chartItem: any) => {
            if (chartItem && chartDataColorBox) {
                const colorBoxItem = chartItem.querySelector(`.${chartDataColorBox}`);
                const colorBoxStyles = colorBoxItem.getAttribute('data-color') || getComputedStyle(colorBoxItem).backgroundColor;

                if (colorBoxStyles) {
                    return colorBoxStyles;
                }
            }
        });
    }

    /**
     * Chart form handler
     */
    protected setChartFormHandler(): any {
        const form = this.element.querySelector<HTMLFormElement>('form');

        if (form) {
            form.addEventListener('submit', e => {
                e.preventDefault();

                const { url } = form.dataset;

                const formQuery = serializeForm(form);

                this.loadChartData(`${url}?${formQuery}`);
            });
        }
    }

    /**
     * Load chart data
     * @param {String} url - url to which a request will be sent to receive data.
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    protected async loadChartData(url): Promise<any> {
        const { chartDataContainer, voteContainer, chartWrapper, chartResultsContainer, hiddenClass } = this.options.classList;
        const resultsContainer = this.element.querySelector(`.${chartResultsContainer}`) as HTMLElement;

        try {
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
            });

            const data = await response.text();

            if (resultsContainer) {
                const chartDataResultsContainer = resultsContainer.querySelector(`.${chartDataContainer}`);
                const chartWrapperContainer = resultsContainer.querySelector(`.${chartWrapper}`) as HTMLElement;

                if (hasClass(resultsContainer, hiddenClass)) {
                    removeClass(resultsContainer, hiddenClass);

                    const voteBlock = this.element.querySelector(`.${voteContainer}`) as HTMLElement;

                    if (voteBlock) {
                        addClass(voteBlock, hiddenClass);
                    }

                    if (chartWrapperContainer && hasClass(chartWrapperContainer, hiddenClass)) {
                        removeClass(chartWrapperContainer, hiddenClass);
                    }
                    resultsContainer.removeAttribute('aria-hidden');
                    resultsContainer.setAttribute('tabindex', '-1');
                    resultsContainer.focus();
                }

                if (chartDataResultsContainer) {
                    chartDataResultsContainer.innerHTML = data;

                    const result = chartDataResultsContainer.querySelector<HTMLElement>('.js-interest-results-number');

                    if (result) {
                        const targetResult = this.element.querySelector<HTMLElement>('.js-interest-results-number')!;

                        targetResult.textContent = result.textContent;
                        result.remove();
                    }
                }

                const chartData = this.getChartData(this.element);

                this.chart.data = {
                    labels: chartData.labels,
                    datasets: [{ data: chartData.values, backgroundColor: chartData.backgroundColors, borderWidth: 0 }],
                };

                this.chart.update();
            }
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Creating the text for the tooltip.
     * @param data
     * @param element
     * @private
     */
    public createTooltipText(data, element): any {
        const { values, itemsUnit } = data;
        const { chartHideQuantity, chartUnit } = this.options.classList;
        const hideQuantity = element.querySelector(`[${chartHideQuantity}]`);
        const chartUnitAll = element.querySelector(`[${chartUnit}]`);
        const arrayTitles: string[] = [];
        const labels = this.getChartLabels();

        values.forEach((value, i) => {
            let text = '';

            if (labels[i]) {
                text += labels[i].trim();
            }

            if (values[i] && !hideQuantity) {
                text += `: ${values[i]}`;

                if (chartUnitAll) {
                    text += chartUnitAll.getAttribute(chartUnit);
                } else if (itemsUnit[i]) {
                    text += itemsUnit[i];
                }
            }

            arrayTitles.push(text);
        });

        return arrayTitles;
    }
}
