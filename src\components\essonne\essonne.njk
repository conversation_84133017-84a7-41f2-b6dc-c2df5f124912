{%- from 'views/utils/utils.njk' import setAttr -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}

{% set defaultSettingsItem = {
    title: lorem(3, 'words'),
    teaser: false,
    category: lorem(range(1, 3) | random, 'words'),
    imageSizes: ['127x127?767','197x197?1279','221x213'],
    tag: 'h3',
    role: false,
    ariaLevel: false
} %}

{#
    EssonnesItem template.
    @param {object} settings - user settings object
#}
{%- macro EssonnesItem(settings = {}) -%}
    {# Default params  #}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}

    <article class="essonnes-item">
        <div class="essonnes-item__content">
            {% if params.title %}
                <{{ params.tag }}
                    class="item-title essonnes-item__title"
                    {{ setAttr('role', params.role) }}
                    {{ setAttr('aria-level', params.ariaLevel) }}
                >
                {% if params.category %}
                    <span class="theme essonnes-item__category">{{ params.category }}</span>
                    <span class="sr-only"> : </span>
                {% endif %}
                    <a href="#" class="essonnes-item__title-link">
                        {% if params.useInlineBgTitle %}
                            <span class="js-inline-bg underline" data-max-length="20">{{ params.title }}</span>
                        {% else %}
                            <span class="underline">{{ params.title }}</span>
                        {% endif %}
                    </a>
                </{{ params.tag }}>
            {% endif %}
            {% if params.teaser %}
                <p class="item-teaser essonnes-item__teaser">{{ params.teaser }}</p>
            {% endif %}
        </div>
        {{ Image({
            className: 'essonnes-item__image',
            sizes: params.imageSizes,
            alt: 'image alt',
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(50) | random
        }) }}
    </article>
{%- endmacro -%}

{#
    EssonnesHome template.
    Template for essonnes on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of essonnes
    @param {boolean} moreButton - insert more link
#}
{%- macro EssonnesHome(
    titleText = "C'est aussi l'Essonne",
    itemsCount = 4,
    cols = 2,
    mdCols = 2,
    moreButton = true,
    type = '',
    itemsList = true,
    listClass = 'essonnes-list',
    sectionModifier = '',
    imagesSizes = [
        ['224x148?1279', '396x262'], 
        ['189x164?1279', '333x289'], 
        ['167x210?1279', '294x370'], 
        ['218x164?1279', '385x290']
    ]
) -%}
    {# imageSizes: ['127x127?767','197x197?1279','221x213'] #}
    {% call Section(className = 'essonnes-home' + ' ' + type + ' ' + sectionModifier, container = 'essonnes-home__container') %}
            <div class="section__title">
                {{ Title.TitlePrimary(text = titleText) }}
            </div>
            <div class="section__content essonnes-home__content">
                {% for size in range(4) %}
                    {{ EssonnesItem({imageSizes : imagesSizes[size]}) }}
                {% endfor %}
            </div>
        {% if moreButton %}
            <div class="section__more-links essonnes-home__more-links">
                {% if moreButton %}
                    {{ Link(
                        href = "#",
                        text = 'TOUS LES DOSSIERS',
                        className = 'btn is-primary is-sm-small',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}