{% from 'views/utils/utils.njk' import getImagePath %}

{#
    Map Header
#}
{%- macro MapHeader() -%}
    <div class="map-header">
        <div class="map-header__logo-wrapper">
            <!--  <img src="{{ getImagePath('map/logo.png') }}" alt="Logo du site"> -->
            <p class="map-site-domen">
                cartographie </br>
                <a href="#" class="map-site-domen__domen">
                    <span>essonne</span>.fr
                </a>
            </p>
        </div>
        <button
            type="button"
            class="map-header__filters-button js-tooltip js-map-template-toggle"
            aria-controls="map-filter-column"
            data-content="Filtrer"
            >
            <span class="fa fa-sliders-h" aria-hidden="true"></span>
            <span class="sr-only">Filtrer</span>
        </button>
    </div>
{%- endmacro -%}
