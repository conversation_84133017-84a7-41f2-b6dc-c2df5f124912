.deliberations-content-item {
    @extend %link-block-context;
    $this: &;
    align-items: center;
    display: flex;
    margin-top: 50px;
    position: relative;

    &::before {
        @include size(100vw, 535px);
        @include absolute(0, 151px, null, null);
        background-color: var(--color-1--1);
        border-left: none;
        border-radius: 0 20px 20px 0;
        content: "";
        transform: matrix(1, -0.07, 0.07, 1, 0, 0);
        z-index: -10;

        @include breakpoint(medium down) {
            @include size(100%, calc(100% + 60px));
            right: 40%;
        }

        .is-width-66 & {
            @include size(90%, calc(100% + 120px));
            top: -40px;

            @include breakpoint(medium down) {
                @include size(520px, calc(100% + 110px));
                right: auto;
                top: -40px;
            }
        }

        .is-width-33 & {
            @include size(410px, 93%);
            left: 0;
            right: 0;
            top: 90px;
        }

        @include breakpoint(small down) {

            .is-width-66 &,
            .is-width-33 &,
            .is-full-width & {
                @include size(calc(100vw + 50px), 95%);
                @include absolute(70px, 0, null, -50px);
                border-radius: 0;
            }
        }
    }

    &::after {
        @include size(100vw, 205px);
        @include absolute(null, 50%, -104px, null);
        background-color: var(--color-2--1);
        border-radius: 10px;
        content: "";
        transform: matrix(-1, -0.05, 0.05, -1, 0, 0);
        z-index: -11;

        @include breakpoint(medium down) {
            bottom: -78px;
        }

        .is-width-66 & {
            @include size(454px, 239px);
            bottom: -98px;
            right: auto;
        }

        .is-width-33 & {
            @include size(80%, 100px);
            bottom: -72px;
            left: 0;
            right: auto;
        }

        @include breakpoint(small down) {
            @include absolute(null,
                0 !important,
                -73px !important,
                -18px !important);
            border-top-right-radius: 0;
            width: calc(100vw - 30px) !important;
        }
    }

    @include breakpoint(small down, true) {
        align-items: initial;
        flex-direction: column;
        text-align: center;
    }

    @include breakpoint(small down) {
        flex-direction: column-reverse;
        margin-left: auto;
        margin-right: auto;
        width: 100% !important;
    }

    .is-width-33 & {
        align-items: center;
        flex-direction: column-reverse;
        text-align: center;
        width: 423px;
    }

    &__image {
        display: inline-block;
        flex-shrink: 0;
        height: auto;
        position: relative;

        @include breakpoint(small down, true) {
            margin: 0 auto;
        }

        img {
            box-shadow: 0 0 20px rgba($color-black, 0.16);

            @include breakpoint(medium down) {
                width: 100%;
            }
        }
    }

    &__content {
        flex-grow: 1;
        margin-right: 66px;
        max-width: 457px;
        padding: 53px 0 0;

        @include breakpoint(medium down, true) {
            margin-right: 0;
            padding: 10px 0 0 75px;
        }

        @include breakpoint(medium down) {
            max-width: 377px;
            padding-left: 0;
            padding-top: 0;
        }

        @include breakpoint(small down, true) {
            padding: 30px 0 0;
        }

        @include breakpoint(small down) {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-width-66 & {
            margin-left: 66px;
            margin-right: 0;
        }

        .is-width-33 & {
            margin-right: 0;
            max-width: 296px;
            padding-top: 22px;
        }
    }

    &__category {
        color: $color-white;
        font-weight: var(--fw-normal);
        margin-bottom: 13px;

        @include breakpoint(small down, true) {
            font-size: 1.4rem;
        }
    }

    &__title {
        color: $color-white;
        font-size: 3.5rem;
        margin-bottom: 11px;
        max-width: 100%; //For IE

        @include breakpoint(medium down, true) {
            margin-bottom: 23px;
        }

        @include breakpoint(medium down) {
            font-size: 2.7rem;
        }

        @include breakpoint(small down, true) {
            font-size: 2.4rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
        color: $color-white;

        .underline {
            @include multiline-underline($color: $color-white);
        }
    }

    &__subtitle {
        @include font(null, 3rem, var(--fw-normal));
        color: $color-white;
        display: block;
        margin: 5px 0 0;

        @include breakpoint(medium down, true) {
            font-size: 3rem;
        }

        @include breakpoint(medium down) {
            font-size: 2.4rem;
        }

        @include breakpoint(small down, true) {
            font-size: 2.2rem;
        }
    }

    &__publication[class] {
        color: $color-white;
        margin-top: 20px;
        padding-left: 0;

        &::before {
            content: none;
        }

        @include breakpoint(small down, true) {
            padding-left: 0;
        }
    }

    &__bottom-wrapper {
        @include breakpoint(medium down, true) {
            display: block;
        }

        @include breakpoint(small down, true) {
            display: block;
        }
    }

    &__teaser {
        color: $color-white;
        font-size: 1.7rem;
        font-weight: var(--fw-normal);
        margin: 0 30px 45px 0;

        @include breakpoint(medium down, true) {
            margin-right: 0;
        }

        @include breakpoint(small down, true) {
            margin-right: 0;
        }

        .is-width-33 & {
            margin-bottom: 10px;
        }
    }

    &__actions {
        align-items: flex-end;
        display: flex;
        flex-shrink: 0;
        position: relative;
        z-index: 3;

        @include breakpoint(small down, true) {
            display: block;
        }

        .document-actions {
            &__item {
                background-color: transparent;
                border: none;

                &:first-child {
                    .document-actions {
                        &__link {
                            color: var(--color-1--1);
                            display: flex;
                            flex-direction: column;

                            span[class*="fa-"] {
                                background-color: $color-white;
                            }
                        }

                        &__link-text {
                            color: $color-white;
                            font-size: 1.3rem;
                            font-weight: var(--fw-normal);
                            margin-left: 0;
                            margin-top: 5px;

                            @include breakpoint(small down) {
                                font-size: 1rem;
                            }
                        }
                    }
                }

                &:last-child {
                    .document-actions {
                        &__link {
                            color: var(--color-1--2);

                            span[class*="fa-"] {
                                background-color: var(--color-2--1);
                            }
                        }

                        &__link-text {
                            color: $color-white;
                        }
                    }
                }

                span[class*="fa-"] {
                    @include size(60px);
                    @include min-size(auto);
                    align-content: center;
                    border-radius: 40px;
                    font-size: 1.6rem;
                    padding: 0;
                    text-align: center;
                }
            }

            &__link {
                min-width: auto;
                padding: 0;
            }

            @include breakpoint(small down, true) {
                align-items: center;
            }

            @include breakpoint(small down) {
                align-items: flex-start;
                justify-content: center;
            }

            .is-width-33 & {
                align-items: flex-start !important;
                justify-content: center;
            }
        }
    }
}
