{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}
{%- from 'views/utils/utils.njk' import svg -%}

{#
    QuickLinksContentInfo template.
#}
{% macro QuickLinksContentInfo(
    nameIcon = 'avatar',
    title = 'Mes <br>démarches',
    teaser = 'J’accède à mon compte pour mes démarches, mes abonnements, ...'
) %}
    <div class="quicklinks-content-info">
        <div class="quicklinks-content-info__text">
            <div class="quicklinks-content-info__svg-wrapper" aria-hidden="true">
                {{ svg('icons/' + nameIcon, 60, 60) }}
            </div>
            <h3 class="quicklinks-content-info__title">
                <a href="#" class="quicklinks-content-info__link">
                    <span class="underline">{{ title | safe }}</span>
                </a>
            </h3>
        </div>
        <p class="quicklinks-content-info__teaser">{{ teaser }}</p>
        <span class="quicklinks-content-info__icon far fa-long-arrow-right" aria-hidden="true"></span>
    </div>
{% endmacro %}

{#
    QuickLinksContentItem template.
#}
{%- macro QuickLinksContentItem(
    nameIcon = 'archery',
    title = lorem(1, 'word'),
    modifier = ''
) -%}
    <div class="quicklinks-content-item {{ modifier }}">
        <div class="quicklinks-content-item__svg-wrapper" aria-hidden="true">
            {{ svg('icons/' + nameIcon, 60, 60) }}
        </div>
        <a href="#" class="quicklinks-content-item__text">
            <span class="underline">{{ title }}</span>
        </a>
    </div>
{%- endmacro -%}

{#
    QuickLinksContentBlock template.
#}
{%- macro QuickLinksContentBlock(settings = {}) -%}
    {% set params = Helpers.merge({
        listLinks: [
            ['man-and-trash', 'Propreté'],
            ['payment', 'Paiement en ligne'],
            ['cone', 'Infos travaux'],
            ['man-and-message', 'Je signale...'],
            ['route', 'Annuaires'],
            ['family', 'Portail famille'],
            ['phonendoscope', 'Santé'],
            ['communication', 'Contact']
        ],
        modifier: '',
        type: 'carousel',
        carouselItemsToShow: [4, 3, 1],
        itemsCount: 3,
        carouselAttrs: {
            'aria-label' : 'Liens'
        }
    }, settings) %}
    <div class="quicklinks-content {{ params.modifier }}">
        {% if params.type === 'list' %}
            <nav class="quicklinks-content__wrapper">
                <ul class="quicklinks-content__list">
                    {% for link in params.listLinks %}
                        {% if loop.index <= params.itemsCount %}
                            <li class="quicklinks-content__list-item">
                                {{ QuickLinksContentItem(
                                    nameIcon = link[0],
                                    title = link[1]
                                ) }}
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
        {% endif %}
        {% if params.type === 'carousel' %}
            {% call CarouselWrapper(settings = {
                wrapperClassName: 'quicklinks-content-block',
                wrapperTag: 'ul',
                itemsToShow: params.carouselItemsToShow,
                enableNavigationAlign: false,
                arrows: {
                    outside: true,
                    next: {
                        text: 'Accès rapide suivant',
                        icon: 'far fa-long-arrow-right'
                    },
                    prev: {
                        text: 'Accès rapide précédent',
                        icon: 'far fa-long-arrow-left'
                    }
                },
                actions: false,
                autoplay: false,
                wrapperAttrs: params.carouselAttrs
            }) %}
                {% for link in params.listLinks %}
                    <li class="quicklinks-content-block__item swiper-item">
                        {{ QuickLinksContentItem(
                            nameIcon = link[0],
                            title = link[1]
                        ) }}
                    </li>
                {% endfor %}
            {% endcall %}
        {% endif %}
    </div>
{%- endmacro -%}

{#
    QuickLinksContentSection template.
#}
{%- macro QuickLinksContentSection(
    className = 'quicklinks-content',
    titleText = 'Accès rapides',
    blockSettings = {}
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ QuickLinksContentBlock(blockSettings) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksContentHome template.
#}
{%- macro QuickLinksContentHome(
    className = 'quicklinks-content-home has-info-block',
    titleText = 'Accès rapides',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-content-home__container') %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
            {{ QuickLinksContentBlock(blockSettings) }}
            {{ QuickLinksContentInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksContentHomeFluid template.
#}
{%- macro QuickLinksContentHomeFluid(
    className = 'quicklinks-content-home is-fluid has-info-block has-box-shadow',
    titleText = 'Accès rapides',
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-content-home__container') %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content">
            {{ QuickLinksContentBlock(blockSettings) }}
            {{ QuickLinksContentInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksContentHomeLight template.
#}
{%- macro QuickLinksContentHomeLight(
    className = 'quicklinks-content-home is-light',
    titleText = 'Accès rapides',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted',
        carouselItemsToShow: [7, 3, 1]
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-content-home__container') %}
        <h2 class="ghost">{{ titleText }}</h2>
        <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
            {{ QuickLinksContentBlock(blockSettings) }}
        </div>
    {% endcall %}
{%- endmacro -%}
