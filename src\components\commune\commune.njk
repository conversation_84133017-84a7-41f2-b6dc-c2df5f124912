{%- from 'views/core-components/infos.njk' import InfoBlock, InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleWidget -%}
{%- from 'components/social/social.njk' import SocialLinks -%}


{#
    ContactItem template.
#}
{%- macro CommuneItem(
    className = '',
    imageSizes = ['200x200?1279', '250x250'],
    category = true,
    contentInfo = true,
    structure = false
) -%}
    <article class="commune-item">
        {{ Image({
            sizes: imageSizes,
            className: 'commune-item__picture',
            serviceID: range(50) | random,
            alt: ''
        }) }}
        <div class="commune-item__content">
            <div class="commune-item__content-top">
                <h3 class="commune-item__title">
                    {%- if category %}
                        <span class="commune-item__theme">Thématique</span>
                        <span class="sr-only">
                            :
                        </span>
                    {%- endif %}
                    <a href="single-commune.html" class="commune-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor'}}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="commune-item__content-info">
                    <div class="commune-item__details">
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'address') }}
                            {{ InfoItem(type = 'hours') }}
                            {% endcall %}
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'website') }}
                            <div class="info-item infos__item is-itineraire">
                                {{ InfoItem(type = 'itineraire') }}
                            </div>
                            {% endcall %}
                    </div>
                    <ul class="commune-item__infos">
                        <div class="flex-row">
                            <div class="col-lg-6 col-md-12">
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'tel:0494001234',
                                        text = '04 94 00 12 34',
                                        textSrOnly = 'Téléphone',
                                        className = 'btn is-small',
                                        icon = 'far fa-phone'
                                    ) }}
                                </li>
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'tel:0494000000',
                                        text = '04 94 00 00 00',
                                        textSrOnly = 'Fax',
                                        className = 'btn is-small',
                                        icon = 'far fa-fax'
                                    ) }}
                                </li>
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'tel:0639987845',
                                        text = '06 39 98 78 45',
                                        textSrOnly = 'Mobile',
                                        className = 'btn is-small',
                                        icon = 'far fa-mobile'
                                    ) }}
                                </li>
                            </div>
                            <div class="col-lg-6 col-md-12">
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'mailto:<EMAIL>',
                                        text = 'Courriel',
                                        className = 'btn is-small',
                                        icon = 'far fa-at'
                                    ) }}
                                </li>
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'mailto:<EMAIL>',
                                        text = 'Courriel',
                                        className = 'btn is-small',
                                        icon = 'far fa-at'
                                    ) }}
                                </li>
                                <li class="commune-item__infos-item">
                                    {{ Link(
                                        href = 'mailto:<EMAIL>',
                                        text = 'Courriel',
                                        className = 'btn is-small',
                                        icon = 'far fa-at'
                                    ) }}
                                </li>
                            </div>
                        </div>
                    </ul>
                </div>
                {{ SocialLinks(
                                className = 'has-mt-3',
                                links = [
                                    {
                                        title: 'Compte Facebook',
                                        icon: 'fab fa-facebook-f'
                                    },
                                    {
                                        title: 'Compte Twitter',
                                        icon: 'fa-brands fa-x-twitter'
                                    },
                                    {
                                        title: 'Compte Instagram',
                                        icon: 'fab fa-instagram'
                                    },
                                    {
                                        title: 'Compte Youtube',
                                        icon: 'fab fa-youtube'
                                    },
                                    {
                                        title: 'Compte Linkedin-in',
                                        icon: 'fab fa-linkedin-in'
                                    },
                                    {
                                        title: 'Compte Tiktok',
                                        icon: 'fab fa-tiktok'
                                    },
                                    {
                                        title: 'Compte Snapchat',
                                        icon: 'fa-brands fa-snapchat'
                                    },
                                    {
                                        title: 'Compte Pinterest',
                                        icon: 'fa-brands fa-pinterest-p'
                                    },
                                    {
                                        title: 'Compte Vimeo',
                                        icon: 'fa-brands fa-vimeo-v'
                                    }
                                ]
                            ) }}
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro CommuneList(
    itemClass = 'has-mb-1',
    cols = 4,
    mdCols = 4,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ CommuneItem(imageSizes = ['282x187']) }}
    {% endcall %}
{%- endmacro -%}
