{%- from 'views/core-components/infos.njk' import InfoBlock, InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleWidget -%}
{%- from 'components/social/social.njk' import SocialLinks -%}
{%- from 'views/utils/utils.njk' import svg with context -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}

{#
    ContactItem template.
#}
{%- macro ContactsItem(
    className = '',
    imageSizes = ['200x200?1279', '250x250'],
    category = true,
    contentInfo = true,
    structure = false
) -%}
    <article class="contact-item {{ 'is-structure' if structure }} {{ className }}">
        {{ Image({
            sizes: imageSizes,
            className: 'contact-item__picture',
            serviceID: range(50) | random,
            alt: ''
        }) }}
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only">
                            :
                        </span>
                    {%- endif %}
                    <a href="single-{{ 'structures' if structure else 'contacts' }}.html" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' if structure else 'Prénomlorem Nomsitamet' }}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                    <div class="contact-item__details is-spaces">
                        {% if structure %}
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'address') }}
                            {{ InfoItem(type = 'hours') }}
                            {% endcall %}
                            {% call InfoBlock() %}
                            {{ InfoItem(type = 'website') }}
                            {{ InfoItem(type = 'itineraire') }}
                            {% endcall %}
                            {{ SocialLinks(
                                className = 'has-mt-3',
                                links = [
                                    {
                                        title: 'Compte Facebook',
                                        icon: 'fab fa-facebook-f'
                                    },
                                    {
                                        title: 'Compte Twitter',
                                        icon: 'fa-brands fa-x-twitter'
                                    },
                                    {
                                        title: 'Compte Instagram',
                                        icon: 'fab fa-instagram'
                                    },
                                    {
                                        title: 'Compte Youtube',
                                        icon: 'fab fa-youtube'
                                    },
                                    {
                                        title: 'Compte Linkedin-in',
                                        icon: 'fab fa-linkedin-in'
                                    },
                                    {
                                        title: 'Compte Tiktok',
                                        icon: 'fab fa-tiktok'
                                    }
                                ]
                            ) }}
                        {% else %}
                            <div class="contact-item__function-wrapper">
                                <p class="contact-item__function is-main">Fonction 1 lorem ipsum dolor sir amet</p>
                                <a href="#" class="contact-item__function is-location">Commune lorem ipsum</a>
                            </div>
                            <div class="contact-item__function-wrapper">
                                <p class="contact-item__function">Fonction 2 lorem ipsum dolor consectur elis</p>
                                <a href="#" class="contact-item__function is-location">Commune lorem ipsum</a>
                            </div>
                            <div class="contact-item__function-wrapper">
                                <p class="contact-item__function">Fonction 3 passam filis poder</p>
                                <a href="#" class="contact-item__function is-location">Commune lorem ipsum</a>
                            </div>
                        {% endif %}
                    </div>
                    <ul class="contact-item__infos">
                        {% if structure %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494001234',
                                    text = '04 94 00 12 34',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0639987845',
                                    text = '06 39 98 78 45',
                                    textSrOnly = 'Mobile',
                                    className = 'btn is-small',
                                    icon = 'far fa-mobile'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {% else %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                              <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Fax',
                                    className = 'btn is-small',
                                    icon = 'far fa-fax'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>

                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>

                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    TravauxItem template.
#}
{%- macro TravauxItem(
    className = '',
    imageSizes = ['200x200?1279', '384x257'],
    teaser = lorem(2),
    category = 'Thématique',
    contentInfo = true,
    structure = false,
    infos = true
) -%}
    <article class="travaux-item {{ 'is-structure' if structure }} {{ className }}">
        <div class="travaux-item__image">
            <p class="travaux-item__category">{{ category }}</p>
            {% if imageSizes %}
                {{ Image({
                    sizes: imageSizes,
                    className: 'travaux-item__picture',
                    serviceID: range(50) | random,
                    alt: ''
                }) }}
            {% endif %}
        </div>
        <div class="travaux-item__content">
            <div class="travaux-item__content-top">
                <h3 class="travaux-item__title">
                    <a href="single-travaux.html" class="travaux-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' if structure else 'Prénomlorem Nomsitamet' }}
                        </span>
                    </a>
                </h3>
                <p class="travaux-item__teaser">{{ teaser | safe }}</p>
            </div>
            {% if contentInfo %}
                <div class="travaux-item__content-info">
                    <div class="travaux-item__details">
                        {% if structure %}
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'address-travaux') }}
                            {% endcall %}
                        {% endif %}
                    </div>
                    {% if infos %}
                        <ul class="travaux-item__infos">
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'consequence') }}
                            {% endcall %}
                        </ul>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    ContactList template.
#}
{%- macro ContactsList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
        {{ ContactsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro StructuresList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
        {{ ContactsItem(imageSizes = ['250x166'], structure = true) }}
    {% endcall %}
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro TravauxList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
        {{ TravauxItem(imageSizes = ['384x257'], structure = true) }}
    {% endcall %}
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro TravauxCarousel(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call CarouselWrapper(settings = {
        wrapperClassName: 'travaux-block',
        jsClassName: 'travaux-js-swiper',
        pagination: 'outside',
        wrapperTag: 'ul',
        itemsToShow: 1,
        enableNavigationAlign: false,
        arrows: {
            next: {
                text: 'Travaux suivant',
                icon: 'fa-light fa-chevron-right'
            },
            prev: {
                text: 'Travaux précédent',
                icon: 'fa-light fa-chevron-left'
            }
        },
        actions: false,
        autoplay: false,
        wrapperAttrs: {
            'aria-label': 'Travaux'
        }
    }) %}
        {% for item in range(0, 9) %}
            <li class="travaux-block__item swiper-item">
                {{ TravauxItem(imageSizes = false, structure = true, infos = false) }}
            </li>
        {% endfor %}
    {% endcall %}
    <div class="carousel-pagination-event">
        {% for i in range(count) %}
            <span class="travaux-bullet {% if i == 0 %}active{% endif %}"></span>
        {% endfor %}
    </div>
{%- endmacro -%}

{#
    TravauxHome template.
#}
{%- macro TravauxHome(
    className = 'travaux-home has-info-block',
    titleText = 'Travaux',
    moreButton = true
) -%}
    {% call Section(className = className, container = 'travaux-home__container') %}
    <h2 class="ghost">{{ titleText }}</h2>
    <div class="section__content">
        {{ TravauxCarousel() }}
    </div>
    <div class="section__more-links">
        {{ Link(
            href = kGlobalLinks.travaux,
            text = 'Tous les travaux',
            className = 'btn is-primary is-sm-small',
            icon = false
        ) }}
        {{ Link(
            href = kGlobalLinks.travauxMap,
            text = 'Voir sur la carte interactive',
            className = 'btn is-primary is-sm-small',
            icon = false
        ) }}
    </div>
    {% endcall %}
{%- endmacro -%}

{#
    ContactsContent template.
#}
{%- macro ContactsContent(
    className = 'contact-content',
    titleText = 'Contact',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false,
    anchors = false,
    textAncre = ''
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText,
                iconPath = false,
                className = className,
                anchors = anchors,
                textAncre = textAncre
            ) }}
    </div>
    <div class="section__content">
        {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = 1,
                xsCols = false
            ) }}
    </div>
    {% if moreButton or proposerButton %}
        <div class="section__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'fas fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'fas fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsStructuresContent template.
#}
{%- macro ContactsStructuresContent(
    className = 'contact-section',
    titleText = 'Structure',
     titleClassName = '',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText,
                className = titleClassName
            ) }}
    </div>
    <div class="section__content">
        {% call List(count = itemsCount, cols = 1, mdCols = false, smCols = false, xsCols = false) %}
        {{ ContactsItem(imageSizes = ['250x166'], structure = true) }}
        {% endcall %}
    </div>
    {% if moreButton or proposerButton %}
        <div class="section__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = 'page-proposer.html',
                        text = 'Proposer un structure',
                        className = 'btn',
                        icon = 'fas fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = 'list-contacts.html',
                        text = 'Toutes les structures',
                        className = 'btn',
                        icon = 'fas fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsSidebar template.
#}
{%- macro ContactsSidebar(
    titleText = 'Contact',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
    <div class="widget__title">
        {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
    </div>
    <div class="widget__content">
        {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = false,
                xsCols = false
            ) }}
    </div>
    {% if moreButton or proposerButton %}
        <div class="widget__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'fas fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'fas fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsStructuresSidebar template.
#}
{%- macro ContactsStructuresSidebar(
    titleText = 'Structure',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
    <div class="widget__title">
        {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
    </div>
    <div class="widget__content">
        {% call List(count = itemsCount, cols = 1, mdCols = false, smCols = false, xsCols = false) %}
        {{ ContactsItem({ imageSizes: ['250x200'], structure: true }) }}
        {% endcall %}
    </div>
    {% if moreButton or proposerButton %}
        <div class="widget__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'fas fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'fas fa-plus-circle'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro PersonneList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(itemClass = itemClass, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols, count = count) %}
    {{ PersonneItem(imageSizes = ['250x166']) }}
    {% endcall %}
{%- endmacro -%}
{#
    PersonneItem template.
#}
{%- macro PersonneItem(
    className = '',
    imageSizes = ['200x200?1279', '250x250'],
    category = true,
    contentInfo = true
) -%}
    <article class="contact-item">
        {{ Image({
            sizes: imageSizes,
            className: 'contact-item__picture',
            serviceID: range(50) | random,
            alt: ''
        }) }}
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only">
                            :
                        </span>
                    {%- endif %}
                    <a href="single-contacts.html" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor'}}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                    <div class="contact-item__details">
                        {% call InfoBlock() %}
                        {{ InfoItem(type = 'address') }}
                        {{ InfoItem(type = 'hours') }}
                        {% endcall %}
                        {% call InfoBlock() %}
                        {{ InfoItem(type = 'website') }}
                        {{ InfoItem(type = 'itineraire') }}
                        {% endcall %}
                    </div>
                    <ul class="contact-item__infos">
                        <li class="contact-item__infos-item">
                            {{ Link(
                                href = 'tel:0494001234',
                                text = '04 94 00 12 34',
                                textSrOnly = 'Téléphone',
                                className = 'btn is-small',
                                icon = 'far fa-phone'
                            ) }}
                        </li>
                        <li class="contact-item__infos-item">
                            {{ Link(
                                href = 'tel:0639987845',
                                text = '06 39 98 78 45',
                                textSrOnly = 'Mobile',
                                className = 'btn is-small',
                                icon = 'far fa-mobile'
                            ) }}
                        </li>
                        <li class="contact-item__infos-item">
                            {{ Link(
                                href = 'mailto:<EMAIL>',
                                text = 'Courriel',
                                className = 'btn is-small',
                                icon = 'far fa-at'
                            ) }}
                        </li>
                    </ul>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}
