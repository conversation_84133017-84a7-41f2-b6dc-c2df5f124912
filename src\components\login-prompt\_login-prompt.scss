.login-prompt {
    @include font(var(--typo-1), 1.6rem, var(--fw-normal));
    background-color: $color-3--2;
    border-top: 5px solid var(--color-2--1);
    color: $color-black;
    position: relative;
    z-index: 1;

    @include breakpoint(small down) {
        font-size: 1.3rem;
    }

    &__container {
        @extend %container-fluid;
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin: 0 auto;
        padding: 7px 55px;

        @include breakpoint(small down) {
            padding: 6px 12px;
        }
    }

    &__online {
        align-items: center;
        display: flex;
        flex-grow: 1;
    }

    &__online-login {
        margin: 0 auto 0 0;

        span {
            text-transform: uppercase;
        }
    }

    &__online-list {
        align-items: center;
        display: flex;
    }

    &__online-link {
        @include trs;
        font-size: 1.6rem;
        padding: 0 13px;

        @include on-event {
            @include fa-icon-style(false) {
                color: var(--color-1--2);
            }
        }
    }

    &__offline {
        display: flex;
    }

    &__offline-link {
        display: block;
        padding: 0 10px;
        text-decoration: none;

        @include on-event {
            text-decoration: underline;
        }
    }

    &__online-item,
    &__offline-item {
        &:not(:nth-of-type(1)) {
            position: relative;

            &::before {
                @include absolute(50%, null, null, 0);
                @include size(1px, 17px);
                background-color: $color-3--4;
                content: '';
                transform: translateY(-50%);
            }
        }
    }
}
