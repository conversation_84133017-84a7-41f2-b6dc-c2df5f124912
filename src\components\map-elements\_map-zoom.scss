.ol-zoom {
    @include trs($prop: opacity);
    background-color: transparent;
    border-radius: 0;
    display: flex;
    left: unset;
    padding: 0;
    position: static;

    @include on-event() {
        background-color: transparent;
    }

    .is-popup-open & {
        @include breakpoint(small down) {
            display: none;
        }
    }
}

.ol-control button {
    @include size(45px);
    background-color: var(--color-1--1);
    border-radius: 0;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.16);
    color: var(--color-2--1);
    font-size: 2.6rem;
    font-weight: var(--fw-light);
    margin: 0 5px;
    padding: 0;

    @include on-event {
        background-color: var(--color-1--1);
        color: $color-white;
        cursor: pointer;
    }
}
