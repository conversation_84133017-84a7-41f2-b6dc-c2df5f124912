.hero {
    &.is-type-2 {
        background-color: $color-white;

        .hero-item {
            $this: '.hero-item';

            &__content {
                @include trs;
                @include absolute(null, 0, 14%, null);
                background-color: var(--color-1--1);
                border-radius: 30px 0 0 30px;
                display: inline-block;
                max-width: 338px;
                min-height: 103px;
                padding: 67px 40px 75px;
                text-decoration: none;

                @include breakpoint(medium down) {
                    bottom: 91px;
                    max-width: 318px;
                    padding: 31px 40px 70px 37px;
                    top: auto;
                    transform: matrix(1, -0.04, 0.03, 1, 8, 0);
                }

                @include breakpoint(small down) {
                    bottom: auto;
                    max-width: 244px;
                    padding: 31px 20px 42px 37px;
                    top: 0;
                }

                @include on-event {
                    &[href] {
                        #{$this}__title {
                            color: $color-white;
                            text-decoration: underline;
                        }

                        #{$this}__category {
                            color: $color-white;
                        }
                    }
                }

                &::before {
                    @include absolute(-8px, 0, null, -17px);
                    @include size(calc(100% + 19px), calc(100% + 11px));
                    background-color: var(--color-1--1);
                    border-radius: 30px 0 0 30px;
                    content: '';
                    transform: matrix(1, -0.03, 0.03, 1, 3, 0);
                    z-index: 0;
                }
            }

            &__title {
                @include trs;
                @include font(null, 2.4rem, var(--fw-bold));
                color: $color-white;
                line-height: 1.3;
                position: relative;
                z-index: 25;

                @include breakpoint(medium down) {
                    font-size: 2.2rem;
                }

                @include breakpoint(small down) {
                    font-size: 2rem;
                }
            }

            &__category {
                @include font(null, 1.4rem, var(--fw-bold));
                color: $color-white;
                margin-bottom: 10px;

                @include breakpoint(small down) {
                    margin-bottom: 5px;
                }
            }

            &__search {
                left: auto;
                right: 870px;
                top: 44%;
                transform: translateY(-50%);

                @media screen and (min-width: 1280px) and (max-width: 1700px) {
                    left: 76px;
                    right: auto;
                }

                @include breakpoint(medium down) {
                    bottom: -26px;
                    left: 109px;
                    top: auto;
                    transform: none;
                }

                @include breakpoint(small down) {
                    bottom: auto;
                    left: 0;
                    margin: -38px auto;
                    position: relative;
                }
            }

            &__media {
                @include breakpoint(large) {
                    min-height: 800px;
                }

                img {
                    @include breakpoint(medium down) {
                        height: 468px;
                    }

                    @include breakpoint(small down) {
                        height: 339px;
                    }
                }

                @at-root {
                    .has-video.is-type-2 {
                        #{$this}__media {
                            @include responsive-ratio(1920, 744, 'before');
                        }
                    }
                }
            }

            &__picture {
                &::after {
                    height: 219px;

                    @include breakpoint(medium down) {
                        height: 226px;
                    }

                    @include breakpoint(small down) {
                        height: 140px;
                    }
                }
            }

            &__video {
                &::after {
                    height: 300px;

                    @include breakpoint(medium down) {
                        height: 200px;
                    }

                    @include breakpoint(small down) {
                        height: 0;
                    }
                }
            }

            .video {
                &__controls-wrap {
                    @include breakpoint(medium down) {
                        transform: translate(-50%, 50%);
                    }
                }
            }
        }
    }
}
