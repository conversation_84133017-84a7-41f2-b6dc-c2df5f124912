{%- from 'views/utils/utils.njk' import svg -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/utils/constants.njk' import kMainNav -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/lang/lang.njk' import Lang -%}
{%- from 'components/search/search.njk' import SearchForm, SearchTags -%}


{% macro NavItem(item = {}, level = 0) %}
    <li class="main-nav__nav-item {{ 'has-dropdown js-main-nav-ddm' if item.dropdown }}">
        <span class="main-nav__nav-item-actions">
            {{ Icon('fas fa-caret-right') if level === 3 }}
            <a href="#" class="main-nav__nav-link {{ 'is-active' if item.current }}">{{ item.text }}</a>
            {% if item.dropdown %}
                {% set level = level + 1 %}
                <button type="button" class="main-nav__nav-toggle js-main-nav-ddm-toggle">
                    <span class="main-nav__nav-toggle-icon" aria-hidden="true"></span>
                    <span class="ghost">{{ item.text }}</span>
                </button>
            {% endif %}
        </span>
        {% if item.dropdown %}
            <ul class="main-nav__nav-dropdown is-level-{{ level }} js-main-nav-ddm-block">
                {% for subItem in item.dropdown %}
                    {{ NavItem(item = subItem, level = level) }}
                {% endfor %}
            </ul>
        {% endif %}
    </li>
{% endmacro %}

{%- macro MainNavAside(items = kMainNav, usePageImage) -%}
    {% set menuLinks = [
        ['Actualités', 'icons/newspaper-regular'],
        ['Agenda', 'icons/calendar-alt-regular'],
        ['Publications', 'icons/file-alt-regular'],
        ['Lettre d\'information', 'icons/paper-plane-regular'],
        ['Espace presse', 'icons/paperclip-regular'],
        ['Contact', 'icons/envelope-regular'],
        ['Extranet', 'icons/lock-alt-regular']
    ] %}
    <div class="main-nav js-main-nav">
        <div class="main-nav__container js-main-nav-container" aria-modal="true" role="dialog" aria-label="Navigation principale">
            <div class="main-nav__block js-main-nav-block">
                <div class="main-nav__close-wrap">
                    {%- call Button(
                        className = 'btn is-small is-only-icon is-inverted main-nav__close-button js-main-nav-close',
                        tooltip = 'Fermer le menu',
                        icon = 'far fa-times'
                    ) -%}
                        <span class="btn__text">Fermer</span>
                    {%- endcall -%}
                </div>
                <div class="main-nav__lang">
                    <!-- GTranslate will be inserted via JS if it's activated in header.njk and core.ts -->
                    {{ Lang() if not usePageImage }}
                </div>
                <div class="main-nav__left">
                    <div class="main-nav__search">
                        <div class="main-nav" role="search">
                            {{ SearchForm(
                                buttonClassName = 'main-nav__btn',
                                label = 'Que recherchez-vous ?',
                                labelModifier = 'ghost'
                            ) }}
                        </div>
                    </div>
                    <ul class="main-nav__nav-list">
                        {% for item in items %}
                            {{ NavItem(item = item) }}
                        {% endfor %}
                    </ul>
                </div>
                <div class="main-nav__right">
                    <ul class="main-nav__list">
                        {% for link, linkIcon in menuLinks %}
                            <li class="main-nav__item">
                                <a href="#" class="main-nav__link">
                                    {{ svg(linkIcon, 22, 22) }}
                                    <span class="main-nav__link-text">{{ link }}</span>
                                </a>
                            </li>
                        {% endfor %}
                        <li class="main-nav__item">
                            <a href="#" class="main-nav__link">
                                <span class="far fa-book-alt" aria-hidden="true"></span>
                                <span class="main-nav__link-text">Fontawesome</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="main-nav-overlay" id="js-main-nav-overlay"></div>
{%- endmacro -%}
