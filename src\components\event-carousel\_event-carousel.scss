.event-carousel {
    margin: 35px -12px 0;
    position: relative;

    .is-width-33.events-widget & {
        margin-top: 0;
    }

    @include breakpoint(medium down) {
        margin: 100px auto 0;
        max-width: 460px;
        padding-left: 40px;
        padding-right: 40px;

        .page-content &,
        .is-events-list & {
            margin-top: 100px;
        }

        .home-page & {
            padding: 0;
        }

        .is-width-33 & {
            margin-left: 0;
            width: fit-content;
        }
    }

    @include breakpoint(small down) {
        margin-top: 114px;
        max-width: 550px;
        padding-left: 30px;
        padding-right: 30px;
    }

    .swiper-container {

        @include breakpoint(medium down) {
            .single-event-page & {
                padding-bottom: 30px;
            }
        }

        @include breakpoint(small down) {
            padding-bottom: 0;

            .single-event-page & {
                padding-bottom: 0;
            }

            .page-content & {
                padding-bottom: 10px;
            }
        }

        .events-widget & {
            padding-bottom: 20px;
        }
    }

    &__container {
        overflow: visible;
        overflow-x: clip;
    }

    &__pagination {
        @include absolute(auto !important, null, 4px !important, 40px);
        display: flex;
        justify-content: center;
        margin-left: -30px !important;
        width: 100%;
        z-index: 2;

        .single-event-page & {
            @include breakpoint(medium down) {
                bottom: 14px !important;
            }
        }

        .swiper-pagination {
            &__bullet {
                @include size(10px);
                background-color: $color-white;
                border: 1px solid $color-3--4;
                border-radius: 5px;
                margin-right: 6px;

                &.is-active {
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    border-radius: 5px;
                    width: 26px;
                }
            }

            &__bullet-btn {
                @include size(10px);
                @include absolute();
                background-color: transparent;
                border: none;
                border-radius: 5px;
                outline: none;
                padding: 0;
            }
        }
    }

    &__control {
        @include absolute(50%);
        background: none;
        border: 0;
        cursor: pointer;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);
        z-index: 10;

        @include fa-icon-style(false) {
            color: $color-black;
            font-size: 4rem;
            font-weight: 700;
            font-weight: var(--fw-light);
        }

        @include breakpoint(medium down) {
            bottom: 0;
            top: auto;
        }

        &.is-prev {
            @include absolute(auto, null, -33px, 25%);

            @include breakpoint(medium down) {
                left: 35px;

                .single-event-page & {
                    bottom: -25px;
                }
            }

            @include breakpoint(small down) {
                left: 0;
            }
        }

        &.is-next {
            @include absolute(auto, 25%, -33px, null);

            @include breakpoint(medium down) {
                right: 35px;

                .single-event-page & {
                    bottom: -25px;
                }
            }

            @include breakpoint(small down) {
                right: 0;
            }
        }
    }

    &__wrapper {
        @include breakpoint(large only) {
            display: flex;
            flex-wrap: wrap;
        }

        .home-page & {
            @include breakpoint(large only) {
                flex-wrap: nowrap;
            }
        }
    }

    &__item {
        height: auto;
        padding: 0 12px;

        &.swiper-slide {
            @include breakpoint(large only) {
                width: calc(100% / 3) !important;
            }

            .single-event-page & {
                @include breakpoint(large only) {
                    margin-top: 0;
                    padding: 0 24px;
                    width: 25% !important;
                }
            }

            .home-page & {
                @include breakpoint(large only) {
                    margin-top: 0;
                    width: 416px !important;
                }

                .event-item {
                    &__image {
                        @include breakpoint(large only) {
                            @include size(235px, 210px);
                            margin-top: 100px;

                            img {
                                @include size(235px, 210px);
                            }
                        }
                    }

                    &__wrap {
                        @include breakpoint(large only) {
                            margin-top: 0;
                            width: 232px;
                        }
                    }

                    &__nbplaces {
                        @include breakpoint(large only) {
                            display: none;
                        }
                    }
                }
            }

            .is-width-33 & {
                width: 100% !important;
            }
        }

        &.swiper-slide-prev {
            .home-page & {
                margin-right: 52px;
                // margin-top: -65px;

                @include breakpoint(medium down) {
                    opacity: 0;
                }

                .event-item {
                    &__content {
                        @include breakpoint(medium down) {
                            margin-left: 0;
                        }

                        &::before {
                            content: none;
                        }

                        .event-badge {
                            display: none;
                        }
                    }
                }
            }
        }

        &.swiper-slide-active {
            .home-page & {
                @include breakpoint(large only) {
                    width: 432px !important;
                }

                @include breakpoint(medium down) {
                    margin-left: -50px;

                    &:first-of-type {
                        margin-left: 0;
                    }
                }

                @include breakpoint(small down) {
                    margin-left: 0;
                }

                .event-item {
                    max-width: 458px;
                    padding-top: 56px;
                    width: 458px;

                    @include breakpoint(small down) {
                        width: 100%;
                    }

                    &__image {
                        height: 432px;
                        margin-top: -7px;
                        width: 100%;

                        @include breakpoint(small down) {
                            @include size(234px);
                        }

                        img {
                            @include size(100%);
                        }
                    }

                    &__content {
                        width: 448px;

                        @include breakpoint(small down) {
                            padding-right: 17px;
                        }
                    }

                    &__wrap {
                        background-color: $color-white;
                        border-radius: 20px;
                        margin-top: -37px;
                        padding: 41px 69px 20px 56px;
                        width: 100%;

                        @include breakpoint(small down) {
                            margin-top: -33px;
                            padding: 40px 20px 20px 20px;
                        }
                    }

                    &__nbplaces {
                        display: block;
                        left: 50%;
                        margin: -61px auto 0;
                        position: absolute;
                        transform: translateX(-50%);

                        @include breakpoint(medium down) {
                            font-size: 1.2rem;
                            margin-top: -62px;
                            right: unset;
                            top: unset;
                        }

                        @include breakpoint(small down) {
                            @include size(155px, 34px);
                            font-size: 1rem;
                        }
                    }
                }
            }
        }

        .home-page & {
            &:not(.swiper-slide-active) {
                .event-item {
                    padding-top: 0;

                    &__date {
                        top: 30px;
                    }
                }
            }
        }

        :not(&.swiper-slide-active) {
            .event-item__wrap {
                @include breakpoint(small down) {
                    margin-left: 12px;
                }
            }
        }

        &.swiper-slide-next {
            .home-page & {
                margin-left: 50px;

                @include breakpoint(medium down) {
                    margin-left: 0;
                    opacity: 0;
                }

                .event-item {
                    &__content {
                        &::before {
                            top: 10px;
                        }
                    }
                }
            }
        }

        @include breakpoint(small down) {
            padding-left: 5px;
            padding-right: 5px;
        }
    }
}
