.contact-item {
    $this: &;

    &.is-structure {
        @include breakpoint(small down) {
            padding: 35px 10px;
        }

        #{$this}__theme {
            font-weight: var(--fw-normal);
            letter-spacing: 3.24px;
            margin-bottom: 13px;
        }

        #{$this}__picture {
            border-radius: 10px;

            img {
                border-radius: 10px;
            }

            @include breakpoint(medium down) {
                margin-right: 30px;
                max-width: 200px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 20px;
            }
        }

        #{$this}__content-top {
            @include breakpoint(small down) {
                padding: 0 30px;
            }
        }

        #{$this}__content-info {
            @include breakpoint(small down) {
                margin-top: 10px;
                padding: 0 30px;
            }
        }

        #{$this}__infos-item {
            .btn {
                border-radius: 40px;
                font-weight: var(--fw-normal);
            }
        }

        #{$this}__details {
            text-align: left;

            .info-item {
                &.is-location {
                    display: flex;
                    padding-left: 0;

                    a {
                        svg {
                            @include size(27px);
                        }
                    }
                }
            }
            
            .infos:nth-child(2) {
                margin-top: 30px;

                @include breakpoint(small down) {
                    align-items: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    margin-top: 20px;
                }
            }

            .infos {
                &__item {
                    .far {
                        font-size: 2rem;
                    }
                }
            }
        }

        .is-width-66 & {
            @include breakpoint(large) {
                #{$this}__picture {
                    margin-right: 30px;
                    max-width: 200px;
                }

                #{$this}__infos {
                    flex-direction: row;
                    flex-wrap: wrap;
                    margin: 0 -2.5px;
                    width: 400px;
                }

                #{$this}__theme {
                    font-size: 1.3rem;
                }

                #{$this}__infos-item {
                    margin: 0 2.5px 5px;
                }
            }
        }

        .is-width-33 & {
            @include breakpoint(large) {
                #{$this}__picture {
                    margin: 0 auto 20px;
                    max-width: 200px;
                }

                #{$this}__content-info {
                    margin-top: 10px;
                    padding: 0 30px;
                }

                #{$this}__details {
                    .info-item {
                        margin: 0;

                        p {
                            margin-bottom: 5px;
                        }
                    }

                    .infos:nth-child(2) {
                        align-items: center;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        margin-top: 20px;
                    }
                }
            }
        }
    }
}
