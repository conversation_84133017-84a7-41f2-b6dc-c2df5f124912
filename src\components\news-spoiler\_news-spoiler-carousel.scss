.news-spoiler-carousel {
    &.has-pagination {
        padding-bottom: 25px;

        @include breakpoint(small down) {
            padding-bottom: 50px;
        }
    }

    .swiper-container {
        position: static;
    }

    &__container {
        margin: 0 auto;
        width: calc(100% - 90px);

        @include breakpoint(medium down) {
            width: calc(100% - 47px);
        }

        @include breakpoint(small down) {
            align-items: center;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            width: 100%;
        }
    }

    &__wrapper {
        @include breakpoint(small down) {
            margin: 0 0 15px;
            order: -1;
        }
    }

    &__actions {
        @include absolute(9px, 0);
        border-left: 1px solid $color-3--3;
        display: inline-flex;
        margin: 0;
        min-width: 75px;
        padding: 2px 15px 2px;
        z-index: 10;

        @include breakpoint(small down) {
            left: 50%;
            min-width: 25px;
            right: auto;
            top: 23px;
            transform: translateX(5px);
        }
    }

    &__action {
        @include trs(opacity);
        background: none;
        border: 0;
        color: $color-3--3;
        cursor: pointer;
        display: inline-block;
        font-size: 0.9rem;
        padding: 0 3px;

        &.is-active {
            color: var(--color-2--1);
            display: none;
        }

        @include on-event {
            opacity: 0.7;
        }
    }

    &__pagination {
        @include absolute(9px, 83px);
        align-items: center;
        display: inline-flex;
        list-style: none;
        margin: 0;
        padding: 2px 0 17px;
        width: auto;
        z-index: 10;

        @include breakpoint(small down) {
            right: 50%;
            top: 23px;
            transform: translateX(-5px);
        }

        .swiper-pagination__bullet {
            @include trs(opacity);
            display: block;
            padding: 4px;

            &.is-active {
                .swiper-pagination__bullet-btn {
                    background-color: var(--color-2--1);
                    border-color: var(--color-2--1);
                    transform: scale(1.5);
                }
            }

            &:not(.is-active) {
                @include on-event {
                    opacity: 0.7;
                }
            }
        }

        .swiper-pagination__bullet-btn {
            @include trs;
            @include size(8px);
            background-color: transparent;
            border: 1px solid $color-3--3;
            border-radius: 50%;
            cursor: pointer;
            display: block;
            padding: 0;
        }
    }

    &__control {
        @include absolute(50%);
        @include size(50px);
        background: none;
        border: none;
        color: $color-3--4;
        cursor: pointer;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);
        z-index: 2;

        @include breakpoint(small down) {
            @include size(46px);
        }

        @include fa-icon-style(false) {
            font-size: 2.5rem;
            font-weight: 700;
            vertical-align: -2px;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        &.is-prev {
            left: -15px;

            @include breakpoint(medium down) {
                left: 0;
            }

            @include breakpoint(small down) {
                left: -8px;
            }
        }

        &.is-next {
            right: -15px;

            @include breakpoint(medium down) {
                right: 0;
            }

            @include breakpoint(small down) {
                right: -8px;
            }
        }

        &.swiper-button-disabled {
            opacity: 0.3;
        }
    }
}
