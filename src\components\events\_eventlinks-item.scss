.eventlinks-item {
    @include size(100%);
    @include line-decor();
    @extend %link-block-context;
    align-items: center;
    background-color: $color-white;
    box-shadow: 0 0 50px rgba($color-black, 0.16);
    color: $color-white;
    display: flex;
    padding: 20px 60px 20px 25px;

    &::before {
        @include absolute(null, null, 0, 98px);
        transform: translateY(50%);
        z-index: 10;

        @include breakpoint(medium down) {
            left: 70px;
        }
    }

    &__svg-wrapper {
        @include size(60px);
        flex-shrink: 0;
        margin-right: 10px;

        @include breakpoint(medium down) {
            @include size(35px);
        }

        svg {
            @include size(100%);
            fill: var(--color-1--1);
        }
    }

    &__link {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        @extend %link-block;
        @extend %underline-context;
        color: var(--color-1--2);
        line-height: 1.1;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        &:focus {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__icon {
        @include absolute(50%, 30px);
        @include font(null, 2rem, var(--fw-normal));
        color: $color-black;
        transform: translateY(-50%);
    }
}
