{% from 'views/utils/utils.njk' import svg %}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{% set defaultSettings = {
    modifier: '',
    text: lorem(5),
    imageSizes: ['250x250']
} %}

{#
    IndicatorItem template.
    @param {object} settings - indicator item settings.
#}
{%- macro IndicatorItem(settings = {}) -%}
    {% set params = Helpers.merge(defaultSettings, settings) %}

    <article class="indicator-item {{ params.modifier }}">
        <div class="indicator-item__wrapper js-doughnut-chart">
            <div class="indicator-item__chart">
                <div class="indicator-item__canvas">
                    <canvas aria-hidden="true"></canvas>
                </div>
            </div>
            <div class="indicator-item__block">
                <div class="indicator-item__chart-data">
                    <ul data-chart-data data-chart-hide-quantity class="chart-data">
                        {% for item in range(6) %}
                            {% set percent = range(5, 50) | random %}
                            <li class="chart-data__item js-chart-item js-chart-label" data-index="{{ loop.index0 }}" data-value="{{ percent }}">
                                <span class="chart-data__colorbox js-chart-data-colorbox"></span>
                                <strong>{{ percent }}%</strong><span class= "chart-data__legend"> {{ lorem(range(5) | random, 'words') | capitalize }}</span>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="indicator-item__description">
                    <h3>Description chiffre clé lorem ipsum dolor</h3>
                    <p>{{ params.text }}</p>
                </div>
            </div>
        </div>
    </article>
{%- endmacro -%}

{#
    IndicatorsContent template.
    @param {string} titleText - section title text.
    @param {number} itemsCount - count of indicator items
    @param {string} modifier - section modifier
#}
{%- macro IndicatorsContent(
    titleText = 'Chiffres-clés',
    itemsCount = 1,
    modifier = ''
) -%}
    {% call Section(className = 'indicators ' + modifier, container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ IndicatorItem({
                modifier: 'is-column' if itemsCount > 1 else ''
            }) }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    IndicatorsContentColumns template.
     @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsContentColumns(
    itemsCount = 2,
    columnModifier = '',
    contentModifier = 'is-column'
) -%}
    <div class="indicators-columns {{ columnModifier }}">
        {% for item in range(itemsCount) %}
            {{ IndicatorsContent(
                titleText = 'Titre du graphique',
                itemsCount = itemsCount,
                modifier = contentModifier
            ) }}
        {% endfor %}
    </div>
{%- endmacro -%}

{#
    IndicatorsContentBlocks template.
    @param {string} titleText - section title text.
    @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsContentBlocks(
    titleText = 'Chiffres-clés quatre blocs',
    itemsCount = 4
) -%}
    {% call Section(className = 'indicators', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {% call List(
                count = itemsCount,
                cols = 4,
                mdCols = 2,
                smCols = 2,
                xsCols = 1
            ) %}
                {{ IndicatorItem({
                    modifier: 'is-box',
                    text: lorem(15, 'words') | capitalize,
                    imageSizes: ['480x321?479', '767x513?767', '588x403?1279', '283x194']
                }) }}
            {% endcall %}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    IndicatorsSG template
    Styleguide template.
    @param {string} sectionId - section id.
    @param {string} title - section title.
    @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsSG(sectionId = 'indicators', title = 'Chiffres-clés', itemsCount = 1) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            {{ IndicatorItem({
                modifier: 'is-column' if itemsCount > 1 else ''
            }) }}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    IndicatorsColumnsSG template
    Styleguide template.
    @param {string} sectionId - section id.
    @param {string} title - section title.
    @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsColumnsSG(sectionId = 'indicators-columns', title = 'Chiffres-clés 2 col', itemsCount = 2) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            <div class="indicators-columns">
                {% for item in range(itemsCount) %}
                    <section class="indicators is-column">
                        {{ IndicatorItem({
                            modifier: 'is-column' if itemsCount > 1 else ''
                        }) }}
                    </section>
                {% endfor %}
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{%- macro IndicatorsOneColumnSG(sectionId = 'indicators-one-column', title = 'Chiffres-clés 1 col', itemsCount = 1) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-sm-12 col-md-8">
                    <div class="rte">
                        <p>
                            {{ lorem(5) }}
                        </p>
                        <p>
                            {{ lorem(16) }}
                        </p>
                        <p>
                            {{ lorem(9) }}
                        </p>
                    </div>
                </div>
                <div class="col-sm-12 col-md-4">
                    <div class="indicators-columns is-width-33">
                        {% for item in range(itemsCount) %}
                            <section class="indicators">
                                {{ IndicatorItem() }}
                            </section>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {% endcall %}
{%- endmacro -%}

{#
    IndicatorsOneColumnSG template
    Styleguide template.
    @param {string} sectionId - section id.
    @param {string} title - section title.
    @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsOneColumnSG(sectionId = 'indicators-one-column', title = 'Chiffres-clés 1 col', itemsCount = 1) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-sm-12 col-lg-8">
                    <div class="rte">
                        <p>
                            {{ lorem(5) }}
                        </p>
                        <p>
                            {{ lorem(16) }}
                        </p>
                        <p>
                            {{ lorem(9) }}
                        </p>
                    </div>
                </div>
                <div class="col-sm-12 col-lg-4">
                    <div class="indicators-columns is-width-33">
                        {% for item in range(itemsCount) %}
                            <section class="indicators">
                                {{ IndicatorItem() }}
                            </section>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {% endcall %}
{%- endmacro -%}

{#
    IndicatorsBlocksSG template
    Styleguide template.
    @param {string} sectionId - section id.
    @param {string} title - section title.
    @param {number} itemsCount - count of indicator items
#}
{%- macro IndicatorsBlocksSG(sectionId = 'indicators-box', title = 'Chiffres-clés quatre blocs', itemsCount = 4) -%}
    {% call SG.Section(sectionId) %}
        <h2 class="styleguide-section__title">{{ title }}</h2>
        {%- call SG.Preview() -%}
            {% call List(
                count = itemsCount,
                cols = 3,
                mdCols = 2,
                smCols = 2,
                xsCols = 1
            ) %}
                {{ IndicatorItem({
                    modifier: 'is-box',
                    text: lorem(15, 'words') | capitalize,
                    imageSizes: ['480x321?479', '767x513?767', '588x403?1279', '283x194']
                }) }}
            {% endcall %}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

