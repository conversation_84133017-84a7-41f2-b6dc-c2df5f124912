.location-home {
    margin-bottom: 165px;
    position: relative;

    @include breakpoint(medium down) {
        margin-bottom: 97px;
        margin-top: 65px;
        padding: 0;
    }

    @include breakpoint(small down) {
        .home-page & {
            margin-bottom: 30px;
            margin-top: 55px;
        }
    }

    &__container {
        @extend %container;
        position: relative;

        &::before {
            @include size(calc(100vw - 283px), 610px);
            @include absolute(68px, null, null, 11%);
            background-color: var(--color-1--5);
            border-radius: 0 20px 20px 0;
            content: '';
            transform: matrix(-1, -0.02, 0.02, -1, 0, 0);

            .is-news & {
                content: none;
            }

            @include breakpoint(medium only) {
                @include size(calc(100vw - 26px), 543px);
                left: 4.5%;
                top: 180px;
            }

            @include breakpoint(small down) {
                @include size(calc(100vw - 6px), 336px);
                left: 20px;
            }
        }
    }

    &.home-feed {
        .section__title {
            @include breakpoint(medium down) {
                margin-bottom: 134px;
            }

            @include breakpoint(small down) {
                margin-bottom: 50px;
            }
        }
    }

    .section__title {
        margin-bottom: -25px;

        @include breakpoint(medium down) {
            margin-bottom: 60px;
        }

        @include breakpoint(small down) {
            margin-bottom: 30px;
        }
    }
}
