.heading-filter-toggle {
    @extend %button;
    @extend %button-size-small;
    @extend %button-style-only-icon;
    @extend %button-style-circle;
    @extend %button-style-secondary;

    @include breakpoint(medium) {
        display: none;
    }

    &.is-md-breakpoint {
        @include breakpoint(medium down) {
            display: block;
        }
    }

    &.is-lg-breakpoint {
        display: block;
    }
}

.heading {
    $this: &;

    @include breakpoint(large only) {
        position: relative;
    }

    &.with-image {
        @include breakpoint(large only) {
            min-height: 600px;
        }
    }

    &.is-center {
        #{$this}__category,
        #{$this}__title,
        #{$this}__teaser,
        #{$this}__name,
        #{$this}__synonyms,
        #{$this}__publication,
        #{$this}__type,
        #{$this}__function {
            text-align: center;
        }

        #{$this}__links,
        #{$this}__title {
            justify-content: center;
            width: 100%;
        }
    }

    .is-single-contact & {
        &__content-bottom {
            &.is-contacts {
                .flex-row {
                    >div {
                        @include breakpoint(small down) {
                            align-items: flex-start;
                        }
                    }
                }
            }
        }
    }

    &.has-image-left {
        position: relative;

        #{$this}__image {
            border-radius: 30px;
            max-width: 250px;
            order: -1;
            position: relative;

            @include breakpoint(medium down) {
                margin-bottom: 25px;
            }

            @include breakpoint(small down) {
                max-width: 100%;
            }

            &::before {
                @include absolute(-16px, null, null, 5%);
                @include size(206px, 200px);
                background-color: var(--color-2--1);
                border-radius: 30px;
                clip-path: polygon(0 0, 98% 7%, 100% 75%, 0 19%);
                content: '';
                z-index: 0;

                @include breakpoint(small down) {
                    top: 20px;
                }

                .is-single-presse & {
                    top: 4px;
                }
            }
        }

        #{$this}__title-text {
            &::before {
                left: -384px;
                width: 510px;

                @include breakpoint(small down) {
                    left: -427px;
                }

                .is-single-contact &,
                .is-single-presse &,
                .is-single-elected &,
                .is-single-reviews & {
                    content: none;
                }
            }
        }

        #{$this}__content {
            @include breakpoint(medium only) {
                width: 1%;

                .is-single-presse & {
                    width: 100%;
                }
            }
        }
    }

    &__reservation {
        .btn {
            margin-bottom: 10px;
        }
    }

    .is-single-presse & {
        &__wrapper {
            @include breakpoint(medium down) {
                flex-direction: column-reverse;
            }
        }
    }

    &__wrapper {
        @extend %container;
        background-color: $color-white;
        display: flex;
        flex-wrap: wrap;
        padding: 48px 40px 10px;

        .single-event-page & {
            padding-bottom: 110px;

            @include breakpoint(medium down) {
                padding-bottom: 40px;
            }

            @include breakpoint(small down) {
                padding-bottom: 0;
            }
        }

        @include breakpoint(medium down) {
            justify-content: flex-end;
            padding: 18px 62px 40px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 20px 20px 40px;
        }
    }

    &__image {
        margin: 0;
        max-width: 126px;
        z-index: 1;

        @include breakpoint(medium down) {
            margin: 0 34px 0 0;
        }

        @include breakpoint(small down) {
            margin: 0 0 25px;
            max-width: 100%;
        }

        &.is-right {
            position: relative;

            @include breakpoint(large only) {
                @include absolute(20px, -25px, null, null);
                max-width: 50%;
            }

            .page-content & {
                @media screen and (min-width: 1280px) and (max-width: 1540px) {
                    max-width: 706px;
                }
            }

            @include breakpoint(medium down) {
                margin: 76px -71px 0 0;
                max-width: 100%;
            }

            @include breakpoint(small down) {
                margin: 46px -27px 0 auto;
                max-width: 340px;
            }

            .single-event-page & {
                @include breakpoint(small down) {
                    margin: 110px -27px 0 auto;
                }

                &::before {
                    top: -10px;
                    width: 542px;

                    @include breakpoint(medium down) {
                        top: -11px;
                        width: 409px;
                    }
                }
            }

            &.is-travaux {
                @include breakpoint(small down) {
                    margin: 20px -27px 0 auto;
                }
            }

            .single-guide &,
            .single-event-page & {
                @include breakpoint(large only) {
                    max-width: 584px;
                    right: 0;
                    top: 16px;

                    .heading__caption {
                        padding-right: 0;
                    }
                }
            }

            &::before {
                @include absolute(-10px, 0, null, null);
                @include size(calc(100% - 42px), 230px);
                background-color: var(--color-2--1);
                border-radius: 30px;
                content: '';
                z-index: 0;

                @include breakpoint(small down) {
                    content: none;
                }

                .single-glossary & {
                    @include breakpoint(small down) {
                        top: 8px;
                        width: 221px;
                    }
                }

                .single-guide &,
                .single-event-page & {
                    top: -7px;
                    width: calc(100% - 27px);
                }
            }

            img {
                border-radius: 30px 0 0 30px;
            }
        }

        &.is-small {
            @include breakpoint(large) {
                max-width: 272px;
            }
        }

        picture {
            display: block;
            transform: matrix(1, -0.03, 0.03, 1, 0, 0);

            &.is-rounded {
                border-radius: 50%;
                overflow: hidden;
            }

            img {
                border-radius: 30px;
            }
        }

        +.heading__content {
            width: 1%;

            @include breakpoint(small down) {
                width: 100%;
            }

            .heading__title-text::before,
            .heading__name-text::before {
                @include breakpoint(large only) {
                    left: -384px;
                    width: 510px;
                }
            }
        }

        #{$this}__status {
            >* {
                display: block;
                margin: 0 0 10px;
            }
        }
    }

    &__image-link {
        @include focus-outline($color: var(--color-1--1), $offset: 2px);
        color: $color-black;
        display: block;
        text-align: center;
        width: 100%;

        @include on-event {
            picture {
                //
            }
        }
    }

    &__date {
        margin: 12px 0;
    }

    &__figure {
        position: relative;
    }

    &__caption {
        @include absolute(100%, 50%, null);
        @include font(var(--typo-1), 1.4rem, var(--fw-normal), normal);
        color: $color-3--5;
        line-height: 1.35;
        margin-left: 15px;
        margin-top: 18px;
        max-width: 55%;

        @include breakpoint(medium down) {
            max-width: 80%;
            right: 20%;
        }

        .single-event-page &,
        .single-guide & {
            @include breakpoint(large only) {
                max-width: 323px;
            }
        }

        &-text {
            display: -webkit-box;
            margin-left: auto;
            width: fit-content;
        }

        &::before {
            @include absolute(-29px, -15px, null, null);
            @include size(2px, 72px);
            background-color: var(--color-1--1);
            content: "";
            display: block;
        }

        .page-content & {
            width: 480px;

            @include breakpoint(small down) {
                width: 270px;
            }
        }
    }

    &__label {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: $color-3--5;

        @include breakpoint(medium down) {
            font-size: 1.5rem;
        }

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }
    }

    &__item {
        &.informations {
            border-top: 1px solid $color-3--3;
            display: flex;
            gap: 0 20px;
            justify-content: space-between;
            padding-top: 20px;
            width: 100%;

            @include breakpoint(small down) {
                flex-direction: column;
                gap: 7px 0;
            }

            * {
                text-decoration: none;
            }
        }

        .heading__events-group.is-travaux & {
            margin: 0 0 5px 0;
            position: relative;

            @include fa-icon-style(false) {
                @include absolute(3px, null, null, -25px);
                color: var(--color-1--1);
                font-size: 2rem;
            }
        }
    }

    &__popups {
        a {
            width: 100%;
        }
    }

    &__Value {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: $color-3--5;
        padding-bottom: 10px;

        @include breakpoint(medium down) {
            font-size: 1.5rem;
        }

        @include breakpoint(small down) {
            display: block;
            font-size: 1.4rem;
            padding-bottom: 0;
        }

        &.informations {
            border-top: 1px solid $color-1--3;
            display: flex;
            justify-content: space-between;
        }

        &.is-event {
            align-items: center;
            display: flex;

            b {
                margin-left: 5px;
            }
        }

        &.is-travaux {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            position: relative;

            span[class*=fa-] {
                color: var(--color-1--1);
                left: -20px;
                position: absolute;
            }
        }

        &.is-tarif {
            align-items: center;
            display: flex;
        }

        &.is-remaining-places {
            display: flex;

            svg {
                margin-right: 5px;
                width: 27px;

                @include breakpoint(small down) {
                    margin-right: 18px;
                    width: 34px;
                }
            }
        }

        svg {
            @include size(24px);
            fill: var(--color-1--1);
            margin-right: 11px;

            @include breakpoint(small down) {
                @include size(30px);
                margin-right: 22px;
            }
        }
    }

    &__value-list {
        display: flex;
        flex-direction: column;
        gap: 8px 0;
    }

    &__value-item {
        @include icon-before($fa-var-long-arrow-right);
        padding-left: 25px;
        position: relative;

        &::before {
            @include absolute(2px, null, null, 0);
            color: var(--color-1--1);
            font-size: 1.6rem;
            font-weight: var(--fw-normal);
            width: 15px;

            @include breakpoint(medium down) {
                font-size: 1.4rem;
                top: 1px;
            }
        }
    }

    &__value-link {
        text-decoration: underline;

        @include on-event {
            text-decoration: none;
        }
    }

    .is-single-deliberation & {
        &__date-deliberations {
            color: $color-3--4;
            font-size: 1.2rem;
            text-transform: uppercase;
        }

        &__publication {
            margin-bottom: 20px;
        }

        &__deliberations-title {
            font-weight: var(--fw-bold);
        }
    }

    &__content {
        align-content: flex-start;
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        flex-grow: 2;
        flex-wrap: nowrap;
        margin-top: 35px;
        min-height: 160px;
        position: relative;
        width: 1%;
        z-index: 1;

        &.is-travaux {
            margin-bottom: 0;
        }

        .page-list & {
            flex-direction: row;
        }

        @include breakpoint(medium only) {
            margin-bottom: 0;
            margin-top: 31px;
        }

        @include breakpoint(small down) {
            margin-bottom: 0;
            margin-top: 0;
            min-height: auto;
        }

        .list-events & {
            align-items: center;
            gap: 14px;
            justify-content: center;
        }

        @include breakpoint(medium down) {
            width: 100%;
        }

        >*:first-child {
            margin-top: 0;
        }

        >*:not(#{$this}__title):not(#{$this}__links) {
            width: 100%;
            z-index: 1;
        }

        &.is-full-width {
            width: 100%;
        }

        .single-guide &,
        .single-event-page & {
            @include breakpoint(large only) {
                margin-top: 16px;
                width: 100%;
            }

            @include breakpoint(medium only) {
                margin-top: 31px;
            }

            @include breakpoint(small down) {
                margin-top: 0;
            }
        }

        .with-image & {
            @include breakpoint(large only) {
                flex-direction: column;
                max-width: 580px;
            }
        
            @include breakpoint(medium down) {
                flex-direction: column;
                margin-top: 31px;
                max-width: 100%;
            }
        
            @include breakpoint(small down) {
                margin-top: 0;
            }

            .page-content & {
                max-width: 601px;

                @media screen and (min-width: 1280px) and (max-width: 1525px) {
                    max-width: 538px;
                }

                @include breakpoint(medium down) {
                    margin-top: 31px;
                    max-width: 100%;
                    min-height: auto;
                }
            
                @include breakpoint(small down) {
                    margin-top: 0;
                }
            }
        }

        .page-list & {
            flex-wrap: nowrap;

            @include breakpoint(medium down) {
                flex-wrap: wrap;
            }

            @include breakpoint(small down) {
                flex-direction: column;
                flex-wrap: nowrap;
            }
        }
    }

    &__commune {
        @include font(var(--typo-1), 2.4rem, var(--fw-normal));
        color: $color-3--4;
        z-index: 2;
    }

    &__title-content {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        color: var(--color-1--1);
        margin-top: 72px;
    }

    &__content-commune {
        background-color: $color-3--1;
        margin-bottom: 50px;
        margin-top: 28px;
        padding: 65px 75px;
        width: 100%;

        @include breakpoint(medium down) {
            padding: 40px 55px 25px;
        }

        @include breakpoint(small down) {
            padding: 25px;
        }
    }

    &__content-top {
        margin-bottom: 30px;
        width: 100%;

        .single-event-page & {
            margin-bottom: 0;

            .heading-informations {
                padding-right: 24px;
            }
        }

        .is-single-news & {
            margin-bottom: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }

    &__content-bottom {
        width: 100%;

        &.is-title {
            margin-top: 20px;

            .rte {
                &:not(.infowidget > &):not(.title) {
                    margin-bottom: 0;
                }
            }
        }

        &.is-elected,
        &.is-works-info,
        &.is-structures,
        &.is-contacts {
            background-color: $color-3--1;
            margin-top: 70px;
            padding: 65px 75px;
            width: 100%;

            @include breakpoint(large only) {
                width: calc(100% + 160px);
            }

            @include breakpoint(medium down) {
                padding: 40px 55px 25px;
            }

            @include breakpoint(small down) {
                padding: 25px;
            }

            .flex-row {
                >div {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;

                    @include breakpoint(small down) {
                        align-items: center;
                    }

                    &:nth-child(1) {
                        @include breakpoint(medium down) {
                            margin: 0 0 40px;
                        }
                    }
                }

                .col-lg-8.col-md-12 {
                    position: relative;

                    &::before {
                        @include absolute(null, 30px, 0, null);
                        @include size(1px, 100%);
                        background-color: $color-3--3;
                        content: "";
                        display: block;

                        @include breakpoint(medium down) {
                            @include absolute(null, 0, 0, 0);
                            @include size(100%, 1px);
                        }
                    }
                }
            }

            .list-contact {
                margin: -2.5px;
                margin-bottom: 10px;
                width: 100%;

                @include breakpoint(medium down) {
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 15px;
                }

                @include breakpoint(small down) {
                    align-items: center;
                    flex-direction: column;
                }

                &__item {
                    padding: 2.5px;
                    width: 100%;
                }

                .btn {
                    width: 220px;
                }
            }
        }

        &.is-contacts,
        &.is-elected {
            .flex-row {
                >div {
                    &:nth-child(2),
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 407px;
                        }
                    }
                }
            }
        }

        &.is-works-info {
            .flex-row {
                >div {
                    &:nth-child(1) {
                        margin-bottom: 0;

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(2) {
                        @include breakpoint(medium only) {
                            max-width: 250px;
                        }

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 355px;
                        }
                    }

                    .list-contact {
                        @include breakpoint(medium down) {
                            margin-top: 10px;
                        }
                    }
                }
            }
        }

        &.is-structures {
            .flex-row {
                @include breakpoint(medium only) {
                    justify-content: space-between;
                }

                >div {
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            align-items: center;
                            max-width: 250px;
                        }
                    }
                }
            }
        }

        .is-commune-page & {
            margin-left: 0;
            margin-right: 0;

            .list-contact {
                &__item {
                    padding: 2.5px;
                    width: 100%;

                    .btn {
                        width: 220px;
                    }
                }
            }
        }

        .with-image & {
            .single-guide & {
                @include breakpoint(large only) {
                    margin-top: -9px;
                }
            }
        }

        &.is-guide {
            background-color: $color-3--1;
            padding: 28px 40px 40px;

            @include breakpoint(medium down) {
                margin-top: 20px;
            }

            @include breakpoint(small down) {
                margin-top: 36px;
                padding: 30px 30px 25px;
            }

            .list-contact {
                .btn {
                    max-width: 209px;
                    width: 100%;

                    @include breakpoint(small down) {
                        max-width: 260px;
                    }
                }
            }

            .btn {
                font-weight: var(--fw-bold);
                margin-bottom: 7px;

                @include fa-icon-style(false) {
                    &::before {
                        font-size: 1.6rem;
                    }
                }

                &.is-guide {
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    color: $color-white;
                    margin-bottom: 17px;
                    min-height: 44px;
                    padding: 9px 26px;

                    @include on-event() {
                        background-color: var(--color-2--1);
                        border-color: var(--color-2--1);
                    }

                    @include breakpoint(small down) {
                        max-width: 260px;
                        width: 100%;
                    }
                }
            }

            .info-item.is-web-link {
                margin: 20px 0;

                @include breakpoint(small down) {
                    margin-left: auto;
                    margin-right: auto;
                    max-width: fit-content;
                    min-height: auto;
                    padding-left: 30px;
                }

                @include fa-icon-style(false) {
                    &::before {
                        font-size: 2.9rem;

                        @include breakpoint(small down) {
                            font-size: 2rem;
                            width: 20px;
                        }
                    }
                }
            }

            .infos {
                &__item {
                    margin: 10px 0;

                    @include breakpoint(small down) {
                        align-items: flex-start;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        margin: 10px 0;
                        min-height: 31px;
                        padding-left: 60px;
                    }
                }
            }

            .date-guides {
                font-size: 1.4rem;
                white-space: pre-wrap;
            }

            .info-item {
                @include fa-icon-style(false) {
                    &::before {
                        @include breakpoint(small down) {
                            display: flex;
                            font-size: 3rem;
                            justify-content: center;
                            width: 38px;
                        }
                    }
                }

                .title-item {
                    &.is-link {
                        font-size: 2.2rem;
                        font-weight: var(--fw-normal);

                        @include breakpoint(small down) {
                            font-size: 1.6rem;
                        }
                    }
                }
            }
        }

        &.is-structures,
        &.is-contacts {
            margin-top: 20px;
        }
    }

    &__content-buttons {
        width: 100%;
    }

    &__content-btn {
        margin: 25px 5px 10px 0;
    }

    &__category {
        @include font(var(--typo-1), 18px, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 3.24px;
        line-height: 1.2;
        margin: 0 0 9px;
        position: relative;
        text-transform: uppercase;
        z-index: 2;

        @include breakpoint(small down) {
            font-size: 14px;
            margin: 0 0 5px;
        }
    }

    &__part-left {
        height: 100%;
        position: relative;

        @include breakpoint(large only) {
            max-width: 757px;
        }

        @include breakpoint(medium down) {
            padding-bottom: 20px;
        }

        &::before {
            @include absolute(null, -64px, 0, null);
            @include size(1px, 100%);
            background-color: $color-3--3;
            content: "";
            display: block;

            @include breakpoint(medium down) {
                @include absolute(null, 0, 0, 0);
                @include size(100%, 1px);
            }
        }
    }

    &__part-right {
        margin-top: 72px;
        padding-left: 17px;

        @include breakpoint(medium down) {
            margin-top: 30px;
            padding-left: 0;
        }
    }

    &__title,
    &__name {
        @include font(null, 55px, var(--fw-bold));
        align-self: center;
        color: $color-black;
        display: flex;
        flex-direction: column;
        flex-grow: 2;
        line-height: 1;
        margin: 0 0 5px;

        @include breakpoint(small down) {
            font-size: 38px;
            line-height: 1.2;
            word-break: break-word;
        }

        +#{$this}__publication {
            margin-bottom: 15px;
            margin-top: 0;
        }
    }

    &__title-text,
    &__name-text {
        display: block;
    }

    &__title {
        position: relative;

        @include title-decor($style: primary);

        &:not(:has(#{$this}__category)) {
            #{$this}__title-text {
                margin-top: 20px;

                @include breakpoint(small down) {
                    margin-top: 8px;
                }
            }
        }

        &::before {
            z-index: -1;

            @include breakpoint(small down) {
                @include size(125px, 179px);
            }

            .single-guide &,
            .single-event-page & {
                @include breakpoint(large only) {
                    top: -75px;
                }
            }
        }

        @include breakpoint(large only) {
            line-height: 1;
            width: 100%;
            z-index: 1;

            // .page-list & {
            //     width: auto;
            // }
        }

        .single-guide & {
            @include breakpoint(large only) {
                line-height: 1.3;
            }
        }

        .is-single-news & {
            @include breakpoint(large only) {
                max-width: 100%;
            }
        }
    }

    &__subtitle {
        @include font(null, 3.5rem, var(--fw-normal));
        position: relative;
        z-index: 1;

        @include breakpoint(medium down) {
            font-size: 25px;
        }

        @include breakpoint(small down) {
            font-size: 22px;
        }
    }

    &__events-group {
        align-items: flex-start;
        background-color: $color-3--1;
        border-radius: 10px;
        display: flex;
        margin-top: -18px;
        padding: 92px 78px 60px 40px;
        position: relative;
        width: 100%;

        @include breakpoint(medium down) {
            flex-direction: column;
            margin-top: 64px;
        }

        @include breakpoint(small down) {
            margin-top: 95px;
            padding: 30px 20px;
        }

        .with-image & {
            @include breakpoint(small down) {
                margin-top: 12px;
            }
        }

        &.is-travaux {
            @include breakpoint(large only) {
                padding: 55px 70px 20px 66px;
            }
        }

        #{$this}__publication {
            margin: 0 0 25px 0;
            padding: 0;

            @include breakpoint(small down) {
                margin-bottom: 30px;
                text-align: center;
            }

            &::before {
                content: none;
            }
        }

        #{$this}__item {
            margin: 0 0 17px 0;

            &.is-travaux {
                margin: 0 0 5px 0;
                position: relative;

                @include fa-icon-style(false) {
                    @include absolute(4px, null, null, -25px);
                    color: var(--color-1--1);
                    font-size: 2rem;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        #{$this}__time-place {
            margin: 0 0 17px 0;

            @include breakpoint(small down) {
                margin-bottom: 27px;
            }
        }

        .time-place {
            &__item {
                color: $color-3--5;
                font-size: 1.8rem;

                @include breakpoint(medium down) {
                    font-size: 1.5rem;
                }

                @include breakpoint(small down) {
                    font-size: 1.4rem;
                }

                &.is-time {
                    @include breakpoint(small down) {
                        align-items: center;
                    }

                    @include fa-icon-style(false) {
                        margin-right: 11px;

                        @include breakpoint(small down) {
                            font-size: 3rem;
                            margin-right: 22px;
                        }
                    }

                    time {
                        font-weight: var(--fw-bold);
                    }
                }

                &.is-place {
                    @include breakpoint(small down) {
                        align-items: center;
                    }

                    @include fa-icon-style(false) {
                        margin-left: 4px;
                        margin-right: 14px;

                        @include breakpoint(small down) {
                            font-size: 3rem;
                            margin-right: 26px;
                        }
                    }
                }

                @include fa-icon-style(false) {
                    color: var(--color-1--1);
                    font-size: 2.3rem;
                    font-weight: var(--fw-light);
                    margin-right: 14px;
                }

                .place-label {
                    margin-right: 5px;
                }
            }
        }

        #{$this}__content-buttons {
            margin: 0 0 30px 0;
            width: fit-content;

            @include breakpoint(small down) {
                width: 100%;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .add-to-calendar {
                &.btn {
                    @include size(100%);
                    align-items: baseline;
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    color: $color-white;
                    display: flex;
                    padding: 14px 25px;

                    @include breakpoint(small down) {
                        font-size: 1.2rem;
                    }

                    @include fa-icon-style(false) {
                        &::before {
                            font-size: 1.6rem;
                        }
                    }

                    @include on-event() {
                        background-color: $color-white;
                        color: var(--color-1--1);
                    }
                }
            }

            .book-btn {
                &.btn {
                    @include font(var(--typo-1), 1.2rem, var(--fw-normal));
                    background-color: transparent;
                    border-color: var(--color-1--1);
                    color: var(--color-1--1);
                    margin-bottom: 10px;
                    padding: 14px 30px;
                    width: auto;

                    @include breakpoint(small down) {
                        font-size: 1.2rem;
                        width: 100%;
                    }

                    @include on-event() {
                        background-color: var(--color-1--1);
                        color: $color-white;
                    }
                }
            }
        }

        #{$this}__events-first {
            @include breakpoint(large only) {
                padding-right: 50px;
                width: 752px;

                &.is-travaux {
                    padding-right: 0;
                    width: 50%;
                }
            }

            @include breakpoint(small down) {
                padding: 0 0 30px 0;
                width: 100%;
            }

            .date-badge-item {
                @include absolute(-68px, null, null, -6px);
                align-items: center;
                display: flex;

                @include breakpoint(small down) {
                    top: -95px;
                }

                &.is-travaux {
                    @include breakpoint(large only) {
                        left: auto;
                        right: 0;
                        z-index: 9;
                    }
                }

                .with-image & {
                    @include breakpoint(small down) {
                        top: -55px;
                    }
                }

                .event-badge {
                    margin-left: -15px;
                    margin-top: 0;

                    @include breakpoint(small down) {
                        margin-left: -15px;
                    }
                }

                .date {
                    @include breakpoint(small down) {
                        @include size(121px);
                        padding: 40px 14px;

                        .is-day {
                            font-size: 2.3rem;
                        }

                        .is-month {
                            font-size: 0.9rem;
                        }
                    }
                }
            }
        }

        #{$this}__events-second {
            @include breakpoint(large only) {
                border-left: 1px solid $color-3--3;
                margin-top: 13px;
                padding-bottom: 32px;
                padding-left: 24px;
                width: 330px;

                &.is-travaux {
                    padding-left: 132px;
                    width: 50%;
                }
            }

            @include breakpoint(medium down) {
                border-left: none;
                border-radius: 0;
                border-top: 1px solid $color-3--3;
                margin-top: 17px;
                padding: 30px 0 0 0;
                width: 100%;
            }

            @include breakpoint(small down) {
                margin-top: 0;
            }
        }

        #{$this}__date {
            margin: 0;

            @include breakpoint(small down) {
                width: 100%;
            }

            .date {
                @include breakpoint(small down) {
                    width: 100%;
                }

                &::before {
                    content: none;
                }
            }
        }
    }

    &__works-group {
        display: flex;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    &__links {
        @extend %button-links-group;
        align-self: stretch;
        flex-shrink: 0;
        z-index: 3;

        @include breakpoint(medium down) {
            margin-top: 75px;
        }

        @include breakpoint(small down) {
            align-items: flex-start;
            flex-direction: column;
            margin-top: 0;
        }

        .btn {
            height: 60px;
            min-height: 39px;
            width: -moz-fit-content;
            width: fit-content;

            &.is-primary {
                @include on-event() {
                    background-color: var(--color-1--4);
                    border-color: var(--color-1--4);
                }
            }
        }
    }

    &__information {
        border-bottom: 1px solid $color-3--3;
        margin-bottom: 26px;
        padding-bottom: 27px;
        position: relative;
    }

    &__title-contact-groupe {
        font-weight: var(--fw-bold);
    }

    &__contact-groupe {
        z-index: 1;
    }

    &__synonyms {
        @include font(null, 2.4rem, var(--fw-bold));
        color: var(--color-1--1);
        margin: 0 0 5px;
        z-index: 1;
    }

    &__publication {
        margin: 25px 0;

        &.is-large {
            margin: 35px 0 5px;

            @include breakpoint(small down) {
                margin: 20px 0 5px;
            }
        }
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.55;
        margin: 0;

        &.is-main {
            @include font(null, 1.8rem, var(--fw-bold));
            color: var(--color-1--1);
            margin: 15px 0 0;

            @include breakpoint(small down) {
                margin: 10px 0 0;
            }
        }
    }

    &__teaser {
        font-weight: var(--fw-bold);
        margin-top: 40px;
        position: relative;
        z-index: 1;

        @include breakpoint(small down) {
            margin-top: 30px;
        }

        .single-guide & {
            @include breakpoint(large only) {
                margin-bottom: 30px;
            }
        }

        .single-event-page & {
            @include breakpoint(large only) {
                margin-bottom: 100px;
            }
        }

        .is-single-news & {
            font-weight: var(--fw-bold);

            @include breakpoint(large only) {
                margin-bottom: 9px;
                margin-top: 20px;
            }
        }

        +.heading__status {
            margin-top: 20px;
        }
    }

    &__quote {
        margin-top: 30px;
        position: relative;

        @include breakpoint(small down) {
            margin-top: 20px;
        }

        &::after {
            content: close-quote;
        }

        &::before {
            content: open-quote;
        }
    }

    &__content-state {
        align-items: center;
        border-bottom: 1px solid $color-3--4;
        border-top: 1px solid $color-3--4;
        display: flex;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-direction: column;
        }
    }

    &__status {
        margin: 35px 0 0;
        position: relative;
        z-index: 2;

        >* {
            display: inline-block;
        }
    }

    .copyright {
        display: block;
    }
}
