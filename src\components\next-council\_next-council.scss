.next-council {
    $this: &;

    &.section {
        margin: 47px 0 0;

        @include breakpoint(small down) {
            margin-top: 30px;
        }
    }

    &__container {
        @extend %container;
        display: flex;
        flex-direction: column-reverse;

        @include breakpoint(medium only) {
            max-width: 800px;
            padding: 0 60px;
        }
    }

    &__wrapper {
        align-items: center;
        background-color: $color-3--2;
        display: flex;
        flex-grow: 1;
        padding: 52px 66px 52px 78px;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
            justify-content: center;
            padding: 45px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 27px 31px 38px;
            text-align: center;
        }
    }

    &__svg {
        @include size(188px, 146px);
        flex-shrink: 0;
        margin-right: 52px;

        @include breakpoint(medium down) {
            @include size(138px, 96px);
            margin: 0 0 20px;
        }

        @include breakpoint(small down) {
            @include size(98px, 56px);
        }

        svg {
            @include size(100%);
            fill: var(--color-1--1);
        }
    }

    &__inner-wrapper {
        flex-grow: 1;
        width: 100%;
    }

    &__title {
        @include line-decor;
        font-size: 3.5rem;
        font-weight: 400;
        line-height: 1.1;
        margin-bottom: 13px;
        padding-bottom: 13px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
        }

        &::before {
            @include absolute(null, null, 0, 0);

            @include breakpoint(small down) {
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }

    &__content {
        align-items: flex-end;
        display: flex;
        justify-content: space-between;
        width: 100%;

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
        }
    }

    &__text {
        margin-right: 30px;

        @include breakpoint(medium down) {
            max-width: 40%;
        }

        @include breakpoint(small down) {
            margin-right: 0;
            max-width: 100%;
        }
    }

    &__date {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        line-height: 1.15;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            margin-bottom: 0;
        }

        @include breakpoint(small down) {
            text-align: center;
        }
    }

    &__place {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        display: block;
        line-height: 1.15;

        @include breakpoint(medium down) {
            margin: 5px 0;
        }
    }

    &__links {
        flex-shrink: 0;

        @include breakpoint(small down) {
            margin-top: 20px;
        }
    }

    // Styles for secondary type
    &.is-secondary {
        #{$this}__container {
            @include breakpoint(large) {
                flex-direction: row-reverse;
            }
        }

        #{$this}__elected {
            @extend %underline-context;
            background-color: var(--color-1--3);
            color: var(--color-1--4);
            display: block;
            flex-grow: 1;
            font-size: 2rem;
            font-weight: 700;
            margin-right: 24px;
            max-width: 280px;
            padding: 56px 30px 69px;
            text-align: center;
            text-decoration: none;

            @include breakpoint(medium down) {
                align-items: center;
                display: flex;
                justify-content: center;
                margin: 0 0 10px;
                max-width: 100%;
                padding: 22px;
            }

            .underline {
                @include multiline-underline($color: var(--color-1--4));
            }

            &:focus {
                outline: 2px solid currentColor !important;
                outline-offset: 3px;
            }
        }

        #{$this}__elected-svg {
            @include size(96px, 85px);
            display: block;
            margin: 0 auto 8px;

            @include breakpoint(medium down) {
                @include size(61px, 51px);
                margin: 0 16px 0 0;
            }

            svg {
                @include size(100%);
                fill: var(--color-1--4);
            }
        }

        #{$this}__elected-title[class] {
            @include breakpoint(medium down) {
                width: auto;
            }
        }
    }
}
