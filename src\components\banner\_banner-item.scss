.banner-item {
    $this: &;

    @extend %link-block-context;
    align-items: stretch;
    border-radius: 10px;
    display: flex;
    flex-direction: row-reverse;
    position: relative;
    z-index: 1;

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    @include breakpoint(small down, true) {
        flex-direction: column-reverse;

        &.is-reverse {
            flex-direction: column;
        }
    }

    &__image-container {
        width: 100%;

        #{$this}__image {
            &.full-image {
                border-radius: 10px;
                width: 100%;

                img {
                    border-radius: 10px;

                    @include breakpoint(small down) {
                        min-height: 270px;
                    }
                }
            }
        }
    }

    &__image {
        width: 50%;

        &::before {
            @include size(100%);
            @include absolute(40px, null, null, 125px);
            content: "";
            background: {
                image: image("Fond-Bandeau-Dsktop.svg");
                repeat: no-repeat;
                size: contain;
            };
            z-index: -999;

            @include breakpoint(medium down) {
                @include absolute(20px, null, null, 67px);
                background-image: image("Fond-Bandeau-Tablette.svg");
            }

            @include breakpoint(small down) {
                @include absolute(auto, null, -5%, 15px);
                @include size(calc(100% - 40px), 80%);
                background-color: var(--color-1--1);
                background-image: none;
                border-top-left-radius: 20px;
                transform: matrix(-1, -0.05, 0.05, -1, 0, 0);

                .home-feed & {
                    bottom: -6px;
                }
            }

            .banner-content & {
                @include absolute(auto, 49px, -48px, auto);
                @include size(712px, 184px);

                @include breakpoint(medium down) {
                    bottom: -69px;
                    width: 579px;
                }

                @include breakpoint(small down) {
                    bottom: -6px;
                    height: 50px;
                    width: 227px;
                }
            }
        }

        @include breakpoint(small down) {
            width: 100%;
        }

        @include breakpoint(small down, true) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);

            @include breakpoint(small down) {
                max-height: 250px;
            }

            &:not(.home-feed) & {
                @include breakpoint(xsmall down) {
                    max-height: 135px;
                }
            }
        }
    }

    &__content {
        @include size(50%, null);
        padding: 40px 100px 40px 65px;

        @include breakpoint(medium down) {
            padding: 21px 60px 21px 30px;
        }

        @include breakpoint(small down) {
            padding: 28px 50px 28px 34px;
            width: 100%;
        }

        .is-inverted & {
            color: $color-white;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        @include absolute(null, 58px, 53px, null);
        font-size: 3rem;
        font-weight: var(--fw-normal);

        @include breakpoint(medium down) {
            @include absolute(null, 32px, 28px, null);
            font-size: 1.6rem;
        }

        @include breakpoint(small down) {
            @include absolute(null, 24px, 30px, null);
        }
    }

    &__title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        display: inline;
        line-height: 1.2;

        @include breakpoint(medium down) {
            font-size: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }

        .underline {
            .is-inverted & {
                @include multiline-underline($color: $color-white);
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &.is-reverse {
        flex-direction: row;

        @include breakpoint(small down) {
            flex-direction: column;
        }

        @include fa-icon-style(false) {
            right: calc(50% + 58px);

            @include breakpoint(medium down) {
                right: calc(50% + 32px);
            }

            @include breakpoint(small down) {
                bottom: 285px;
                right: 24px;
            }

            @include breakpoint(xsmall down) {
                bottom: 170px;
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            flex-direction: column-reverse;

            &.is-reverse {
                flex-direction: column;

                @include fa-icon-style(false) {
                    bottom: 195px;
                }
            }

            #{$this}__content {
                padding: 37px 59px 37px 41px;
                width: 100%;
            }

            #{$this}__title {
                font-size: 2.4rem;
                line-height: calc(28 / 24);
            }

            #{$this}__image {
                &::before {
                    bottom: -121px;
                    width: 318px;
                }
            }

            @include fa-icon-style(false) {
                bottom: 35px;
                font-size: 1.9rem;
                right: 28px;
            }
        }
    }
}
