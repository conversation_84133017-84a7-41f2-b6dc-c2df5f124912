.main-nav-second-menu {
    $this: &;

    &__list {
        align-items: center;
        display: flex;
        gap: 35px;
        justify-content: end;

        @include breakpoint(medium down) {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            margin: 0 auto !important;
            width: 60%;
        }

        @include breakpoint(small down) {
            display: block;
            width: 100%;
        }
    }

    &__item {
        padding: 10px 14px;
        position: relative;

        @include breakpoint(medium down) {
            margin: 3px 0;
        }

        @include breakpoint(small down) {
            margin: 0;
            padding: 0;
        }
    }

    &__link {
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        @include trs;
        @include focus-outline($offset: 2px);
        background-color: transparent;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        display: flex;
        letter-spacing: 0;
        line-height: 2.7rem;
        padding: 0;
        position: relative;
        text-decoration: none;
        text-transform: none;
        width: 100%;

        &::after {
            @include absolute(null,null,-5px,0);
            @include size(100%,4px);
            background-color: var(--color-1--1);
            content: '';
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
    
        @include on-event {
            &::after {
                opacity: 1;
            }
        }

        @include breakpoint(small down) {
            display: block;
            margin: 7px auto;
            text-align: center;
        }
    }
}
