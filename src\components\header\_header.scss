.header-wrapper {
    $this: &;

    display: block;
    width: 100%;

    &__container {
        @extend %container-fluid;
        margin: 0 auto;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 100%;
        }
    }

    .has-page-image &,
    body.home-page &,
    body.home-hospital-page & {
        position: relative;
        z-index: 99;
    }
}

.header {
    $this: &;

    @include trs(background);
    opacity: 1;
    padding: 0;
    z-index: 20;

    @include breakpoint(medium down) {
        &.js-fixed-el-abs {
            left: 0;
            transform: none;
        }
    }

    &__inner {
        align-items: center;
        background-color: var(--color-1--1);
        display: flex;
        margin: 0 auto;
        max-width: 1460px;
        min-height: 80px;
        padding: 20px 20px;
        position: relative;
    
        .is-account-page & {
            height: 134px;
        }

        @include breakpoint(medium down) {
            min-height: 65px;
            padding: 12px 26px;
        }

        @include breakpoint(small down) {
            min-height: 45px;
            padding: 9px 26px;
        }

        &::before {
            @include absolute(0, null, null, -50%);
            @include size(100%);
            background-color: var(--color-1--1);
            content: '';
            z-index: -1;
        }

        &::after {
            @include absolute(0, -50%, null, null);
            @include size(100%);
            background-color: var(--color-1--1);
            content: '';
            z-index: -1;
        }
    }

    &__logo {
        @include trs;
        align-content: center;
        align-items: center;
        display: flex;
        flex: 1 0 404px;
        min-height: 80px;
        padding: 0;

        @include breakpoint(medium down) {
            flex-basis: 178px;
            min-height: 65px;
        }

        @include breakpoint(small down) {
            flex-basis: 189px;
            justify-content: center;
            min-height: 45px;
        }

        #{$this}:not(.js-fixed-el) & {
            body:not(.is-mnv-opened) .has-page-image &,
            body:not(.is-mnv-opened).home-page &,
            body:not(.is-mnv-opened).home-hospital-page & {
                a.logo {
                    color: $color-white;
                }
            }
        }
    }

    &__components {
        align-items: center;
        display: flex;
        flex: 1 1 100%;
        flex-wrap: wrap;
        justify-content: flex-end;

        @include breakpoint(small down) {
            display: none;
        }
    }

    &__actions {
        align-items: center;
        display: flex;
        margin-right: 35px;

        @include breakpoint(medium down) {
            height: 65px;
            margin-right: 0;
        }

        @include breakpoint(small down) {
            display: none;
        }

        .btn {
            background-color: transparent;
            border-color: transparent;
            color: $color-white;
            margin-right: 11px;
            padding: 0;
            padding: 0 7px;

            @include on-event {
                text-decoration: underline;
            }

            &__svg {
                height: 22px !important;
                width: 22px !important;
            }
        }
    }

    &__lang {
        @include size(54px, 80px);
        align-items: center;
        display: flex;
        margin-right: 35px;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__nav {
        align-items: center;
        background-color: $color-white;
        display: flex;
        flex-grow: 1;
        justify-content: center;
        width: 100%;
    }

    &.has-nav-bottom {
        @include breakpoint(large) {
            padding-bottom: 0;
        }

        #{$this}__inner {
            @include breakpoint(large) {
                flex-wrap: wrap;
            }
        }

        #{$this}__components {
            @include breakpoint(large) {
                flex-basis: calc(100% - 404px);
            }
        }

        #{$this}__nav {
            @include breakpoint(large) {

                background-color: var(--color-1--1);
                margin: 25px 0 0;
                min-height: 80px;
                position: relative;
                width: 100%;

                &::before,
                &::after {
                    @include absolute(0, null, 0, null);
                    background-color: var(--color-1--1);
                    content: '';
                    width: 1000vh;
                    z-index: -1;
                }

                &::before {
                    left: 0;
                }

                &::after {
                    right: 0;
                }
            }

            @include breakpoint(medium down) {
                display: none;
            }
        }
    }

    &.js-fixed-el {
        box-shadow: 0 0 20px rgba($color-black, 0.1);

        .is-account-page & {
            box-shadow: none;
        }

        @include breakpoint(medium) {
            #{$this}__inner {
                min-height: 45px;
                padding: 12px 20px;
            }

            #{$this}__nav-second {
                display: none;
                padding: 10px 0;

                .is-account-page & {
                    display: block;
                    visibility: hidden;
                }
            }

            #{$this}__logo {
                flex-basis: 272px;
                min-height: 45px;

                @include breakpoint(medium down) {
                    flex-basis: 195px;
                }
            }

            #{$this}__actions,
            #{$this}__lang {
                height: auto;
            }

            .lang {
                &__toggle {
                    height: 45px;
                }

                &__block {
                    top: 100%;
                }
            }
        }

        &.has-nav-bottom {
            @include breakpoint(large) {
                padding-bottom: 0;

                #{$this}__nav {
                    margin-top: 10px;
                    min-height: 55px;
                }
            }
        }
    }

    &:not(.js-fixed-el) {
        body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
        body:not(.is-mnv-opened).home-page &,
        body:not(.is-mnv-opened).home-hospital-page & {
            .header__actions {
                .btn {
                    background-color: transparent;
                    border-color: transparent;
                    color: $color-white;
                    margin-right: 11px;
                    padding: 0 7px;

                    @include breakpoint(medium down) {
                        margin-right: 17px;
                    }

                    @include on-event {
                        text-decoration: underline;
                    }

                    &__svg {
                        @include size(30px);
                    }
                }
            }
        }
    }

    &:not(.js-fixed-el).has-logo-center {
        #{$this}__logo {
            @include absolute(20px, 50%);
            transform: translate(50%, 100%);

            @include breakpoint(medium down) {
                top: 125px;
            }

            @include breakpoint(small down) {
                top: 75px;
            }
        }

        #{$this}__actions {
            display: flex;
            margin-right: auto;

            @include breakpoint(small down) {
                height: 45px;
            }
        }
    }

    &__nav-second {
        background-color: $color-white;
        margin: 0 auto;
        max-width: 1460px;
        min-height: 80px;
        padding: 16px 20px;

        @include breakpoint(medium down) {
            display: none;
        }
    }
}
