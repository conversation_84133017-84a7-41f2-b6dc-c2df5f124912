{%- from 'views/utils/utils.njk' import setAttributes -%}

{#
    GoToTop template.
#}
{%- macro GoToTop(
    icon = 'fa-regular fa-arrow-up-long',
    text = 'Retour en haut',
    scrollTarget = '0',
    focusTarget = '.site-wrapper:global',
    bottomElement = '.footer__wrapper:global',
    hideAtTop = 'true'
) -%}
    <div class="go-to-top js-scroll-handler"
         {{ setAttributes({
             'data-scroll-target': scrollTarget,
             'data-focus-target': focusTarget,
             'data-bottom-element': bottomElement,
             'data-hide-at-top': hideAtTop
         }) }}
    >
        <p class="go-to-top__text">
            <a href="#" class="go-to-top__link">
                <span class="{{ icon }}" aria-hidden="true"></span>
                <span class="ghost">{{ text }}</span>
            </a>
        </p>
    </div>
{%- endmacro -%}
