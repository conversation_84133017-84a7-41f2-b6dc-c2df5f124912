{%- from 'views/utils/utils.njk' import svg with context -%}

{%- macro MainNavToggle() -%}
    <button type="button"
            class="main-nav-toggle"
            id="main-nav-toggle"
            aria-label="Menu - fenêtre modale"
            aria-haspopup="dialog"
    >
        <span class="main-nav-toggle__bars" aria-hidden="true">
            {{ svg('ico-menu',20,20)}}
            <span class="main-nav-toggle__bar"></span>
            <span class="main-nav-toggle__bar"></span>
            <span class="main-nav-toggle__bar"></span>
        </span>
        <span class="main-nav-toggle__text">Menu</span>
    </button>
{%- endmacro -%}
