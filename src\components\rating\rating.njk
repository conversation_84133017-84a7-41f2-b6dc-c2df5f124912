{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    Rating template.
    @param {string} icon - rating icon
    @param {number} count - rating icons count
#}
{%- macro Rating(result = false, icon = 'fas fa-star', count = 5, currentValue = 4) -%}
    {% set className = 'rating-result' if result else 'rating' %}

    <div class="rating js-rating {{ 'is-result' if result }}" data-rating-current-value="{{ currentValue }}">
        {% if not result %}
            <form action="#" class="rating__form">
        {% else %}
            <p class="sr-only">Note actuelle : {{ currentValue }} sur {{ count }}</p>
        {% endif %}
            <div class="rating__block" {{ 'aria-hidden="true"' if result }}>
                {%- for item in range(0, count + 1) -%}
                    {% set number = loop.index - 1 %}
                    <input type="radio" name="{{ className }}" {{ 'checked' if number === 0 }} value="{{ number }}" id="{{ className }}{{ number }}" />
                    <label for="{{ className }}{{ number }}">
                        {% if number > 0 %}
                            <span class="{{ icon }}" aria-hidden="true"></span>
                        {% endif %}
                        <span class="ghost">{{ number }} étoile{{ 's' if number > 1 }}</span>
                    </label>
                {%- endfor -%}
            </div>
        {% if not result %}
                <output class="rating__output"></output>
                <button type="submit" class="btn is-small rating__button">Noter</button>
            </form>
        {% endif %}
    </div>
{%- endmacro -%}

{#
    RatingSG template.
    Styleguide template.
#}
{%- macro RatingSG() -%}
    {% call SG.Section('rating') %}
        <h2 class="styleguide-section__title">Rating</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-sm-6">
                    <div class="has-mb-3">
                        {{ Rating() }}
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="has-mb-3">
                        {{ Rating(result = true) }}
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}
