.organigramme-item {
    $this: &;

    @extend %link-block-context;
    display: flex;

    @include breakpoint(medium down) {
        flex-wrap: wrap;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
        text-align: center;
    }

    &.is-maire {
        flex-direction: row;
    }

    .is-commune-page & {
        flex-direction: column;
    }

    & &__picture {
        @include size(180px);
        border-radius: 90px;
        display: block;
        flex-shrink: 0;
        margin-right: 24px;

        img {
            border-radius: 90px;
        }

        @include breakpoint(medium down) {
            margin: 0 24px 20px 0;
        }

        @include breakpoint(small down) {
            @include min-size(110px);
            margin: 0 0 27px;
        }

        .is-commune-page & {
            border-radius: 10px;
        }
    }

    &__content {
        @include font(var(--typo-1), null, var(--fw-normal));
        color: $color-3--3;
        flex-basis: min-content;
        flex-grow: 1;
        font-family: var(--typo-1);
        min-width: 200px;
    }

    &__position {
        @include font(null, 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 2.52px;
        line-height: 1.125;
        margin: 0 0 20px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
            margin-bottom: 13px;
        }
    }

    &__name {
        @include font(null, 2.2rem, var(--fw-bold));
        color: $color-black;
        line-height: calc(24 / 22);
        margin: 0;

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
        }
    }

    &__name-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__function-wrapper {
        position: relative;
        z-index: 5;
    }

    &__function {
        @include font(null, 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.38;
        margin: 0 0 6px;

        @include breakpoint(small down) {
            margin-bottom: 0;
        }

        &.is-location {
            color: $color-3--5;
            text-decoration: underline;

            @include on-event {
                text-decoration: none;
            }
        }

        &.is-main {
            color: var(--color-1--1);
            font-size: 1.8rem;
            font-weight: var(--fw-bold);

            @include breakpoint(medium only) {
                font-size: 2rem;
            }

            @include breakpoint(small down) {
                margin-bottom: 2px;
            }
        }
    }

    &__content-info {
        margin-top: 22px;
    }

    &__details {
        &.is-spaces {
            display: flex;
            flex-direction: column;
            gap: 15px 0;
        }
    }

    &__details,
    &__infos {
        a {
            border-radius: 40px;
            font-weight: var(--fw-normal);
            position: relative;
            z-index: 42;
        }
    }

    &__infos[class] {
        margin-top: 32px;
    }

    &__infos-item {
        .btn {
            &.is-small {
                min-width: 179px;
            }
        }

        &:not(:last-child) {
            margin-bottom: 5px;
        }
    }

    &.is-primary {
        background-color: $color-3--1;
        padding: 35px;

        #{$this}__picture {
            @include size(250px);
            margin-right: 35px;

            @include breakpoint(medium down) {
                @include size(200px);
                margin-right: 29px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 20px;
            }
        }

        #{$this}__content {
            @include breakpoint(large) {
                padding-top: 5px;
            }
        }

        #{$this}__position {
            margin: 0 0 23px;

            @include breakpoint(medium down) {
                margin: 0 0 18px;
            }
        }

        #{$this}__name {
            font-size: 3.5rem;
            line-height: calc(40 / 35);

            @include breakpoint(medium down) {
                font-size: 2.2rem;
                line-height: calc(24 / 22);
            }

            @include breakpoint(small down) {
                font-size: 2rem;
                line-height: calc(24 / 20);
            }
        }

        #{$this}__content-info {
            display: flex;
            justify-content: space-between;

            @include breakpoint(medium down) {
                flex-direction: column;
                margin-top: 20px;
            }

            @include breakpoint(small down) {
                padding: 0 5px;
            }
        }

        #{$this}__details {
            padding-right: 15px;

            @include breakpoint(medium down) {
                margin-bottom: 28px;
                padding: 0;
            }
        }

        #{$this}__infos {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            margin: 0;
            width: 250px;

            @include breakpoint(small down) {
                align-items: center;
                width: 100%;
            }
        }

        .is-commune-page & {
            flex-direction: row;
        }
    }
}
