{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/core-components/secondary.njk' as Secondary -%}
{%- from 'views/utils/constants.njk' import kGlobalLinks -%}

{#
    DeliberationsContentItem template.
    @param {object} settings - publication item settings.
#}
{%- macro DeliberationsContentItem(
    link = 'single-deliberations.html',
    imageSize = ['303x427'],
    category = 'Dolor sit amet',
    title = 'Titre de la publication lorem ipsum',
    subtitle = 'Septembre 2099 - N°999',
    teaser = false,
    documents = range(1, 5) | random
) -%}
    <article class="deliberations-content-item">
        <div class="deliberations-content-item__content">
            <span class="item-title js-title-tag-to-change deliberations-content-item__title">
                {% if category %}
                    <span class="theme is-large deliberations-content-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {% endif %}
                <a href="./{{ link }}" class="deliberations-content-item__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
                {%- if subtitle -%}
                    <span class="deliberations-content-item__subtitle">{{ subtitle }}</span>
                {%- endif -%}
            </span>
            {% if documents !== 1 %}
                <p class="publication is-primary deliberations-content-item__publication">
                    <span class="publication__number">{{ documents }} documents</span>
                    <span>Publié le </span>
                    <time datetime="2018-03-28">28/03/2018</time>
                </p>
            {% endif %}
            {%- if teaser or documents === 1 -%}
            <div class="deliberations-content-item__bottom-wrapper">
                {%- if teaser -%}
                    <p class="item-teaser deliberations-content-item__teaser">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore</p>
                {%- endif -%}
                {%- if documents === 1 -%}
                    <div class="deliberations-content-item__actions">
                        {{ Secondary.DocumentActions({
                            icons: [
                                ['download', 'PDF - 1.5 Mo', 'Télécharger Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-arrow-to-bottom', '#'],
                                ['read', '', 'Feuilleter Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-book-open', 'https://google.fr/']
                            ]
                        }) }}
                    </div>
                {%- endif -%}
            </div>
            {%- endif -%}
        </div>

        {{ Image({
            className: 'deliberations-content-item__image',
            sizes: imageSize,
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(100) | random
        }) }}
    </article>
{%- endmacro -%}

{#
    DeliberationsContent template.
    Template for deliberations on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of deliberations
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro DeliberationsContent(
    modifier = '',
    titleText = 'En kiosque',
    itemsCount = 2,
    moreButton = true,
    proposerButton = false,
    imageSize = ['303x427']
) -%}
    {% call Section(
        className = 'deliberations-content js-title-handler',
        modifier = modifier,
        container = false
    ) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            <ul class="list is-columns-1">
                {% for item in range(itemsCount) %}
                    <li class="list__item">
                        {{ DeliberationsContentItem(
                            date = 'Septembre 2099 - N°999' if loop.index === 2,
                            teaser = true if loop.index === 2,
                            documents = 3 if loop.index === 1 else 1,
                            imageSize = imageSize
                        ) }}
                    </li>
                {% endfor %}
            </ul>
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links">
                {% if proposer %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un publication',
                        className = 'btn is-link is-small',
                        icon = 'far fa-long-arrow-right'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        text = 'Toutes les deliberations',
                        className = 'btn is-link is-small',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}
