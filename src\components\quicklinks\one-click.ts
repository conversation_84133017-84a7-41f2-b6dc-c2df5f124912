import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import OnInit from '@core/decorators/init-method.decorator';
import CarouselFactory from '@core/core-components/carousel';
import InlineSVGFactory from '@core/core-components/inline-svg';

interface Item {
    title: string;
    id: string | number;
    icon: string;
    url?: string;
    children?: Item[];
    target?: string;
}

/**
 * StratisAutocomplete plugin.
 */
class Oneclick extends StratisElementAbstract {
    public options: IGlobalOptions<Oneclick> = {
        classList: {
            oneClickWrapperClass: '.quicklinks-block__wrapper',
            toggleOneClickClass: '.js-toggle-one-click-item',
            oneClickReturnBtnWrapperClass: '.js-one-click-return-wrap',
            oneClickReturnBtnClass: '.js-return-btn',
        },
        dataset: {
            'json-path': false,
        },
        DOMElements: {
        },
    };

    private data: any = null;
    private curentItem: any;
    private curentChildren: any;


    public constructor(selector: HTMLElement, options: IGlobalOptions<Oneclick>) {
        super(selector, options);
        if (!this.created) {
            this.init();
        }
    }

    protected createEvents(): void {
        super.createEvents([
            [window, 'resise', this.initSwiper],
        ]);
    }

    private templateRetourBtn(menuItem): string {
        let template = '';
        template = `<button data-item-id="${menuItem.id}" class="one-click-return__button js-return-btn">
            <span aria-hidden="true" class="far fa-long-arrow-left"></span> <span class="btn__svg" aria-hidden="true">
                <img
                    class="inline-svg"
                    src="data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20width=%22'+16+'%22%20height=%22'+8+'%22%20viewBox=%220%200%20'+16+'%20'+8+'%22%20/%3E"
                    data-src="${menuItem.icon}"
                    width="8"
                    height="16"
                    alt
                >
            </span>
            <span class="btn__text">${menuItem.title}</span>
        </button>`;
        return template;
    }

    private templateOneClickCarousel(menuItem): string {
        let template = '';
        template = `<li class="quicklinks-block__item swiper-item swiper-slide swiper-slide-active" aria-hidden="false">
                <div class="quicklink-item "><div class="quicklink-item__svg-wrapper" aria-hidden="true"> 
                <img
                    class="inline-svg"
                    src="data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20width=%22'+43+'%22%20height=%22'+41+'%22%20viewBox=%220%200%20'+43+'%20'+41+'%22%20/%3E"
                    data-src="${menuItem.icon}"
                    width="43"
                    height="41"
                    alt
                >
                </div>`;
        if (menuItem.children) {
            template += `<span class="quicklink-item__text js-toggle-one-click-item" tabindex="0" data-item-id="${menuItem.id}" role="button"><span class="underline">${menuItem.title}</span></span>`;
        } else {
            template += `<a href="${menuItem.url}" class="quicklink-item__text" tabindex="0" ${menuItem.target ? `target="${menuItem.target}"` : ''}><span class="underline">${menuItem.title}</span></a>`;
        }
        template += '</div></li>';
        return template;
    }

    private initSwiper(): void {
        setTimeout(() => {
            CarouselFactory.create('.js-swiper-oneclick', {
                swiperOptions: {
                    slidesPerGroup: 1,
                    slidesPerView: 3,
                    slidesPerColumn: 4,
                    slidesPerColumnFill: 'row',
                    breakpoints: {
                        320: {
                            slidesPerGroup: 1,
                            slidesPerView: 3,
                            slidesPerColumn: 4,
                            slidesPerColumnFill: 'row',
                        },
                        768: {
                            slidesPerGroup: 1,
                            slidesPerView: 4,
                            slidesPerColumn: 3,
                            slidesPerColumnFill: 'row',
                        },
                        1280: {
                            slidesPerGroup: 1,
                            slidesPerView: 4,
                            slidesPerColumn: 3,
                            slidesPerColumnFill: 'row',
                        },
                    },
                },
            });
        });
    }

    private getCurrentItem(itemId): any {

        function searchItems(items): any {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return item;
                }
                if (item.children) {
                    const found = searchItems(item.children);
                    if (found) {
                        return found;
                    }
                }
            }
            return null;
        }

        return searchItems(this.data);
    }

    private getParentItem(itemId): any {
        function searchItems(items, parent = null): any {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return parent;
                }
                if (item.children) {
                    const found = searchItems(item.children, item);
                    if (found) {
                        return found;
                    }
                }
            }
            return null;
        }

        return searchItems(this.data);
    }

    private hasParentItem(itemId): boolean {
        function searchItems(items, parent = null): boolean {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return parent !== null;
                }
                if (item.children) {
                    const found = searchItems(item.children, item);
                    if (found) {
                        return true;
                    }
                }
            }
            return false;
        }

        return searchItems(this.data);
    }
    private getChildren(itemId): any {
        function searchItems(items): any {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return item.children || [];
                }
                if (item.children) {
                    const found = searchItems(item.children);
                    if (found) {
                        return found;
                    }
                }
            }
            return null;
        }
        return searchItems(this.data);
    }

    private getParents(itemId): Item[] | null {
        function searchItems(items): any {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return items;
                }
                if (item.children) {
                    const found = searchItems(item.children);
                    if (found) {
                        return found;
                    }
                }
            }
            return null;
        }

        return searchItems(this.data);
    }

    private handleClick(): void {
        const { toggleOneClickClass } = this.options.classList;
        const toggleOneClick = document.querySelectorAll(toggleOneClickClass);
        toggleOneClick.forEach(oneClickItem => {
            oneClickItem.addEventListener('click', e => {
                e.stopPropagation();
                const itemId = oneClickItem.getAttribute('data-item-id');
                this.curentItem = this.getCurrentItem(itemId);
                this.curentChildren = this.getChildren(itemId);
                this.renderReturnBtn(this.curentItem);
                this.renderOneClick(this.curentChildren);
                this.initSwiper();
                this.handleClick();
                this.handleClickReturnBtn();
            });
        });
    }

    private renderOneClick(data): void {
        const { oneClickWrapperClass } = this.options.classList;
        const oneClickWrappers = document.querySelectorAll(oneClickWrapperClass);
        oneClickWrappers.forEach(oneClickWrapper => {
            oneClickWrapper.innerHTML = '';
            data.forEach(el => {
                const template = this.templateOneClickCarousel(el);
                oneClickWrapper.insertAdjacentHTML('beforeend', template);
                InlineSVGFactory.create('.inline-svg');
            });
        });
    }

    private renderReturnBtn(currentItem): void {
        const { oneClickReturnBtnWrapperClass } = this.options.classList;
        const oneClickReturnBtnWrappers = document.querySelectorAll(oneClickReturnBtnWrapperClass);
        const template = this.templateRetourBtn(currentItem);
        if (oneClickReturnBtnWrappers as NodeListOf<HTMLElement>) {
            oneClickReturnBtnWrappers.forEach(el => {
                el.innerHTML = '';
                el.insertAdjacentHTML('beforeend', template);
                InlineSVGFactory.create('.inline-svg');
            });
            this.handleClickReturnBtn();
        }
    }

    private isFirstLevelElement(itemId): boolean {
        function searchItems(items): boolean {
            for (const item of items) {
                if (parseInt(item.id, 10) === parseInt(itemId, 10)) {
                    return true;
                }
                if (item.children) {
                    const found = searchItems(item.children);
                    if (found) {
                        return false;
                    }
                }
            }
            return false;
        }

        return searchItems(this.data);
    }
    private removeReturnBtn(): void {
        const { oneClickReturnBtnWrapperClass } = this.options.classList;
        const oneClickReturnBtnWrappers = document.querySelectorAll(oneClickReturnBtnWrapperClass);
        if (oneClickReturnBtnWrappers as NodeListOf<HTMLElement>) {
            oneClickReturnBtnWrappers.forEach(el => {
                el.innerHTML = '';
            });
        }
    }

    private handleClickReturnBtn(): void {
        const { oneClickReturnBtnClass } = this.options.classList;
        const oneClickReturnBtns = document.querySelectorAll(oneClickReturnBtnClass);

        oneClickReturnBtns.forEach(btn => {
            btn.addEventListener('click', e => {
                e.stopPropagation();
                const itemId = btn.getAttribute('data-item-id');
                const curentItem = this.getParentItem(itemId) || this.getCurrentItem(itemId);
                let parents;

                if (this.getChildren(curentItem) || !this.isFirstLevelElement(itemId)) {
                    this.renderReturnBtn(curentItem);

                } else {
                    this.removeReturnBtn();
                }
                if (this.getChildren(curentItem) || !this.isFirstLevelElement(itemId)) {
                    parents = this.getParents(this.getChildren(curentItem.id)[0].id);

                } else {
                    parents = this.getParents(curentItem.id);
                }
                if (parents) {
                    this.renderOneClick(parents);
                    this.initSwiper();
                    this.handleClick();
                }
            });
        });
    }

    @OnInit()
    private async loadData(): Promise<any> {
        const { 'json-path': path } = this.options.dataset;
        try {
            const response = await fetch(path, {
                method: 'GET',
            });

            if (response.status >= 200 && response.status < 300) {
                const results = await response.json();
                const [firstResult] = Object.keys(results).map((key): any => results[key]);
                this.data = firstResult;
                if (this.data.length) {
                    this.renderOneClick(this.data);
                    this.initSwiper();
                    this.handleClick();
                }
            }
        } catch (error) {
            console.warn('JSON file with data not found.');
            console.warn(error);
        }
    }

}

const OneclickFactory = StratisFactoryMixin<typeof Oneclick, Oneclick, IGlobalOptions<Oneclick>>(Oneclick);
export default OneclickFactory;
