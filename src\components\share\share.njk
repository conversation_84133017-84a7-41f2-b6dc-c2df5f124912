{%- from 'views/core-components/dropdown.njk' import Dropdown -%}
{%- from 'views/core-components/icon.njk' import Icon -%}

{#
    Share button widget.
    @param {object[]} items - share-list items.
#}
{% set defaultItems = [
    {
        type: 'facebook',
        icon: 'fab fa-facebook-f'
    },
    {
        type: 'X',
        icon: 'fa-brands fa-x-twitter'
    },
    {
        type: 'linkedin',
        icon: 'fab fa-linkedin'
    },
    {
        type: 'e-mail',
        icon: 'fas fa-envelope'
    }
] %}

{#
    Share template.
    @param {object[]} items - share items array.
#}
{%- macro Share(items = defaultItems) -%}
    {% call Dropdown({
        wrapper: {
            className: 'share'
        },
        toggle: {
            customClassName: 'js-tooltip',
            tooltip: 'Partager la page',
            text: 'Partager cette page',
            icon: {}
        }
    }) %}
        <ul class="share-list">
            {% for item in items %}
                {% set suffix = 'par' if loop.last else 'sur' %}
                {% if loop.last %}
                    <li class="share-list__item">
                        <button
                            type="button"
                            class="share-list__link is-{{ item.type }} iframe js-tooltip"
                            aria-label="Partager cette page {{ suffix }} {{ item.type }} - fenêtre modale"
                            data-content="Partager cette page {{ suffix }} {{ item.type }}"
                            aria-haspopup="dialog"
                            data-dialog-label="Partager cette page {{ suffix }} {{ item.type }}"
                            data-type="iframe"
                            data-fancybox
                            data-src="./test-form.html"
                            data-role="presentation"
                        >
                            {{ Icon(item.icon) }}
                            <span class="share-list__link-text">{{ item.type | capitalize }}</span>
                        </button>
                    </li>
                {% else %}
                    <li class="share-list__item">
                        <a
                            href="{{ item.href or '#' }}"
                            class="share-list__link is-{{ item.type }}"
                            aria-label="Partager cette page {{ suffix }} {{ item.type }}"
                            target="_blank"
                        >
                            {{ Icon(item.icon) }}
                            <span class="share-list__link-text">{{ item.type | capitalize }}</span>
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
        </ul>
    {% endcall %}
{%- endmacro -%}


