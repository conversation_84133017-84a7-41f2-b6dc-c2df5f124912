{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/utils/utils.njk' import svg, wrapper with context -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{% import 'views/core-components/secondary.njk' as Secondary %}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'components/account/account-box.njk' import AccountBox -%}
{%- from 'views/utils/utils.njk' import svg with context -%}
{%- from 'views/core-components/image.njk' import Image -%}


{#
    AccountPageHeader template.
#}
{%- macro AccountPageHeader(
    title = 'Mon compte utilisateur',
    description = false
) -%}
    <header class="header-account" role="banner">
        <div class="header-account__container">
            <div class="header-account__wrapper">
                <div class="header-account__info">
                    <span class="header-account__icon far fa-user-circle" aria-hidden="true"></span>
                    <div class="header-account__text">
                        <p class="header-account__title">{{ title | safe }}</p>
                        {%- if description -%}
                            <p class="header-account__description">{{ description | safe }}</p>
                        {%- endif -%}
                    </div>
                </div>
            </div>
        </div>
    </header>
{%- endmacro -%}

{#
    AccountGreeting template
#}
{%- macro AccountGreeting() -%}
    <div class="account-greeting">
        <div class="account-greeting__wrapper">
            <div class="account-greeting__info">
                <p class="account-greeting__description">Dans cet espace, vous allez pouvoir gérer les informations de votre profil, vos préférences, vos abonnements ainsi que retrouver les demandes réalisées depuis les formulaires de notre site.</p>
            </div>
        </div>
    </div>
{%- endmacro -%}

{% set defaultSettings = {
    imageSizes: ['319x213?479', '316x211?767', '384x256?1279'],
    list: false,
    tag: 'p',
    teaser: false
} %}

{#
    AccountHeader template
#}
{%- macro AccountHeader() -%}
    <div class="account-header">
        <div class="account-header__wrapper">
            <div class="account-header__image" aria-hidden="true">
                {{ Image({
                    sizes: defaultSettings.imageSizes,
                    className: 'account-header__picture',
                    alt: 'image alt'
                }) }}
            </div>
            <div class="account-header__info">
                <p class="account-header__description">Paragraphe normal nam nec exemple de lien tellus a odio auctor a ornare odio. Sed non mauris vitae erat consequat auctor eu in elit. Class aptent taciti sociosqu ad litora torquent2 per conubia nostra, per inceptos. </br>Nullam mauris egestas quamac urna eu exemple de survol de lien sit amet a augue. Sed non neque elit. Sed auris egestas quam, ut aliquam massa nisl quis neque.</p>
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    AccountHeading template
#}
{%- macro AccountHeading(
    title = lorem(3, 'words'),
    description = false,
    requestDetails
) -%}
    <div class="account-heading">
        <h1 class="account-heading__title">{{ title }}</h1>
        {% if description %}
            <p class="account-heading__description">{{ description }}</p>
        {% endif %}
        {% if requestDetails %}
            <div class="account-heading__request-details">
                {{ Secondary.RequestDetails() }}
            </div>
        {% endif %}
    </div>
{%- endmacro -%}

{#
    AccountMainNav template.
#}
{%- macro AccountMainNav(
    isSmall = true,
    active = false,
    pageList = [
        ['Ma page d’accueil', 'accountMain', 'account-main'],
        ['Mon profil', 'accountArea', 'account-area'],
        ['Mes abonnements', 'accountSubscriptions', 'account-subscriptions'],
        ['Mes demandes', 'accountRequests', 'account-requests'],
        ['Mon bloc-notes', 'accountNotebook', 'account-notebook'],
        ['Mes evenements', 'accountEvenements', 'chart-network'],
        ['Mes réservations', 'accountReservations', 'account-propositions'],
        ['Me déconnecter', 'home', 'account-logout']
    ]
) -%}
    <nav class="account-main-nav {{ 'is-small' if isSmall }}" role="navigation" aria-label="Menu compte citoyen">
        <div class="account-main-nav__container">
            <ul class="account-main-nav__list">
                {%- for title, link, icon in pageList -%}
                    <li class="account-main-nav__item">
                        <a href="{{ kGlobalLinks[link] }}"
                           class="account-main-nav__link {{ 'is-active' if loop.index === active }} {{ 'js-tooltip' if loop.index === 1 and isSmall }}"
                            {{ 'aria-current=page' if loop.index === active }}
                            {% if loop.index === 1 and isSmall %}
                                data-content="Ma page d'accueil"
                            {% endif %}
                        >
                            <span class="account-main-nav__item-icon" aria-hidden="true">{{ svg('account/' + icon, 25 if isSmall else 66, 25 if isSmall else 66) }}</span>
                            <span class="account-main-nav__item-text">{{ title }}</span>
                        </a>
                    </li>
                {%- endfor -%}
            </ul>
        </div>
    </nav>
{%- endmacro -%}

{#
    AccountActions template.
#}
{% macro AccountActions() %}
    <div class="account__actions">
        {{ Link(
            className = 'btn is-primary is-small',
            text = 'Télécharger mes données',
            icon = 'far fa-file-archive'
        ) }}
        {{ Button(
            className = 'btn is-basic is-small',
            icon = 'far fa-trash',
            text = 'Supprimer mon compte définitivement',
            attrs = {
                'data-src': '#account-remove-popup',
                'data-fancybox': '' | safe,
                'data-small-btn': 'false',
                'data-toolbar': 'false',
                'data-href': '#',
                'data-redirect': 'https://google.com',
                'data-fancybox-body-class': 'is-popup-opened',
                'data-dialog-label': 'Confirmer la suppression',
                'aria-haspopup': 'dialog',
                'aria-label': 'Supprimer mon compte définitivement - fenêtre modale'
            }
        ) }}
    </div>
{% endmacro %}

{#
    AccountProfileForm template.
#}
{%- macro AccountProfileForm() -%}
    {%- call Form.FormWrapper(legend = false) -%}
        <div class="account-form">
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">Vos identifiants</legend>
                {% call Form.FormGroup() %}
                    <div class="col-lg-6 col-xs-12">
                        {%- call Form.FormField(
                            type = 'email',
                            label = 'Courriel :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'email',
                                'aria-describedby': 'input-help-1'
                            }
                        ) -%}
                            <p class="text-help" id="input-help-1" aria-hidden="true">Exemple : <EMAIL></p>
                        {%- endcall -%}
                    </div>
                    <div class="col-lg-6 col-xs-12 account__helper-wrap">
                        <p class="account__helper-text">
                            Vous voulez changer votre mot de passe ?
                            <a
                                href="account-forgot-password.html"
                                data-src="./iframe-account-message.html"
                                data-fancybox="message5"
                                data-type="iframe"
                                data-role="presentation"
                                data-fancybox-transparent="true"
                                data-fancybox-center-content="true"
                                data-fancybox-body-class="is-popup-opened"
                                data-dialog-label="Réinitialisation du mot de passe"
                                aria-haspopup="dialog"
                                aria-label="Réinitialisez-le - fenêtre modale"
                            >
                                Réinitialisez-le !
                            </a>
                        </p>
                    </div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center is-middle') %}
                {{ Button(
                    className = 'btn is-ternary is-large is-bold is-sm-small',
                    type = 'submit',
                    icon = 'far fa-save',
                    text = 'Enregistrer les modifications'
                ) }}
            {% endcall %}
        </div>
    {%- endcall -%}
    {%- call Form.FormWrapper(legend = false) -%}
        <div class="account-form">
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">Vos Coordonnées</legend>
                {% call Form.FormGroup() %}
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'select',
                            label = 'Civilité :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'honorific-prefix'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Prénom :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'given-name'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Nom :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'family-name'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Nom d\'usage :',
                            customAttrs = {
                                'autocomplete' : 'family-name'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-5 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Adresse :',
                            customAttrs = {
                                'autocomplete' : 'street-address'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-3 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Code postal :',
                            customAttrs = {
                                'autocomplete' : 'postal-code',
                                'pattern': '^[0-9]*$',
                                'maxlength' : '5'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Ville de résidence :',
                            customAttrs = {
                                'autocomplete' : 'address-level2'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {%- call Form.FormField(
                            type = 'tel',
                            label = 'Téléphone :',
                            customAttrs = {
                                'autocomplete' : 'tel',
                                'aria-describedby': 'input-help-2'
                            }
                        ) -%}
                            <p class="text-help" id="input-help-2" aria-hidden="true">10 chiffres avec ou sans espace, ex. : 01 23 45 67 89 ou 0123456789</p>
                        {%- endcall -%}
                    </div>
                    <div class="col-xs-12">
                        <legend class="form__legend legend is-background">Association ou établissement culturel</legend>
                        {{ Form.RadioCheckbox(
                            label = 'Je souhaite créer un compte association ou établissement culturel',
                            className = 'has-mb-3',
                            fieldClass = 'js-condition js-association-checkbox',
                            customAttrs = {
                                'data-block-relation': 'condition-additional-block-id-1'
                            }
                        ) }}
                        <div class="rna-wrap" id="condition-additional-block-id-1">
                            {{ Form.FormField(
                                type = 'text',
                                label = 'Numéro RNA',
                                required = false
                            ) }}
                        
                            {{ Form.FormField(
                                type = 'text',
                                label = 'Nom de l\'association',
                                required = true
                            ) }}
                        
                            {{ Form.FormField(
                                type = 'text',
                                label = 'Site internet',
                                required = false
                            ) }}
                        </div>
                    </div>
                {% endcall %}
            </fieldset>
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">Dans les commentaires</legend>
                {% call Form.FormGroup() %}
                    <div class="col-lg-6 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Pseudonyme',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'nickname'
                            }
                        ) }}
                    </div>
                {% endcall %}
                {% call Form.FormGroup() %}
                    <div class="col-xs-12">
                        {% call Form.FormAvatarsGroup() %}
                            {% for avatarIndex in range(1, 10) %}
                                {{ Form.FormAvatar('avatar' + avatarIndex) }}
                            {% endfor %}
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {{ Form.RadioCheckbox(
                            required = true,
                            disableRequiredLabel = false,
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données. <a href="#">Cliquez ici pour les consulter</a>'
                        ) }}
                    </div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center is-middle') %}
                {{ Button(
                    className = 'btn is-ternary is-large is-bold is-sm-small',
                    type = 'submit',
                    icon = 'far fa-save',
                    text = 'Enregistrer les modifications'
                ) }}
            {% endcall %}
        </div>
    {%- endcall -%}
    {{ AccountBox() }}
    {{ AccountActions() }}
{%- endmacro -%}

{#
    AccountTable template.
#}
{%- macro AccountTable(
    modifier,
    caption = lorem(1),
    links = false,
    buttonproposer = false,
    morebutton = false
) -%}
    <div class="table-responsive {{ modifier }}">
        <table class="table account-table">
            <caption><h2>{{ caption }}</h2></caption>
             {% if buttonproposer %}
            {{ Link(
                className = 'btn is-primary is-small',
                text = 'Proposer un événement',
                icon = false
            ) }}
            {% endif %}
            <thead>
                <tr>
                    <th scope="col">№</th>
                    <th scope="col">Formulaires envoyés</th>
                    <th scope="col">Date</th>
                    <th scope="col">Statut</th>
                </tr>
            </thead>
            <tbody>
                {%- for number, description, date, status, modifier in [
                    ['168475', 'Titre lorem ipsum dolor sit amet consectur elis', '12/03/2021', 'Publié', 'public'],
                    ['165708', 'Consetetur sadipscing elitr', '01/03/2021', 'En attente', 'waiting'],
                    ['156220', 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod sed diam nonumy eirmod tempor invidunt ut labore', '08/02/2021', 'Validé', 'valid'],
                    ['123456', 'Nonumy eirmod tempor invidunt ut labore et dolore sed diam voluptua', '16/12/2020', 'Non-publié', 'not-published'],
                    ['168475', 'Titre lorem ipsum dolor sit amet consectur elis', '12/03/2021', 'Nouveau', 'new'],
                    ['168475', 'Consetetur sadipscing elitr', '01/03/2021', 'Abandonné', 'abandoned'],
                    ['156220', 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod sed diam nonumy eirmod tempor invidunt ut labore', '08/02/2021', 'Cloturé', 'fence']
                ] -%}
                    <tr>
                        <td>{{ number }}</td>
                        <td>
                            {% if links %}<a href="account-requests-detail.html">{% endif %}
                                {{ description }}
                            {% if links %}</a>{% endif %}
                        </td>
                        <td>{{ date }}</td>
                        <td>
                            <span class="account-proposition-status is-{{ modifier | lower }}">
                                {% if status == 'Publié' %}
                                    <span class="far fa-check" aria-hidden="true"></span>
                                {% elif status == 'En attente' %}
                                    <span class="far fa-hourglass" aria-hidden="true"></span>
                                {% elif status == 'Validé'%}
                                    <span class="far fa-check" aria-hidden="true"></span>
                                {% elif status == 'Non-publié'%}
                                    <span class="far fa-times" aria-hidden="true"></span>
                                {% elif status == 'Nouveau'%}
                                    <span class="far fa-sparkles" aria-hidden="true"></span>
                                {% elif status == 'Abandonné'%}
                                    <span class="far fa-exclamation-triangle" aria-hidden="true"></span>
                                {% elif status == 'Cloturé'%}
                                    <span class="far fa-check-circle" aria-hidden="true"></span>
                                {% endif %}
                                {{ status }}
                            </span>
                        </td>
                        <td>
                            {% if morebutton %}
                            {{ Link(
                            href = 'account-form-asso.html',
                            textSrOnly = 'Modifier',
                            className = 'btn is-only-icon is-small',
                            icon = 'far fa-pencil',
                            tooltip = 'modifier',
                            text = false
                        ) }}
                            <button type="button" class="btn is-table is-only-icon is-small js-tooltip" data-fancybox="data-fancybox-{{ loop.index }}" data-src="#account-remove-popup" 
                                data-fancybox-body-class= "is-popup-opened",
                                data-dialog-label="Confirmer la suppression",
                                aria-haspopup ="dialog" 
                                data-content="Suprimer" 
                                data-href="#"
                                data-small-btn= "false"
                                data-toolbar= "false">
                                {{ Icon('far fa-trash', title = 'Suprimer') }}
                                <span class="ghost">Suprimer</span>
                            </button>
                            {% endif %}
                            </td>
                    </tr>
                {%- endfor -%}
            </tbody>
        </table>
    </div>
{%- endmacro -%}

{#
    AccountInfo template.
#}
{%- macro AccountInfo() -%}
    <p class="account-info">
        {{ svg('icons/info', 25, 25) }}
        <span>Vous n'avez encore fait aucune proposition de contribution auprès de nos services.</span>
    </p>
{%- endmacro -%}

{#
    SubscriptionsNewsletter template.
#}
{%- macro SubscriptionsNewsletter() -%}
    {% call Section(className = 'account-section', container = false) %}
        {%- call Form.FormWrapper(legend = false) -%}
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">Lettres d’information</legend>
                {% call Form.FormGroup() %}
                    <div class="col-xs-12 col-lg-6">
                        {{ Form.FormField(
                            type = 'email',
                            label = 'Mon courriel enregistré :',
                            value = '<EMAIL>',
                            readonly = true
                        ) }}
                    </div>
                    <div class="col-lg-6 col-md-12 col-xs-12 account__helper-wrap">
                        <p class="account__helper-text">
                            <a href="account-forgot-password.html">Changez votre adresse électronique</a>
                        </p>
                    </div>
                    {% for item in range(0, 3) %}
                        <div class="col-xs-12 has-mb-2">
                            <fieldset class="form__radio-checkbox radio-checkbox is-inline">
                                <legend class="label">
                                    Lettre d’information {{ lorem(2, 'words') }}
                                    <span class="label__description">{{ lorem(1) }}</span>
                                    <span class="label__frequency">Fréquence : hebdomadaire</span>
                                </legend>
                                {{ Form.RadioCheckbox(
                                    type = 'radio',
                                    label = 'S’abonner',
                                    name = 'radio' + loop.index,
                                    checked = true
                                ) }}
                                {{ Form.RadioCheckbox(
                                    type = 'radio',
                                    label = 'Se désabonner',
                                    name = 'radio' + loop.index
                                ) }}
                            </fieldset>
                        </div>
                    {% endfor %}
                    <div class="col-xs-12">
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter.
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    </div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center has-mt-4') %}
                {{ Button(
                    className = 'btn is-ternary',
                    type = 'submit',
                    icon = 'far fa-save',
                    text = 'Enregistrer mes préférences de lettres d\'information'
                ) }}
            {% endcall %}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    SubscriptionsSms template.
#}
{%- macro SubscriptionsSms() -%}
    {% call Section(className = 'account-section', container = false) %}
        {%- call Form.FormWrapper(legend = false) -%}
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">Alertes SMS</legend>
                {% call Form.FormGroup() %}
                    <div class="col-xs-12 col-lg-6">
                        {{ Form.FormField(
                            type = 'tel',
                            label = 'Mon numéro de téléphone portable',
                            value = '06 39 98 67 89',
                            readonly = true
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        <fieldset class="form__radio-checkbox radio-checkbox">
                            <legend class="label">Mes thématiques préférées</legend>
                            <div class="flex-row">
                                {% for item in range(0, 9) %}
                                    <div class="col-lg-4 col-xs-12">
                                        {{ Form.RadioCheckbox(
                                            name = 'radio',
                                            label = lorem(2, 'word'),
                                            checked = true if loop.index == 1 else false
                                        ) }}
                                    </div>
                                {% endfor %}
                            </div>
                        </fieldset>
                    </div>
                    <div class="col-xs-12 has-mt-2">
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    </div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center has-mt-4') %}
                {{ Button(className = 'btn is-ternary js-tooltip is-tooltip-bottom',
                    type = 'submit',
                    icon = 'far fa-save',
                    text = 'Enregistrer mes préférences d\'alertes SMS',
                    attrs = {
                        'data-content' : 'S\'inscrire aux alertes par SMS'
                    }) }}
            {% endcall %}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    SubscriptionsNews template.
#}
{%- macro SubscriptionsNews() -%}
    {% call Section(className = 'account-section', container = false) %}
        {%- call Form.FormWrapper(legend = false) -%}
            <fieldset class="form__fieldset">
                <legend class="form__legend legend is-background">On en parle</legend>
                {% call Form.FormGroup() %}
                    <div class="col-xs-12 has-mb-2">
                        <fieldset class="form__radio-checkbox radio-checkbox">
                            <legend class="label">Actualités - Mes thématiques préférées</legend>
                            <div class="flex-row">
                                {% for item in range(0, 9) %}
                                    <div class="col-lg-4 col-xs-12">
                                        {{ Form.RadioCheckbox(
                                            label = 'Thématique ' + loop.index + ' ' + lorem(1, 'word'),
                                            name = 'radio',
                                            checked = true if loop.index == 1 else false
                                        ) }}
                                    </div>
                                {% endfor %}
                            </div>
                        </fieldset>
                    </div>
                    <div class="col-xs-12">
                        <fieldset class="form__radio-checkbox radio-checkbox">
                            <legend class="label">Agenda - Mes thématiques préférées</legend>
                            <div class="flex-row">
                                {% for item in range(0, 9) %}
                                    <div class="col-lg-4 col-xs-12">
                                        {{ Form.RadioCheckbox(
                                            label = 'Thématique ' + loop.index + ' ' + lorem(1, 'word'),
                                            name = 'radio',
                                            checked = true if loop.index == 1 else false
                                        ) }}
                                    </div>
                                {% endfor %}
                            </div>
                        </fieldset>
                    </div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center has-mt-4') %}
                {{ Button(className = 'btn is-ternary', type = 'submit', icon = 'far fa-save', text = 'Enregistrer mes préférences d\'actualité') }}
            {% endcall %}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    AccountNewPassword template.
#}
{%- macro AccountNewPassword(
    title = 'Réinitialisation de mon mot de passe',
    buttonText = 'Valider mon nouveau mot de passe',
    buttonIcon = 'far fa-arrows-rotate'
) -%}
    <div class="account-new-password">
        <h2 class="account-new-password__title">{{ title | safe }}</h2>
        {%- call Form.FormWrapper(
            legend = 'Nouveau mot de passe',
            legendClassName = 'ghost',
            className = 'js-validator-form account-new-password__form'
        ) -%}
            {% call Form.FormGroup() %}
                <div class="col-lg-6 col-xs-12">
                    {{ Form.SuperPassword() }}
                </div>
                <div class="col-lg-6 col-xs-12">
                    {{ Form.FormField(
                        label = 'Confirmation du mot de passe',
                        type = 'password',
                        required = true,
                        enablePasswordVisibilityManager = true,
                        id = 'password',
                        fieldClass = 'js-validator-identical',
                        customAttrs = {
                            'data-compare-with-id': 'identical-field-password',
                            'autocomplete': 'current-password'
                        }
                    ) }}
                </div>
            {% endcall %}
            {% call Form.FormActions(className = 'is-center') %}
                {{ Button(
                    className = 'btn is-primary',
                    type = 'submit',
                    icon = buttonIcon,
                    text = buttonText
                ) }}
            {% endcall %}
        {%- endcall -%}
    </div>
{%- endmacro -%}
