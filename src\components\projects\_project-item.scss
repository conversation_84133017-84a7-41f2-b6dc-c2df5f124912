.project-item {
    @extend %link-block-context;
    @include size(220px, 100%);
    background-color: var(--color-1--2);
    color: $color-white;

    @include breakpoint(medium down) {
        width: 195px;
    }

    @include breakpoint(small down) {
        width: 100%;
    }

    &__content {
        line-height: 1.25;
        padding: 44px 25px 36px;
        text-align: center;

        @include breakpoint(medium down) {
            padding: 38px 10px 28px;
        }

        @include breakpoint(small down) {
            align-items: center;
            display: flex;
            padding: 20px 50px 25px 30px;
            text-align: left;
        }
    }

    &__image {
        display: block;
        flex-shrink: 0;
        line-height: 0;
        margin: 0 auto 17px;

        @include breakpoint(small down) {
            margin: 0 18px 0 0;
        }

        svg {
            @include size(58px);
            fill: $color-white;

            @include breakpoint(medium only) {
                @include size(52px);
            }
        }
    }

    &__title {
        @include font(null, 2rem, var(--fw-bold));

        @include breakpoint(small down) {
            font-size: 1.8rem;
        }

        .underline {
            @include multiline-underline($color: $color-white);
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__subtitle {
        @include font(null, 1.4rem, var(--fw-normal));
        display: block;
        margin-top: 5px;
        text-transform: none;

        @include breakpoint(medium down) {
            margin-top: 0;
        }
    }
}
