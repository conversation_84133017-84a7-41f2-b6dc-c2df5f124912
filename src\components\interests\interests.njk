{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- import 'views/core-components/form.njk' as Form -%}

{#
    InterestItem template.
#}
{%- macro InterestItem(
    className = '',
    imageSizes = ['480x321?479', '767x513?767', '349x232'],
    modifier
) -%}
    <div class="interest-item {{ className }}">
        {% if modifier !== 'is-width-33' %}
            <figure class="interest-item__image-wrapper" role="figure" aria-label="Caption ipsum sit amet consectur elis lorem ipsum dolor sit amet passam laurea © [COPYRIGHT]">
                {{ Image({
                            sizes: imageSizes,
                            className: 'interest-item__image',
                            serviceID: range(50) | random,
                            alt: '[ALT IMAGE]'
                        }) }}
                <figcaption class="caption">
                    Caption ipsum sit amet consectur elis lorem ipsum dolor sit amet passam
                    <span class="copyright">© [COPYRIGHT]</span>
                </figcaption>
            </figure>
        {% endif %}
        <div class="interest-item__dynamic js-doughnut-chart">
            <div class="interest-item__vote js-chart-vote">
                {{ InterestForm() }}
            </div>
            <div class="interest-item__results is-hidden js-chart-results-container" aria-hidden="true">
                {{ InterestResults() }}
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    InterestForm template.
#}
{%- macro InterestForm(
    radioLabels = ['parfait', 'satisfaisant, peut mieux faire', 'peu satisfaisant', 'insuffisant']
) -%}
    <div class="interest-form">
        {%- call Form.FormWrapper(
            legend = 'Les activités récréatives, sportives et culturelles sont-elles suffisamment diversifiées ?',
            attrs = { 'data-url': 'dynamic-interests-data.html' },
            method = 'GET'
            ) -%}
        {% for label in radioLabels %}
            {{ Form.RadioCheckbox(
                    type = 'radio',
                    name = 'radio',
                    label = label
                ) }}
        {% endfor %}
        {% call Form.FormActions() %}
        {{ Button(className = 'btn is-primary is-small', type = 'submit', text = 'Voter') }}
        {% endcall %}
        {%- endcall -%}
    </div>
{%- endmacro -%}

{#
    InterestResults template.
#}
{%- macro InterestResults(modifier = "",image = true, imageSizes = ['480x321?479', '767x513?767', '349x232']) -%}
    <div class="interest-results {{ modifier }}">

        <div class="interest-results__content">
            <div class="interest-results__header">
                <h3 class="interest-results__title">Les activités récréatives, sportives et culturelles sont-elles suffisamment diversifiées ?</h3>
                <p class="interest-results__number js-interest-results-number">Nombre de participants : 120</p>
            </div>
            <div class="interest-results__chart js-chart-wrapper">
                <div class="interest-results__canvas">
                    <canvas aria-hidden="true"></canvas>
                </div>
                <div class="interest-results__chart-data js-chart-data-container">
                    <ul data-chart-data="data-chart-data" class="chart-data">
                        {% for item in range(6) %}
                            {% set percent = range(5, 50) | random %}
                            <li class="chart-data__item js-chart-item js-chart-label" data-index="{{ loop.index0 }}" data-value="{{ percent }}">
                                <span class="chart-data__colorbox js-chart-data-colorbox"></span>
                                <strong>{{ percent }}%</strong>
                                <span>{{ lorem(range(5) | random, 'words') | capitalize }}</span>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                {% if image %}
                    <figure class="interest-item__image-wrapper" role="figure" aria-label="Caption ipsum sit amet consectur elis lorem ipsum dolor sit amet passam laurea © [COPYRIGHT]">
                        {{ Image({
                            sizes: imageSizes,
                            className: 'interest-item__image',
                            serviceID: range(50) | random,
                            alt: '[ALT IMAGE]'
                        }) }}
                        <figcaption class="caption">
                            Caption ipsum sit amet consectur elis lorem ipsum dolor sit amet passam
                            <span class="copyright">© [COPYRIGHT]</span>
                        </figcaption>
                    </figure>
                {% endif %}
            </div>
        </div>
        <div class="section__more-links">
            {{ Link(
                href = './list-poll.html',
                text = 'Tous les sondages',
                className = 'btn is-link is-small',
                icon = 'far fa-long-arrow-right'
            ) }}
        </div>
    </div>
{%- endmacro -%}

{#
    InterestContent template.
#}
{%- macro InterestContent(modifier="") -%}
    {% call Section(className = 'interests ' + modifier, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = 'Votre avis nous intéresse'
            ) }}
    </div>
    <div class="section__content">
        {{ InterestItem(modifier) }}
    </div>
    <div class="section__more-links">
        {{ Link(
                href = './list-poll.html',
                text = 'Tous les sondages',
                className = 'btn is-link is-small',
                icon = 'far fa-long-arrow-right'
            ) }}
    </div>
    {% endcall %}
{%- endmacro -%}