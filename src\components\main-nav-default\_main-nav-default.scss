/*
 * @name main-nav-default.
 */

body.mnv-opened {
    max-height: 100vh;
    overflow: hidden;
}

body.is-mnv-opened {
    .header-wrapper {
        background-color: $color-white;
    }
}

.main-nav-overlay {
    display: none;

    @include breakpoint(medium down) {
        @include trs;
        @include fixed(0, 0, 0, 0);
        @include size(100%, 100vh);
        background-color: rgba($color-black, 0.7);
        display: block;
        opacity: 0;
        visibility: hidden;
        will-change: opacity;
        z-index: 9999;

        &.is-open {
            opacity: 1;
            visibility: visible;
        }
    }
}

.main-nav {
    $this: &;

    height: 100%;
    padding: 0 15px;

    @include breakpoint(medium down) {
        padding: 0;
    }

    &__toggle {
        @include breakpoint(medium down) {
            display: flex;
            justify-content: flex-end;
        }
    }

    &__container {
        height: 100%;

        @include breakpoint(medium down) {
            @include trs;
            @include fixed(0, 0);
            @include size(100%, 100vh);
            background-color: $color-white;
            overflow-y: auto;
            transform: translateX(100%);
            will-change: transform;
            z-index: 10000;

            &.is-open {
                transform: translateX(0);
            }

            .js-fixed-el & {
                height: 100vh;
            }
        }
    }

    &__top-components {
        display: none;

        @include breakpoint(medium down) {
            display: flex;
            flex-wrap: wrap;
            margin: 30px 30px 15px;
        }

        @include breakpoint(small down) {
            margin: 20px 20px 10px;
        }
    }

    &__close-wrap {
        margin: 20px 30px 20px auto;

        @include breakpoint(medium down) {
            margin: 0 0 0 auto;
        }
    }

    &__close-submenu {
        @extend %button;
        @extend %button-style-only-icon;
        @include absolute(0, calc(50% - 690px));
        @include size($btn-height-small);
        font-size: 1.2rem;
        min-height: $btn-height-small;

        @include media-max(1420) {
            right: calc(50% - 620px);
        }
    }

    &__nav,
    &__block {
        height: 100%;

        @include breakpoint(medium down) {
            height: auto;
        }
    }

    &__nav {
        @include breakpoint(medium down) {
            padding: 0 90px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    &__nav-dropdown[class] {
        display: none;
        width: 100%;

        .is-open > & {
            display: block;
        }

        &.is-level-1 {
            @include breakpoint(large only) {
                @include absolute(100%, null, null, 0);
                background-color: $color-white;
                border-top: 1px solid $color-3--3;
                box-shadow: 0 20px 20px rgba($color-black, 0.16);
                flex-wrap: wrap;
                padding: 45px 0;
            }

            @include breakpoint(medium down) {
                padding-top: 10px;
            }

            @include breakpoint(small down) {
                padding-top: 20px;
            }

            #{$this}__nav-item-parent {
                margin: 2px auto 42px;
                max-width: 1100px;
                position: relative;
                width: 100%;

                @include breakpoint(medium down) {
                    display: none;
                }

                > #{$this}__nav-link {
                    color: var(--color-1--1);
                    display: inline;
                    flex-grow: 0;
                    font-family: var(--typo-1);
                    font-size: 1.6rem;
                    font-weight: 500;
                    padding-left: 23px;
                    text-decoration: underline;
                    text-transform: none;
                    width: auto;

                    @include on-event {
                        text-decoration: none;
                    }

                    @include fa-icon-style(false) {
                        @include absolute(3px, null, null, 0);
                    }
                }
            }

            > #{$this}__nav-dropdown-list {
                display: flex;
                flex-wrap: wrap;
                margin: 0 auto;
                max-height: calc(100vh - 500px);
                max-width: 1124px;
                overflow-y: auto;
                padding-top: 5px;

                @include breakpoint(medium down) {
                    margin: 0 0 0 77px;
                    max-height: 10000px;
                    max-width: none;
                    padding: 0;
                }

                @include breakpoint(small down) {
                    margin: 0 0 0 10px;
                }

                > #{$this}__nav-item {
                    padding: 0 12px 40px;
                    width: calc(100% / 3);

                    @include breakpoint(medium down) {
                        padding: 0 0 5px;
                        width: 100%;
                    }

                    @include breakpoint(small down) {
                        padding: 0 0 20px;
                    }
                }
            }
        }

        &.is-level-2 {
            @include breakpoint(large only) {
                display: block;
            }

            @include breakpoint(medium down) {
                padding-top: 10px;
            }

            @include breakpoint(small down) {
                padding-top: 30px;
            }

            > #{$this}__nav-dropdown-list {
                @include breakpoint(medium down) {
                    margin-left: 63px;
                }

                @include breakpoint(small down) {
                    margin-left: 30px;
                }

                > #{$this}__nav-item {
                    @include breakpoint(medium down) {
                        padding-bottom: 10px;
                    }
                }
            }
        }
    }

    /* stylelint-disable */
    &__nav-list {
        @include breakpoint(large only) {
            align-items: center;
            display: flex;
            flex-wrap: wrap;
            height: 100%;
            justify-content: flex-end;

            > #{$this}__nav-item {
                height: 45px;

                > ul {
                    opacity: 0;
                    transform: translateY(5%);
                }

                &.is-open {
                    &::before,
                    &::after {
                        @include triangle("top", $color-3--3, 25px, 10px);
                        @include absolute(100%, null, null, null);
                        content: "";
                        transform: translate(20px, -100%);
                    }

                    &::after {
                        border-bottom-color: $color-white;
                        top: calc(100% + 1px);
                    }

                    > ul {
                        max-height: 480px;
                        opacity: 1;
                        overflow-y: auto;
                        transform: none;
                    }

                    > #{$this}__nav-item-actions {
                        > #{$this}__nav-link,
                        > #{$this}__nav-toggle {
                            #{$this}__nav-link-text,
                            #{$this}__nav-toggle-text {
                                &::after {
                                    background-color: var(--color-1--1);
                                }
                            }
                        }
                    }
                }

                &.has-dropdown {
                    > #{$this}__nav-item-actions {
                        > #{$this}__nav-link {
                            display: none;
                        }
                    }
                }

                > #{$this}__nav-item-actions {
                    height: 100%;
                    margin: 0;
                    padding: 0;

                    > #{$this}__nav-link,
                    > #{$this}__nav-toggle {
                        @include font(var(--typo-1), 1.5rem, var(--fw-bold));
                        align-items: center;
                        color: var(--color-1--2);
                        display: flex;
                        height: 100%;
                        justify-content: center;
                        overflow: visible; // Fix for IE
                        padding: 5px 20px;
                        position: relative;
                        text-decoration: none;
                        text-transform: uppercase;

                        &::after {
                            @include trs;
                            @include size(1px, 31px);
                            @include absolute(50%, -1px, null, null);
                            background-color: var(--color-1--2);
                            content: "";
                            opacity: 1;
                            transform: translateY(-50%);
                        }

                        @include on-event {
                            color: var(--color-1--1);

                            &::after {
                                background-color: var(--color-1--2);
                            }
                            #{$this}__nav-link-text,
                            #{$this}__nav-toggle-text {
                                &::after {
                                    background-color: var(--color-1--1);
                                }
                            }
                        }

                        .header:not(.js-fixed-el) & {
                            body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
                            body:not(.is-mnv-opened).home-page &,
                            body:not(.is-mnv-opened).home-hospital-page & {
                                color: $color-white;

                                &::after {
                                    background-color: $color-white;
                                }

                                @include on-event {
                                    #{$this}__nav-link-text,
                                    #{$this}__nav-toggle-text {
                                        &::after {
                                            background-color: $color-white;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    #{$this}__nav-link-text,
                    #{$this}__nav-toggle-text {
                        position: relative;

                        &::after {
                            @include trs;
                            @include absolute(null, 0, -4px, 0);
                            @include size(100%, 4px);
                            background-color: transparent;
                            content: "";
                        }
                    }
                }
            }
        }

        @include breakpoint(medium down) {
            > #{$this}__nav-item {
                padding-bottom: 10px;
            }
        }

        @include breakpoint(small down) {
            > #{$this}__nav-item {
                padding-bottom: 30px;
            }
        }
    }

    /* stylelint-enable */

    &__nav-item-actions {
        display: flex;

        @include breakpoint(medium down) {
            align-items: center;
            min-height: 45px;
            position: relative;
        }

        .is-level-2 > #{$this}__nav-dropdown-list > #{$this}__nav-item > & {
            margin: 0 0 10px;

            @include breakpoint(medium down) {
                margin: 0;
                min-height: auto;
            }
        }

        @include fa-icon-style(false) {
            align-self: flex-start;
            color: var(--color-1--2);
            font-size: 1.2rem;
            font-weight: 400;
            margin: 3px 14px 3px 0;

            @include breakpoint(large) {
                display: none;
            }
        }
    }

    &__nav-link {
        @include trs;
        @include font(var(--typo-1), 1.8rem, 400);
        color: var(--color-1--2);
        display: block;
        position: relative;
        text-decoration: none;
        text-transform: uppercase;
        width: auto;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            font-weight: 900;
            line-height: 1;
            margin-left: 78px;
            text-transform: none;
        }

        @include breakpoint(small down) {
            margin-left: 58px;
            word-break: break-word;
        }

        &[href] {
            @include on-event {
                color: var(--color-1--1);
                text-decoration: underline;
            }

            &:focus {
                outline-offset: 3px;
            }
        }

        + #{$this}__nav-toggle {
            @include breakpoint(medium down) {
                margin-right: -45px;
            }
        }

        .is-level-1 > #{$this}__nav-dropdown-list > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            color: var(--color-1--2);
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 0.75px;
            margin-bottom: 20px;
            padding-bottom: 7px;
            position: relative;

            @include breakpoint(medium down) {
                color: var(--color-1--2);
                font-size: 2.2rem;
                font-weight: 700;
                letter-spacing: normal;
                margin: 0 0 0 58px;
                padding: 0;
            }

            &[href]::before {
                @include absolute(null, null, 0, 0);
                @include size(35px, 4px);
                background-color: var(--color-2--1);
                content: "";

                @include breakpoint(medium down) {
                    content: none;
                }
            }

            + #{$this}__nav-toggle {
                @include breakpoint(medium down) {
                    margin-right: -45px;
                }
            }
        }

        .is-level-2 > #{$this}__nav-dropdown-list > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            font-size: 1.3rem;
            text-transform: none;

            @include breakpoint(medium down) {
                font-size: 1.7rem;
                font-weight: 400;
                margin: 0;
            }

            @include breakpoint(small down) {
                line-height: 1.25;
            }
        }

        #{$this}__nav-item.is-open > #{$this}__nav-item-actions > &,
        #{$this}__nav-dropdown-list > #{$this}__nav-item.is-open > #{$this}__nav-item-actions > & {
            @include breakpoint(medium down) {
                color: var(--color-1--1);
            }
        }

        [data-has-current] > #{$this}__nav-item-actions > & {
            text-decoration: underline;

            @include breakpoint(large) {
                color: var(--color-1--1);
            }
        }
    }

    &__nav-toggle {
        @include trs;
        background: none;
        border: 0;
        cursor: pointer;
        display: none;

        @include breakpoint(medium down) {
            @include size(45px);
            background-color: var(--color-1--1);
            border: 1px solid var(--color-1--1);
            color: $color-white;
            display: block;
            flex-shrink: 0;
            order: -1;
            padding: 4px;
            position: relative;

            @include on-event {
                background-color: var(--color-2--1);
                border-color: var(--color-2--1);
                color: var(--color-1--2);

                .is-level-1 & {
                    background-color: transparent;
                    color: var(--color-2--1);
                }
            }

            .is-open > #{$this}__nav-item-actions > & {
                background-color: var(--color-2--1);
                border-color: var(--color-2--1);
                color: var(--color-1--2);
                transform: scaleY(-1);

                .is-level-1 & {
                    background-color: transparent;
                    color: var(--color-2--1);
                }
            }

            .is-level-1 & {
                background-color: transparent;
                color: var(--color-1--1);
            }
        }
    }

    &__nav-toggle-icon {
        @include icon-after($fa-var-angle-down);
        @include abs-center;
        color: inherit;
        font-size: 1.2rem;
        font-weight: 400;

        @include breakpoint(large) {
            display: none;
        }
    }

    &__nav-toggle-text {
        @include breakpoint(medium down) {
            @include size(0);
            opacity: 0;
            overflow: hidden;
            position: absolute;
        }
    }

    &__lang {
        @include breakpoint(large) {
            display: none;
        }

        @include breakpoint(medium down) {
            @include absolute(25px, null, null, 81px);
        }

        @include breakpoint(small down) {
            left: 11px;
            top: 20px;
        }

        .lang {
            color: var(--color-1--1);
        }
    }
}
