{%- from 'views/core-components/list.njk' import List -%}

{#
    FaqItem template.
#}

{%- macro FaqItem(tag = 'dl', itemClass = '') -%}
    <{{ tag }} class="faq-item {{ itemClass }}">
    <dt class="faq-item__toggle-wrapper">
        <button type="button" class="faq-item__toggle">
            <span class="far fa-angle-down faq-item__toggle-icon" aria-hidden="true"></span>
            <span class="faq-item__toggle-category">{{ lorem(1, 'words') }}</span>
            <span class="faq-item__toggle-text">{{ lorem(2) }}</span>
        </button>
    </dt>
    <dd class="faq-item__block rte" aria-hidden="true">
        <div class="faq-item__wrapper">
            <p class="faq-item__response">Réponse :</p>
            <p class="faq-item__text">{{ lorem(5) }}</p>
        </div>
    </dd>
    </{{ tag }}>
{%- endmacro -%}

{#
    FaqList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
#}
{%- macro FaqList(
    itemClass = 'list__item has-mb-1',
    count = 12
) -%}
    <dl class="list is-columns-1">
        {% for item in range(0, count) %}
            {{ FaqItem(tag = 'div', itemClass = itemClass) }}
        {% endfor %}
    </dl>
{%- endmacro -%}
