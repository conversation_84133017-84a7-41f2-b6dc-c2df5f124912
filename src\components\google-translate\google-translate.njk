{% from 'views/utils/utils.njk' import getImagePath %}

{% set defaultLangs = [
    {
        code: 'fr',
        title: 'Français',
        arialive: 'Site traduit en français'
    },
    {
        code: 'en',
        title: 'English',
        arialive: 'Website translated into english'
    },
    {
        code: 'de',
        title: 'Deutsch',
        arialive: 'Website ins Deutsche übersetzt'
    },
    {
        code: 'it',
        title: 'Italiano',
        arialive: 'Sito web tradotto in italiano'
    },
    {
        code: 'pt',
        title: 'Português',
        arialive: 'Website traduzido para português'
    },
    {
        code: 'es',
        title: 'Español',
        arialive: 'Sitio web traducido al español'
    },
    {
        code: 'ru',
        title: 'Русский',
        arialive: 'Веб-сайт переведен на русский язык'
    },
    {
        code: 'pl',
        title: '<PERSON><PERSON>',
        arialive: 'Strona internetowa przetłumaczona na język polski'
    }
] %}

{#
    GoogleTranslate template.
    @param {object[]} languages - languages array.
#}
{%- macro GoogleTranslate(languages = defaultLangs) -%}
    <!-- GTranslate: https://gtranslate.io/ -->
    <div class="g-translate" data-iframe-title="Google traduction">
        <div class="g-translate-dropdown js-dropdown">
            <button type="button" class="g-translate-dropdown__toggle js-dropdown-toggle js-tooltip" data-content="Sélectionner la langue du site">
                <span class="g-translate-dropdown__toggle-info-sr sr-only" aria-live="polite"></span>
                <span class="g-translate-dropdown__toggle-flag notranslate" lang="fr">fr</span>
                <span class="g-translate-dropdown__toggle-text">Sélectionner la langue du site</span>
                <span class="g-translate-dropdown__toggle-icon" aria-hidden="true"></span>
            </button>
            <div class="g-translate-dropdown__block js-dropdown-block">
                <ul class="g-translate-langs">
                    {%- for lang in languages -%}
                        <li class="g-translate-langs__item notranslate">
                            <button type="button"
                                    data-arialive="{{ lang.arialive }}"
                                    data-lang="{{ lang.code }}"
                                    title="{{ lang.title }}"
                                    onclick="changeLanguage(this, 'fr|{{ lang.code }}')"
                                    class="g-translate-langs__button"
                                    aria-label="{{ lang.title }}"
                                    lang="{{ lang.code }}"
                            >
                                {{- lang.code -}}
                            </button>
                        </li>
                    {%- endfor -%}
                </ul>
            </div>
        </div>
    </div>

    <div id="google_translate_element2" hidden></div>
    {{ GoogleTranslateScripts() }}
{%- endmacro -%}

{% macro GoogleTranslateScripts() %}
    <script type="text/javascript">

        function skipTranslateIframeHandler() {
            setTimeout(function() {
                var gTranslateEl = document.querySelector('.g-translate');
                var title = gTranslateEl.getAttribute('data-iframe-title');
                var iframe = document.querySelector('iframe.skiptranslate');

                if (iframe) {
                    iframe.removeAttribute('frameborder');
                    iframe.setAttribute('title', title);
                }
            }, 1000);
        }

        document.addEventListener('DOMContentLoaded', function () {
            var gTranslate = document.querySelector('.g-translate-dropdown');

            if (gTranslate) {
                var cookies = document.cookie.split(';');
                var gTranslateButtons = [].slice.call(gTranslate.querySelectorAll('.g-translate-langs__button'));

                cookies.forEach(function (cookie) {
                    if (cookie.indexOf('googtrans=') > -1) {
                        var lang = cookie.trim().slice(-2);

                        gTranslateButtons.forEach(function (button) {
                            if (button.getAttribute('data-lang') === lang) {
                                // Add timeout for IE
                                setTimeout(function() {
                                    if (lang !== 'fr') {
                                        button.click();
                                    }
                                }, 250);
                            }
                        });
                    }
                });
            }

            skipTranslateIframeHandler();
        });

        function changeLanguage(element, lang) {
            var jqElement = $(element);

            var gTranslate = jqElement.parents('.g-translate');
            var toggle = gTranslate.find('.g-translate-dropdown__toggle-text');
            var flag = gTranslate.find('.g-translate-dropdown__toggle-flag');
            var infoSr = gTranslate.find('.g-translate-dropdown__toggle-info-sr');
            var ariaLive = jqElement.data('arialive');
            var langCode = jqElement.data('lang');

            toggle
                .html(
                    jqElement
                        .find('span[aria-hidden]')
                        .html()
                );

            flag
                .attr('lang', langCode)
                .html(langCode);

            infoSr.html(ariaLive);

            if (doGTranslate) {
                doGTranslate(lang);
            }

            skipTranslateIframeHandler();
        }

        function googleTranslateElementInit2() {
            new google.translate.TranslateElement({
                pageLanguage: 'fr',
                autoDisplay: false
            }, 'google_translate_element2');
        }
    </script>
    <script type="text/javascript" src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit2" async></script>
    <script type="text/javascript">
        /* <![CDATA[ */
        eval(function (p, a, c, k, e, r) {
            e = function (c) {
                return (c < a ? '' : e(parseInt(c / a))) + ((c = c % a) > 35 ? String.fromCharCode(c + 29) : c.toString(36));
            };
            if (!''.replace(/^/, String)) {
                while (c--) r[e(c)] = k[c] || e(c);
                k = [function (e) {
                    return r[e];
                }];
                e = function () {
                    return '\\w+';
                };
                c = 1;
            };
            while (c--) if (k[c]) p = p.replace(new RegExp('\\b' + e(c) + '\\b', 'g'), k[c]);
            return p;
        }('6 7(a,b){n{4(2.9){3 c=2.9("o");c.p(b,f,f);a.q(c)}g{3 c=2.r();a.s(\'t\'+b,c)}}u(e){}}6 h(a){4(a.8)a=a.8;4(a==\'\')v;3 b=a.w(\'|\')[1];3 c;3 d=2.x(\'y\');z(3 i=0;i<d.5;i++)4(d[i].A==\'B-C-D\')c=d[i];4(2.j(\'k\')==E||2.j(\'k\').l.5==0||c.5==0||c.l.5==0){F(6(){h(a)},G)}g{c.8=b;7(c,\'m\');7(c,\'m\')}}', 43, 43, '||document|var|if|length|function|GTranslateFireEvent|value|createEvent||||||true|else|doGTranslate||getElementById|google_translate_element2|innerHTML|change|try|HTMLEvents|initEvent|dispatchEvent|createEventObject|fireEvent|on|catch|return|split|getElementsByTagName|select|for|className|goog|te|combo|null|setTimeout|500'.split('|'), 0, {}));
        /* ]]> */
    </script>
{% endmacro %}
