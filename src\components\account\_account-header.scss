.account-header {
    margin: 35px 0 87px;

    @include breakpoint(medium down) {
        margin-bottom: 60px;
    }

    @include breakpoint(small down) {
        margin-bottom: 25px;
    }

    &__wrapper {
        @extend %container;
        align-items: center;
        display: flex;

        @include breakpoint(medium down) {
            flex-direction: column;
            justify-content: flex-start;
            padding: 0 40px;
        }
    }

    &__info {
        padding-left: 24px;

        @include breakpoint(medium down) {
            padding-left: 0;
            padding-top: 24px;
        }
    }

    &__title {
        @include font(null, 4.5rem, var(--fw-bold));
        line-height: 1.25;
        margin-bottom: 30px;

        @include breakpoint(medium down) {
            font-size: 3.5rem;
            text-align: center;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
        }

        strong {
            color: var(--color-1--1);
        }
    }

    &__description {
        color: $color-black;
        font-family: var(--typo-1);
        font-size: 2rem;
        letter-spacing: 0;
        line-height: 3.4rem;
        max-width: 636px;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
            line-height: 3rem;
            max-width: none;
            text-align: start;
        }
    }

    &__picture {
        border-radius: 10px;
        display: block;
        position: relative;
        z-index: -1;

        @include breakpoint(medium down) {
            height: 211px;
            width: 316px;
        }

        img {
            border-radius: 10px;
        }
    }
}
