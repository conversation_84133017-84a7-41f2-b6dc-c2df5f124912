.logo {
    color: var(--color-1--1);
    display: inline-block;

    &__image {
        align-content: center;
        align-items: center;
        display: flex;

        img {
            max-height: 100%;

            @include breakpoint(medium down) {
                width: 160px;
            }

            @include breakpoint(small down) {
                width: 129px;
            }
        }
    }

    &__image-default {
        display: none;
        width: 200px;

        .js-fixed-el &,
        .is-mnv-opened & {
            display: block;
        }
    }

    &__image-secondary {
        display: block;

        .js-fixed-el &,
        .is-mnv-opened & {
            display: none;
        }
    }
}

a.logo {
    @include focus-outline($color: currentColor, $offset: 4px);
}
