{%- from 'views/core-components/click-and-roll.njk' import ClickAndRoll -%}
{%- from 'views/utils/utils.njk' import stratisImage -%}
{%- from 'views/core-components/image.njk' import ImageExpander -%}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleWidget -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    MediaElement template.
    @param {object} userSettings - media element custom params.
#}
{% macro MediaElement(userSettings = {}) %}
    {%- set settings = Helpers.merge({
        element: 'audio',
        title: lorem(1),
        iframe: {
            width: 996,
            height: 560
        },
        src: 'media/audio.mp3 ',
        buttonText: 'Transcription',
        text: lorem(10),
        className: '',
        link: false
    }, userSettings)  %}

    <section class="section media-element {{ settings.className }}">
        {%- if settings.title %}
        <div class="section__title">
            {{ TitleRTE(
                text = lorem(1)
            ) }}
        </div>
        {%- endif %}
        <div class="media-element__wrapper">
            {% if settings.element !== 'audio' and settings.element !== 'video' %}
                <img
                        class="media-element__ratio lazy lazyload"
                        data-src="{{ stratisImage('996x560') }}"
                        src="{{ ImageExpander('996', '560') }}"
                        width="996"
                        height="560"
                        alt=""
                >
            {% endif %}

            {%- if settings.element === 'audio' %}
                <audio data-src={{ settings.src }} controls preload="none" class="lazy"></audio>
            {%- elif settings.element === 'video' %}
                <video controls preload="none" poster="{{ stratisImage('996x560') }}" width="996" height="560" class="lazy">
                    <source data-src="{{ settings.src }}" type='video/mp4; codecs="avc1.42E01E, mp4a.40.2"'>
                    <track label="English subtitles" kind="subtitles" srclang="en" src="media/sub-example.vtt" default>
                </video>
            {%- elif settings.element === 'dailymotion' %}
                <div class="iframe js-dailymotion-player-stratis" data-iframe-title="Dailymotion iframe title" videoID="{{ settings.src }}" showinfo="1" autoplay="0"></div>
            {%- elif settings.element === 'vimeo' %}
                <div class="iframe js-vimeo-player-stratis" data-iframe-title="Vimeo iframe title" videoID="{{ settings.src }}"></div>
            {%- elif settings.element === 'youtube' %}
                <div class="iframe js-youtube-player-stratis" data-iframe-title="Youtube iframe title" videoID="{{ settings.src }}" theme="dark" rel="1" controls="1" showinfo="1" autoplay="0"></div>
            {%- else %}
                <iframe class="iframe" loading="lazy" title="Iframe title" src="{{ settings.src }}" ></iframe>
            {%- endif %}
        </div>
        {%- if settings.link %}
            <a href="#" class="media-element__link"> Télécharger la version texte de ce vidéo </a>
        {%- endif %}
        {%- if settings.text and settings.element !== 'external' %}
            {%- call ClickAndRoll({
                toggle: {
                    wrapperTag: 'div',
                    text: settings.buttonText
                },
                dropdown: {
                    className: 'click-and-roll__block'
                }
            }) -%}
                <div class="rte">
                    <p>{{ settings.text }}</p>
                </div>
            {%- endcall -%}
        {%- endif %}
    </section>
{% endmacro %}

{#
    MediaElementSG template.
    MediaElement template for styleguide.
#}
{%- macro MediaElementSG() -%}
    {% call SG.Section('media-elements') %}
        <h2 class="styleguide-section__title">Media elements</h2>
        {%- call SG.Preview() -%}
            {{ MediaElement({ element: 'youtube', src: 'bM7SZ5SBzyY', title: 'Lorem ipsum sit amet consectur Video title ' }) }}
        {%- endcall -%}

        {%- call SG.Preview() -%}
            {{ MediaElement({ element: 'audio', title: 'Lorem ipsum sit amet consectur Player audio title ' }) }}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

