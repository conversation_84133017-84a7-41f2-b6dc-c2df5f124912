.deliberations-list-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    flex-direction: column;

    @include breakpoint(medium down) {
        flex-direction: row;
        justify-content: flex-end;
    }

    &__actions {
        margin-top: 10px;

        .telecharger-item {
            &__meta {
                color: $color-3--4;
            }
        }

        .document-actions {
            &__link {
                @include min-size(50px);
                border: 1px solid $color-3--4;
                color: $color-3--4;
                padding: 12px;
            }

            &__link-text {
                display: none;
            }

            &__item {
                padding: 2.5px 4px;
            }
        }
    }

    &__image {
        display: inline-block;
        flex-shrink: 0;
        margin-left: 20px;
        position: relative;
        width: max-content;

        @include breakpoint(medium down) {
            margin: 0;
        }

        @include breakpoint(small down) {
            max-width: 101px;
        }

        img {
            box-shadow: 0 0 20px rgba($color-black, 0.16);

            @include breakpoint(medium down) {
                @include size(100%, auto);
                max-height: 178px;
                max-width: 250px;
            }

            @include breakpoint(small down) {
                max-height: 101px;
                max-width: 101px;
            }
        }
    }

    &__content {
        flex-grow: 1;
        padding: 22px 0 0;

        @include breakpoint(medium down) {
            max-width: 59%;
            padding: 30px 0 0 40px;
        }

        @include breakpoint(small down) {
            max-width: 66%;
            padding: 0 0 0 10px;
        }
    }

    &__category {
        margin-bottom: 13px;
    }

    &__title {
        max-width: 100%; //For IE
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &::after {
            height: 70%;
        }
    }

    &__subtitle {
        @include font(null, 1.8rem, var(--fw-medium));
        display: block;
        margin: 5px 0 0;

        @include breakpoint(small down) {
            font-size: 1.6rem;
        }
    }

    &__publication[class] {
        margin-top: 20px;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__status {
        margin: 20px 0 0;
    }

    .is-pablic-market & {
        #{$this}__image {
            @include breakpoint(medium down) {
                @include size(126px, auto);
            }

            @include breakpoint(small down) {
                @include size(71px, auto);
            }
        }

        #{$this}__content {
            @include breakpoint(medium down) {
                max-width: none;
                padding: 0 0 0 38px;
            }

            @include breakpoint(small down) {
                padding: 0 0 0 20px;
            }
        }
    }
}
