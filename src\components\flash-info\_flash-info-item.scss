// FLASH INFO
.flash-info-item {
    $this: &;

    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    width: 100%;

    @include breakpoint(medium down) {
        align-items: flex-start;
        flex-direction: column;
    }

    + .flash-info-item:not(.is-popup &) {
        margin-top: 30px;
    }

    &__title-main {
        align-items: center;
        align-self: flex-start;
        display: flex;
        flex-shrink: 0;
        gap: 18px;
        margin: 0 0 30px;
        width: 100%;

        @include breakpoint(medium down) {
            margin: 0 0 25px;
            width: 100%;
        }

        @include breakpoint(small down) {
            align-items: center;
            border: 0;
            display: flex;
            padding-right: 15px;
        }
    }

    &__title-main-text {
        @include font(var(--typo-1), 3.5rem, var(--fw-normal));
        color: var(--color-1--1);
        hyphens: auto;
        line-height: 3.8rem;
        overflow-wrap: break-word;
        word-wrap: break-word;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            hyphens: none;
            line-height: 3.2rem;
            overflow-wrap: normal;
            word-wrap: normal;
        }
    }

    &__title-main-svg {
        @include size(60px);
        display: block;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(42px);
        }

        svg {
            @include size(100%);
            display: block;
            fill: var(--color-1--1);
        }
    }

    &__wrapper {
        display: flex;
        margin-right: auto;

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0;
            width: 100%;
        }
    }

    &__image {
        display: block;
        flex-shrink: 0;
        margin-right: 20px;

        @include breakpoint(small down) {
            margin: 0 0 20px;
        }

        img {
            display: block;
        }
    }

    &__content {
        flex: 1 0 auto;
        margin: 0;
        padding: 4px 0 0;
        width: 1%;

        @include breakpoint(small down) {
            width: auto;
        }
    }

    &__title {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 2.2rem;
        margin: 0 0 5px;
    }

    &__teaser {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        line-height: 1.8rem;
        margin: 0;
    }

    &__button {
        display: flex;
        justify-content: end;

        @include breakpoint(medium down) {
            margin-top: 25px;
        }

        @include breakpoint(small down) {
            justify-content: center;
        }
    }

    &__link[class] {
        flex-shrink: 0;
        font-weight: var(--fw-normal);
        gap: 9px;
        margin-right: 65px;
        min-width: 190px;

        @include breakpoint(medium down) {
            width: auto;
        }

        @include breakpoint(small down) {
            margin: 0;
        }

        @include fa-icon-style(false) {
            font-size: 1.6rem;
        }

        .btn__text {
            margin: 0;
        }
    }

    &__close {
        @include trs;
        @include size(40px);
        align-items: center;
        background: none;
        border: 0;
        border-radius: 4px;
        color: var(--color-1--1);
        cursor: pointer;
        display: flex;
        flex-shrink: 0;
        font-size: 3rem;
        justify-content: center;
        margin: 0;
        margin-left: 57px;
        position: relative;
        text-decoration: none;

        @include fa-icon-style {
            color: inherit;
        }

        @include breakpoint(medium down) {
            @include absolute(0, 0, null, null);
            margin: 0;
        }

        @include breakpoint(small down) {
            @include size(30px);
            font-size: 2.4rem;
            top: -15px;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include absolute(21px, 14px, null, null);

        @include breakpoint(medium down) {
            right: 10px;
            top: 5px;
        }

        @include breakpoint(small down) {
            right: 5px;
        }
    }

    .is-popup & {
        padding: 25px 53px 25px 27px;

        @include breakpoint(medium down) {
            padding: 25px;
        }

        #{$this}__wrapper {
            align-items: flex-start;
        }

        #{$this}__title-main {
            @include breakpoint(medium only) {
                justify-content: flex-start;
            }
        }

        #{$this}__title-main-text {
            @include focus-outline;
            max-width: none;

            @include breakpoint(small down) {
                font-size: 2.8rem;
                line-height: 3.2rem;
            }
        }

        #{$this}__title-main-svg {
            @include size(47px);

            @include breakpoint(small down) {
                @include size(43px);
            }
        }

        #{$this}__image {
            margin-right: 30px;

            @include breakpoint(medium down) {
                margin-right: 13px;
                max-width: 110px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 20px;
            }
        }

        #{$this}__content {
            padding: 0;

            @include breakpoint(small down) {
                align-items: center;
                display: flex;
                flex-direction: column;
            }
        }

        #{$this}__title {
            font-size: 2.4rem;
            line-height: 2.8rem;
            margin: 0 0 15px;

            @include breakpoint(medium down) {
                font-size: 2.2rem;
                line-height: 2.4rem;
            }

            @include breakpoint(small down) {
                font-size: 1.8rem;
                line-height: 2.2rem;
                margin: 0 0 5px;
                text-align: center;
            }
        }

        #{$this}__teaser {
            font-size: 1.7rem;
            line-height: 2.4rem;

            @include breakpoint(small down) {
                font-size: 1.4rem;
                line-height: 1.8rem;
                text-align: center;
            }
        }

        #{$this}__link {
            margin: 30px 0 0;
            width: auto;
        }
    }
}
