.travaux-block {
    position: relative;

    &__wrapper {
        display: flex;
    }

    &__container {
        padding: 45px 0;
        width: calc(100% - 200px);

        @include breakpoint(medium down) {
            width: calc(100% - 164px);
        }

        @include breakpoint(small down) {
            width: calc(100% - 40px);
        }
    }

    &__control {
        @include absolute(50%, null, null, null);
        @include font(null, 2rem, var(--fw-normal));
        @include size(50px);
        background: none;
        border: 0;
        color: $color-black;
        cursor: pointer;
        line-height: 0.9;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);

        @include breakpoint(medium down) {
            top: 35%;
            transform: none;
        }

        @include breakpoint(small down) {
            bottom: 20px;
            font-size: 1.4rem;
            top: auto;
        }

        @include on-event {
            background-color: $color-black;
            color: $color-white;
        }

        &.is-prev {
            font-size: 4rem;
            left: 20px;

            @include breakpoint(small down) {
                left: 0;
            }
        }

        &.is-next {
            font-size: 4rem;
            right: 20px;

            @include breakpoint(small down) {
                right: 0;
            }
        }

        .is-inverted & {
            color: $color-white;

            @include on-event {
                background-color: $color-white;
                color: var(--color-1--2);
            }
        }
    }

    &__pagination {
        @include absolute(null, null, 30px, 50%);
        display: flex;
        transform: translateX(-50%);
        z-index: 2;

        @include breakpoint(small down) {
            justify-content: center;
            margin: 37px auto 0;
            text-align: center;
        }

        .swiper-pagination {
            &__bullet {
                @include size(10px);
                background-color: transparent;
                border: 1px solid $color-3--4;
                border-radius: 5px;
                margin-right: 6px;
                position: relative;

                &.is-active {
                    background-color: var(--color-1--1);
                    width: 26px;
                }
            }

            &__bullet-btn {
                @include absolute();
                @include size(10px);
                border-radius: 5px;
                opacity: 0;
                padding: 0;
            }
        }
    }
}

.travaux {
    @include breakpoint(small down) {
        width: 100%;
    }
}
