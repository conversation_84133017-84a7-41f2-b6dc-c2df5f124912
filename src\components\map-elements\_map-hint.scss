.map-hint {
    &__toggle {
        @include trs($prop: opacity);
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        @extend %button;
        @extend %button-size-small;
        @extend %button-style-primary;
        color: var(--color-2--1);
        display: flex;
        justify-content: center;
        margin: 0 5px;
        min-width: 45px;
        padding: 0;

        .single-event-page & {
            border-radius: 0;
        }

        .is-popup-open & {
            @include breakpoint(small down) {
                display: none;
            }
        }

        @include fa-icon-style {
            @include font(var(--typo-1), 2.6rem, var(--fw-light));
            margin-left: 0;
        }
    }

    &__block[class] {
        @include absolute(0, 55px, null, unset);
        background-color: $color-white;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.16);
        min-width: 280px;
        padding: 10px 16px;
        z-index: 100;

        &::after {
            @include triangle("right", $color-white, 10px, 22px);
            @include absolute(11px, -10px, null, null);
            content: "";
        }

        .map-localiser &,
        .map-detail & {
            @include absolute(0, auto, null, 55px);

            &::after {
                @include triangle("left", $color-white, 10px, 22px);
                @include absolute(11px, auto, null, -10px);
            }
        }

        @include breakpoint(medium down) {
            min-width: 232px;
        }
    }

    &__text {
        font-size: 1.4rem;

        @include breakpoint(medium down) {
            font-size: 1.2rem;
        }
    }
}
