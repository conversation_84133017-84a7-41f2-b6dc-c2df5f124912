/*
 * @name main-nav-aside.
 */
body.mnv-opened {
    max-height: 1000vh;
    overflow: hidden;
}

.main-nav-overlay {
    @include trs;
    @include fixed(0, 0, 0, 0);
    @include size(100%, 100vh);
    background: $color-white;
    opacity: 0;
    visibility: hidden;
    will-change: opacity;
    z-index: 9999;

    &.is-open {
        opacity: 1;
        visibility: visible;
    }
}

.main-nav {
    $this: &;

    &__toggle {
        @include absolute(0, 0);
        z-index: 22;
    }

    &__search {
        border-radius: 40px;
        box-shadow: 0 0 16px #00000024;
        margin-bottom: 72px;

        @include breakpoint(medium down) {
            margin-bottom: 28px;
        }

        @include breakpoint(small down) {
            margin-bottom: 33px;
        }
    }

    &__container {
        @include trs;
        @include fixed(0, 0, null, 0);
        @include size(100%, 100vh);
        margin: 0 auto;
        overflow-x: hidden;
        overflow-y: auto;
        transform: translateX(120%);
        visibility: hidden;
        will-change: transform;
        z-index: 10000;

        &.is-open {
            transform: translateX(0);
            visibility: visible;
        }
    }

    &__nav-list {
        > .main-nav__nav-item {
            margin-bottom: 21px;
            padding: 0 21px 21px;
            position: relative;
    
            &::after {
                @include absolute(null, null, 0, 0);
                @include size(100%, 1px);
                background-color: $color-3--3;
                content: '';
            }

            &:last-child {
                &::after {
                    content: none;
                }
            }
        }
    }

    &__block {
        align-items: stretch;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        max-width: 1040px;
        min-height: 100%;

        @include breakpoint(medium down) {
            align-items: center;
            flex-direction: column;
            height: 100%;
        }
    }

    &__left {
        flex-grow: 1;
        max-width: 486px;
        padding: 85px 10px 40px 42px;

        @include breakpoint(medium down) {
            margin: 0 auto;
            max-width: 480px;
            padding: 120px 0 44px;
            width: 630px;
        }

        @include breakpoint(small down) {
            max-width: 320px;
            padding: 98px 0 75px;
            width: 100%;
        }
    }

    &__right {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        flex-wrap: wrap;
        max-width: 350px;
        padding: 165px 0 40px 60px;
        position: relative;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-grow: 1;
            max-width: none;
            padding: 44px 0 68px;
            width: 480px;
        }

        @include breakpoint(small down) {
            padding: 29px 0 126px;
            width: auto;
        }

        &::before {
            @include absolute(0, null, 0, 0);
            background-color: $color-3--1;
            content: '';
            min-height: 100vh;
            width: 1000vh;
            z-index: -1;

            @include breakpoint(medium down) {
                min-height: 100%;
                transform: translateX(-50%);
            }
        }
    }

    &__close-wrap {
        @include fixed(35px, 45px, null, null);
        margin: 0;

        @include breakpoint(medium down) {
            right: 30px;
            top: 30px;
        }

        @include breakpoint(small down) {
            right: 20px;
            top: 12px;
        }

        .btn {
            flex-direction: row-reverse;

            &.is-inverted {
                background-color: transparent !important;
                border-color: var(--color-1--1) !important;
                color: var(--color-1--1) !important;
                font-size: 2rem;

                @include breakpoint(medium down) {
                    font-size: 1.6rem;
                }

                @include on-event() {
                    background-color: var(--color-1--1) !important;
                    border-color: var(--color-1--1) !important;
                    color: $color-white !important;
                }
            }

            &.is-only-icon {
                @include size(156px, 56px);
                min-height: 56px;

                @include breakpoint(medium down) {
                    @include size(126px, 49px);
                    min-height: 49px;
                }

                @include breakpoint(small down) {
                    @include size(50px);
                    min-height: 50px;
                }
            }

            span[class*=fa-] {
                font-size: 2rem;
                margin-left: 14px;

                @include breakpoint(small down) {
                    margin-left: 0;
                }
            }

            &__text {
                @include breakpoint(small down) {
                    display: none;
                }
            }
        }
    }

    &__nav-dropdown {
        display: none;
        width: 100%;

        .is-open > & {
            display: block;
        }

        &.is-level-1 {
            margin-top: 11px;
        }

        &.is-level-2 {
            position: relative;

            @include breakpoint(small down) {
                padding-top: 15px;
            }

            &::before {
                @include absolute(0, 0, 0, 0);
                @include size(1px, 100%);
                background-color: $color-3--4;
                content: '';
            }
        }

        &.is-level-3 {
            margin: 8px 0 8px 30px;
            @include breakpoint(small down) {
                margin: 8px 0 8px 45px;
            }
        }
    }

    // Item general styles
    &__nav-item {
        .is-level-1 > & {
            padding: 8px 6px 0;

            @include breakpoint(small down) {
                padding-inline: 1px;
            }

            + #{$this}__nav-item {
                @include breakpoint(small down) {
                    padding-top: 18px;
                }
            }
        }

        .is-level-2 > & {
            padding-top: 3px;

            @include breakpoint(small down) {
                padding-top: 10px;
            }
        }

        .is-level-3 > & {
            padding-top: 1px;
        }
    }

    &__nav-item-actions {
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        margin: 0 40px 0 0;
        min-height: auto;
        position: relative;

        @include breakpoint(small down) {
            margin-bottom: 12px;
        }

        @include fa-icon-style(false) {
            color: $color-white;
            font-size: 1.4rem;
            margin-right: 6px;
        }

        .is-level-1 > #{$this}__nav-item > & {
            border-color: var(--color-1--1);

            @include breakpoint(small down) {
                margin: 0 45px 0 5px;
            }
        }

        .is-level-2 > #{$this}__nav-item > & {
            border: 0;
            color: $color-3--4;
            justify-content: start;
            margin: 5px 29px 0 0;
            min-height: 26px;
            position: relative;

            @include breakpoint(small down) {
                margin: 0 33px 0 15px;
            }
        }

        .is-level-3 > #{$this}__nav-item > & {
            border: 0;
            margin: 5px 31px 5px 0;
        }

        .is-open > &,
        [class*='level'] > .is-open > & {
            border-color: transparent;
        }
    }

    &__nav-link {
        @include trs($prop: color);
        @include font(var(--typo-1), 3.2rem, var(--fw-bold));
        color: var(--color-1--1);
        display: block;
        line-height: 1;
        position: relative;
        text-decoration: none;
        width: 100%;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
        }

        @include on-event {
            color: var(--color-2--1);
            text-decoration: underline;
        }

        + #{$this}__nav-toggle {
            margin-right: -45px;
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            font-size: 1.8rem;
            font-weight: 400;
            line-height: calc(24 / 22);
            text-transform: none;
            width: 100%;
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > #{$this}__nav-toggle + &,
        .is-level-2 > #{$this}__nav-item > #{$this}__nav-item-actions > #{$this}__nav-toggle + & {
            margin-left: 12px;
        }

        .is-level-2 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            color: $color-3--4;
            font-size: 1.6rem;
            font-weight: 400;
            line-height: calc(24 / 17);
            margin-left: 15px;
            text-transform: none;
        }

        .is-level-3 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            color: $color-3--4;
            font-size: 1.4rem;
            font-weight: normal;
            text-transform: none;
        }

        [data-has-current] > #{$this}__nav-item-actions > & {
            text-decoration: underline;
        }

        .is-open > #{$this}__nav-item-actions > & {
            color: var(--color-1--1);
            font-weight: var(--fw-bold);

            .is-level-1 > & {
                font-weight: var(--fw-bold);
            }
        }

        &.is-active {
            background-color: transparent !important;
        }
    }

    &__nav-toggle {
        @include trs;
        @include size(33px);
        background-color: var(--color-2--1);
        border: 1px solid var(--color-2--1);
        border-radius: 40px;
        color: $color-white;
        cursor: pointer;
        display: block;
        flex-shrink: 0;
        order: -1;
        padding: 4px;
        position: relative;

        @include on-event {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }

        .is-level-1 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            @include size(21px);
            background-color: transparent;
            border-color: var(--color-1--1);
            color: var(--color-1--1);

            @include on-event {
                border-color: var(--color-2--1);
                color: var(--color-2--1);
            }
        }

        .is-level-1 > #{$this}__nav-item.is-open > #{$this}__nav-item-actions > & {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }

        .is-open > #{$this}__nav-item-actions > & {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
            transform: scaleY(-1);
        }

        .is-level-2 > #{$this}__nav-item > #{$this}__nav-item-actions > & {
            @include size(16px);
            background-color: transparent;
            border-color: var(--color-1--1);
            color: var(--color-1--1);

            @include on-event {
                border-color: var(--color-2--1);
                color: var(--color-2--1);
            }
        }

        .is-level-2 > #{$this}__nav-item.is-open > #{$this}__nav-item-actions > & {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }
    }

    &__nav-toggle-icon {
        @include icon-after($fa-var-angle-down);
        @include abs-center;
        color: inherit;
        font-size: 1.2rem;
        font-weight: 400;
        height: 16px;
    }

    &__list[class] {
        margin-top: 60px;

        @include breakpoint(medium down) {
            margin-top: 0;
        }
    }

    &__link {
        color: var(--color-1--1);
        display: inline-flex;
        font-size: 2rem;
        font-weight: 400;
        line-height: calc(28 / 20);
        margin: 0 0 10px;
        position: relative;
        text-decoration: none;

        @include on-event {
            #{$this}__link-text {
                text-decoration: underline;
            }
        }

        svg {
            @include size(1.1em);
            @include absolute(1px, null, null 0);
            display: block;
            fill: var(--color-1--1);
        }

        @include fa-icon-style {
            position: absolute;
            top: 3px;
        }
    }

    &__link-text {
        padding-left: 36px;
    }

    &__lang {
        @include breakpoint(large) {
            display: none;
        }

        @include breakpoint(medium down) {
            @include absolute(25px, null, null, 80px);
        }

        @include breakpoint(small down) {
            left: 11px;
            top: 20px;
        }

        .lang,
        .g-translate-dropdown__toggle {
            color: $color-white;
        }
    }

    &__btn {
        @include trs;
        @include size(94px, 80px);
        background-color: $color-white;
        border: 0;
        border-radius: 0 40px 40px 0;
        color: var(--color-1--1);
        cursor: pointer;
        font-size: 2.4rem;
        margin-left: -1px;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include breakpoint(medium down) {
            @include size(77px, 63px);
            font-size: 2.3rem;
        }
    }

    .form {
        &__controls-group {
            display: flex;
            margin-bottom: 0;
        }

        &__field-wrapper {
            flex-grow: 1;
            width: 1%;

            &.js-autocomplete.is-visible {
                .form__field {
                    color: $color-black;
                }
            }

            .js-autocomplete-input-clear {
                @include breakpoint(small down) {
                    right: 10px;
                }
            }
        }

        &__field {
            border: none;
            border-radius: 40px 0 0 40px;
            color: var(--color-1--1);
            font-size: 2.2rem;
            height: 80px;
            padding: 15px 20px 15px 42px;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                height: 63px;
                padding: 10px 20px 10px 25px;
            }

            &::-ms-clear {
                display: none;
            }

            &:focus ~ .form__field-placeholder,
            &:not(:focus):valid ~ .form__field-placeholder {
                font-size: 1.2rem;
                left: 18px;
                top: 10px;
                transform: translateY(0);
            }
        }

        &__field-placeholder {
            @include trs;
            @include absolute(50%, 20px, null, 42px);
            color: var(--color-1--1);
            font-size: 2.2rem;
            overflow: hidden;
            pointer-events: none;
            text-overflow: ellipsis;
            transform: translateY(-50%);
            white-space: nowrap;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                left: 25px;
            }

            @include breakpoint(small down) {
                font-size: 1.6rem;
            }
        }
    }
}
