{%- from 'views/utils/utils.njk' import svg -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}

{% macro AccountMessageCreate(
    danger = false,
    iconName,
    title,
    subTitle,
    smallDescription = '',
    btnText
) %}
    <div class="account-message">
        <div class="account-message__wrapper">
            {% if iconName %}
                <div class="account-message__icon{{ ' is-red' if danger }}" aria-hidden="true">
                    {{ svg( 'account/' + iconName, 80, 75 ) }}
                </div>
            {% endif %}
            {% if title %}
                <h2 class="account-message__title js-negative-focus">{{ title }}</h2>
            {% endif %}
            {% if subTitle %}
                <p class="account-message__subtitle">{{ subTitle }}</p>
            {% endif %}
            {% if smallDescription %}
                <p class="account-message__small-description">{{ smallDescription | safe }}</p>
            {% endif %}

            {{ caller() if caller }}
        </div>
    </div>
{% endmacro %}

{#
    Réinitialisation du mot de passe
#}
{% macro ResetPassword() %}
    {% call AccountMessageCreate(
        iconName = false,
        title = 'Réinitialisation du mot de passe',
        smallDescription = 'Un courriel contenant un lien sécurisé vous sera envoyé, vous permettant de <strong>choisir un nouveau mot de passe.</strong>'
    ) %}
        {%- call Form.FormWrapper(
            legend = false,
            className = "js-validator-form account-message__form"
        ) -%}
        <div class="account-message__buttons">
            {{ Link(
                href = '#',
                text = 'annuler',
                className = 'btn is-red',
                icon = false
            ) }}
            {{ Link(
                href = '#',
                text = 'valider',
                className = 'btn is-primary',
                icon = false
            ) }}
        </div>
        {%- endcall -%}
        <div class="account-message__info">
            <p><strong>Pensez à vérifier vos courriers indésirables.</strong></p>
            <p>Une fois ce nouveau mot de passe validé, vous serez connecté(e) à votre compte.</p>
        </div>
    {% endcall %}
{% endmacro %}

{#
    Mot de passe réinitialisé !
#}
{% macro ResetPasswordSuccess(btnText = 'Accéder à mon tableau de bord') %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-2',
        title = 'Mot de passe réinitialisé !',
        subTitle = 'Votre nouveau mot de passe a été enregistré avec succès !',
        btnText = btnText
    ) %}
        <div class="account-message__description">
            <p>Vous pouvez dès à présent vous connecter en utilisant vos identifiants (votre courriel et votre nouveau mot de passe)</p>
        </div>
        {{ Link(
            href = '#',
            text = btnText,
            className = 'btn is-primary',
            icon = 'far fa-cog'
        ) }}
    {% endcall %}
{% endmacro %}

{#
    Votre compte a bien été créé !
#}
{% macro AccountHasBeenCreated() %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-3',
        title = 'Votre compte a bien été créé !',
        subTitle = 'Rendez-vous dans votre boîte de réception !'
    ) %}
        <div class="account-message__description">
            <p>Pour finaliser la création de votre compte, un courriel vient d'être envoyé à l'adresse
            <EMAIL> avec un lien pour valider votre compte. Si ce courriel n'est pas dans votre
            boîte de réception, <strong> vérifiez bien votre dossier de spams ou de courriers indésirables. </strong></p>

            <a href="#" class="account-message__main-link">Je n'ai pas reçu le courriel d'activation.</a>
        </div>
    {% endcall %}
{% endmacro %}

{#
    Veuillez-vous connecter
#}
{% macro shouldBeConnected() %}
    {% call AccountMessageCreate(
        iconName = 'ico-demarche',
        title = 'Veuillez-vous connecter'
    ) %}
        <div class="account-message__description">
            <p>Pour pouvoir ajouter ce contenu à vos favoris, vous devez être authentifié.</p>
        </div>
        <div class="account-message__buttons">
        {{ Link(
            href = '#',
            text = 'Se connecter',
            className = 'btn is-primary',
            icon = false
        ) }}
        {{ Link(
            href = '#',
            text = 'Créer un compte',
            className = 'btn is-primary',
            icon = false
        ) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
    Votre compte a bien été Validé !
#}
{% macro AccountHasBeenValidated() %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Votre compte a bien été validé !',
        subTitle = 'Bienvenue dans votre compte citoyen !'
    ) %}
        <div class="account-message__description">
            <p>Vous pouvez à présent utiliser votre compte et accéder aux services en ligne de ##NOMDUCLIENT#</p>
        </div>
        {{ Link(
            href = '#',
            text = 'Accéder à mon tableau de bord',
            className = 'btn is-primary',
            icon = 'far fa-cog'
        ) }}
    {% endcall %}
{% endmacro %}

{#
    C'est presque fini !
#}
{% macro AlmostDone() %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-4',
        title = 'C\'est presque fini !',
        smallDescription = 'Un courriel vient d\'être envoyé à l\'adresse <EMAIL>'
    ) %}
        <div class="account-message__description">
            <p>Afin de modifier votre mot de passe personnel, cliquez sur le lien figurant dans le courriel que vous venez de recevoir.</p>
            <p>Si ce courriel n'est pas dans votre boîte de réception, <strong>vérifiez bien votre dossier de spams ou de courriers indésirables.</strong></p>
        </div>
    {% endcall %}
{% endmacro %}

{#
    PopupConfirmDeletion template.
#}
{% macro PopupConfirmDeletion() %}
    {% call AccountMessageCreate(
        danger = true,
        iconName = 'popup-icon-5',
        title = 'Confirmer la suppression',
        smallDescription = 'Vous êtes sur le point de supprimer une demande faite en ligne. Cette action est irréversible.'
    ) %}
        <div class="buttons-group is-center">
            {{ Button(
                className = 'btn is-bordered account-message__remove',
                icon = false,
                text = 'Oui'
            ) }}
            {{ Button(
                className = 'btn is-bordered',
                icon = false,
                text = 'Non',
                attrs = {
                    'data-fancybox-close': '' | safe
                }
            ) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
    Your request has been processed successfully!
#}
{% macro RequestHasBeenValidated() %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Votre demande a  été traitée avec succés'
    ) %}
    {% endcall %}
{% endmacro %}
{#
    Your request awaits validation!
#}
{% macro RequestAwaitsValidation() %}
    {% call AccountMessageCreate(
        iconName = 'popup-icon-1',
        title = 'La demande a bien été prise en compte. Elle est en attente de validation.'
    ) %}
    {% endcall %}
{% endmacro %}

