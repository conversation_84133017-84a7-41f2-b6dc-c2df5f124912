.event-item {
    $this: &;

    @extend %link-block-context;

    align-items: center;
    cursor: pointer;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto 30px;
    max-width: 384px;
    padding-top: 96px;
    position: relative;

    .is-main-account-page & {
        padding-top: 116px;
    }

    @include on-event() {
        #{$this}__image {
            img {
                transform: translateZ(0);
            }
        }
    }

    @include breakpoint(medium down) {
        align-items: center;
        flex-direction: column-reverse;
        max-width: 100%;
        padding-top: 70px;
    }

    @include breakpoint(small down) {
        align-items: flex-start;
        flex-direction: column-reverse;
        max-width: 384px;

        .single-event-page & {
            padding-bottom: 30px;
        }
    }

    .swiper-slide-active & {
        .events-home & {
            #{$this} {
                &__title {
                    font-size: 3.5rem;

                    @include breakpoint(small down) {
                        font-size: 2.8rem;
                    }

                    .underline {
                        @include multiline-underline($color: $color-black);
                    }
                }
            }
        }
    }

    .list-events & {
        padding-top: 86px;

        @include breakpoint(medium down) {
            #{$this}__content {
                padding-right: 20px;
            }

            #{$this}__date {
                left: calc(50% - 55px) !important;
            }

            #{$this}__nbplaces {
                margin-left: 70px;
            }

            #{$this}__wrap {
                margin-top: -30px !important;
            }
        }

        @include breakpoint(small down) {
            #{$this}__nbplaces {
                margin-left: 40px;
            }
        }

        #{$this}__image {
            border-radius: 0;
            -webkit-mask-image: image("Mask-Image-Evts.svg");
            mask-image: image("Mask-Image-Evts.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;

            img {
                border-radius: 0;
                -webkit-mask-image: image("Mask-Image-Evts.svg");
                mask-image: image("Mask-Image-Evts.svg");
                -webkit-mask-repeat: no-repeat;
                mask-repeat: no-repeat;
                -webkit-mask-size: contain;
                mask-size: contain;
            }
        }

        #{$this}__date {
            @include size(116px);
            padding: 39px 23px 37px 17px;

            &.date {
                .is-day {
                    font-size: 2.5rem;
                }

                .is-month {
                    font-size: 1rem;
                }

                .date__icon {
                    &::before {
                        font-size: 1.3rem;
                        font-weight: var(--fw-light);
                    }
                }
            }
        }

        #{$this}__time-place {
            .time-place__item {
                font-size: 1.2rem;

                .far {
                    font-size: 1.2rem;
                }
            }
        }

        #{$this}__title-link {
            font-size: 2rem;
        }

        #{$this}__category {
            font-size: 1.4rem;
        }

        #{$this}__nbplaces {
            font-size: 1.2rem;
        }
    }

    // styles for HP with background
    .events-home & {
        align-items: center;

        #{$this}__nbplaces {
            @include size(212px, 48px);
            align-content: center;
            background-color: var(--color-1--1);
            clip-path: polygon(0 2px, 212px 0, 212px 48px, 0 50px);
            color: #fff;
            display: block;
            margin-top: 0;
            order: -1;
            text-align: center;
        }

        #{$this}__content {
            border-left: 1px solid $color-3--3;
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
            margin-left: 15px;
            padding: 0 0 0 15px;
            position: static;
        }

        #{$this}__title {
            font-size: 2rem;

            .underline {
                @include multiline-underline($color: $color-black);
            }
        }

        #{$this}__category {
            font-size: 1.4rem;
        }

        #{$this}__time-place {
            margin-top: 7px;

            .time-place__item {
                color: var(--color-1--3);

                .home-page & {
                    color: $color-black;
                    font-size: 1.2rem;
                }
            }
        }

        #{$this}__wrap {
            background-color: transparent;
            flex-direction: row;
        }

        #{$this}__image {
            border-radius: 0;
            -webkit-mask-image: image("Mask-Image-Evts.svg");
            mask-image: image("Mask-Image-Evts.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;

            img {
                border-radius: 0;
                -webkit-mask-image: image("Mask-Image-Evts.svg");
                mask-image: image("Mask-Image-Evts.svg");
                -webkit-mask-repeat: no-repeat;
                mask-repeat: no-repeat;
                -webkit-mask-size: contain;
                mask-size: contain;
            }
        }

        #{$this}__date {
            @include size(116px);
            padding: 12px 20px;
            z-index: 1;

            .is-day {
                font-size: 2.5rem;
            }

            .is-month {
                font-size: 1rem;
            }
        }
    }

    .events-widget.is-width-33 & {
        #{$this} {
            &__content {
                @include breakpoint(large only) {
                    padding-left: 20px;
                    padding-top: 10px;
                    text-align: left;
                }
            }

            &__wrap {
                margin-left: 10px;
                margin-top: -70px;

                @include breakpoint(medium down) {
                    margin-top: -30px;
                    padding-left: 0;
                }

                @include breakpoint(small down) {
                    margin-left: 0;
                    margin-top: 0;
                }
            }
        }
    }

    // styles for widget/sidebar on CP
    .events-widget & {
        #{$this} {
            &__date {
                @include breakpoint(large only) {
                    @include absolute(0, null, null, null);
                }
            }

            &__content {
                @include breakpoint(large only) {
                    padding: 30px 20px 10px 0;
                    text-align: center;
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(large only) {
                        justify-content: center;
                    }
                }
            }
        }
    }

    //style for single event page
    .single-event-page & {
        padding-top: 66px !important;

        #{$this}__date {
            @include size(116px);
            border: none;
            margin-top: 5px;
            padding: 39px 23px 37px 17px;

            .is-day {
                font-size: 2.5rem;
            }

            .is-month {
                font-size: 1rem;
            }

            @include breakpoint(medium down) {
                left: 160px;
            }

            @include breakpoint(small down) {
                left: 49%;
            }
        }

        #{$this}__category {
            font-size: 1.4rem;
        }

        #{$this}__title {
            font-size: 2rem;

            @include breakpoint(small down) {
                font-size: 2.8rem;
            }
        }

        #{$this}__time-place {
            font-size: 1.2rem;
        }

        #{$this}__image {
            width: 100%;

            @include breakpoint(medium down) {
                @include size(211px);
                margin-right: 30px;
            }

            @include breakpoint(small down) {
                margin-bottom: -20px;
                margin-right: auto;
            }
        }

        // #{$this}__content {
        //     @include breakpoint(small down) {
        //         .single-event-page & {
        //             margin-top: 20px;
        //             padding-left: 72px;
        //             padding-top: 15px;
        //         }
        //     }
        // }
    }

    &__image {
        border-radius: 0;
        display: block;
        -webkit-mask-image: url(../images/Mask-Image-Evts.svg);
        mask-image: url(../images/Mask-Image-Evts.svg);
        mask-position: center;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        width: 317px;

        @include breakpoint(medium down) {
            @include min-size(auto);
            @include size(211px);
        }

        @include breakpoint(small down) {
            margin: 0 auto;
        }

        img {
            @include object-fit();
            @include size(100%);
            border-radius: 0;
            -webkit-mask-image: url(../images/Mask-Image-Evts.svg);
            mask-image: url(../images/Mask-Image-Evts.svg);
            mask-position: center;
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
        }
    }

    &__wrap {
        align-items: start;
        background-color: $color-white;
        display: flex;
        margin-left: 20px;
        margin-top: -70px;
        width: 100%;
        z-index: 1;

        .event-item__content-without-cercle {
            margin-left: 0;
        }

        .list-events & {
            margin: auto;
        }

        @include breakpoint(medium down) {
            align-items: start;
            margin-top: -30px;
        }

        @include breakpoint(small down) {
            margin-top: 0;
            padding-left: 58px;
        }

        @media screen and (max-width: 480px) {
            padding-left: 30;
            margin-left: 0 !important;
        }

        @media screen and (max-width: 390px) {
            padding-left: 0 !important;
        }
    }

    &__date {
        @include absolute(0, null, null, 50%);
        @include size(158px);
        @include min-size(auto);
        align-items: center;
        border-radius: 50%;
        max-width: 220px;
        transform: translateX(-50%);
        z-index: 2;

        &.date {
            .is-day {
                font-size: 3.2rem;

                @include breakpoint(medium down) {
                    font-size: 2.5rem;
                }
            }

            .is-month {
                font-size: 1.3rem;

                @include breakpoint(medium down) {
                    font-size: 1rem;
                }
            }

            .date__icon {
                align-content: center;
                margin-left: 7px;
                margin-right: 7px;

                &::before {
                    font-size: 1.3rem;
                    font-weight: var(--fw-light);
                    margin-bottom: 8px;
                }
            }

            &::before {
                content: none;
            }
        }

        @include breakpoint(medium down) {
            @include size(116px);
            padding: 12px 20px;
        }
    }

    &__content {
        padding: 20px 0 20px 10px;

        @include breakpoint(medium down) {
            display: flex;
            flex-direction: column;
            padding: 0 0 0 8px;
        }
    }

    &__content-without-cercle {
        padding: 20px 0 20px 66px;

        &::before {
            display: none;
        }
    }

    &__title {
        @include font(null, 2.4rem, var(--fw-bold));
        line-height: 1.2;

        @include breakpoint(small down) {
            font-size: 2.8rem;
            line-height: 28px;
        }

        .is-width-33 & {
            font-size: 2rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.6rem, var(--fw-bold));
        color: var(--color-1--1);
        letter-spacing: 2.52px;
        line-height: 17px;
        margin-top: 8px;
        text-transform: uppercase;
        z-index: 2;

        @include breakpoint(small down) {
            margin-top: -12px;

            .events-home__content & {
                margin-top: 6px;
            }
        }

        .is-width-33 & {
            font-size: 1.4rem;
        }
    }

    &__type {
        @include font(null, 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-top: 9px;
        z-index: 2;

        @include breakpoint(small down) {
            margin-top: 8px;
        }
    }

    &__nbplaces {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        text-transform: uppercase;
        z-index: 2;

        @include breakpoint(medium down) {
            background-color: var(--color-1--1);
            color: #fff;
            order: -1;
            padding: 10px 20px;
            text-align: center;
            width: max-content;
        }

        @include breakpoint(small down) {

            font-size: 1rem;
            font-weight: var(--fw-bold);
            margin: 0 0 17px 0;

            .single-event-page & {
                top: -37px;
            }
        }
    }

    &__time-place {
        margin-top: 7px;

        .time-place__item {
            font-size: 1.5rem;
            font-weight: var(--fw-normal);
            margin-right: 16px;

            .far {
                color: var(--color-1--1);

                .home-page & {
                    font-size: 1.2rem;
                    font-weight: var(--fw-light);
                }
            }

            &.is-time {
                color: $color-black;
                flex-shrink: 0;

                .is-width-33 & {
                    font-size: 1.2rem;
                    justify-content: left;
                }

                @include breakpoint(medium down) {
                    font-size: 1.2rem;
                }
            }

            &.is-place {
                color: $color-black;

                .is-width-33 & {
                    font-size: 1.2rem;
                    justify-content: left;
                }

                @include breakpoint(medium down) {
                    font-size: 1.2rem;
                }
            }
        }

        @include breakpoint(small down) {
            margin-top: 5px;
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }

    &__title-reservation {
        display: block;

        @include on-event() {
            text-decoration: none;
        }
    }
}
