{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/core-components/title.njk' import TitleDefault -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- import 'views/core-components/form.njk' as Form -%}

{% set defaultNewsletterLinks = [
    { link: 'Archives', icon: 'fas fa-angle-right' },
    { link: 'Désinscription', icon: 'fas fa-angle-right' }
] %}

{#
    NewsletterLinks template.
    @param {object[]} links - newsletter links array.
#}
{%- macro NewsletterLinks(links = defaultNewsletterLinks) -%}
    <ul class="newsletter-links">
        {% for item in links %}
            <li class="newsletter-links__item">
                <a href="#" class="newsletter-links__item-link {{ item.modifier }}">
                    {{ Icon(className = item.icon) if item.icon }}
                    <span class="newsletter-links__item-text">{{ item.link }}</span>
                </a>
            </li>
        {% endfor %}
    </ul>
{%- endmacro -%}

{#
    NewsletterBar template
#}
{%- macro NewsletterBar(
    className = 'newsletter-bar',
    container = 'newsletter-bar__container',
    titleTag = 'h3'
    ) -%}
    {% call Section(className = className, container = container) %}
        <div class="newsletter-bar__wrap">
            <div class="newsletter-bar__title">
                {{ TitleDefault(
                    tag = titleTag,
                    text = 'Lettre d\'information',
                    iconPath = 'icons/plane'
                ) }}
            </div>
            <div class="newsletter-bar__form-wrapper">
                {%- call Form.FormWrapper(legend = false, className = 'newsletter-bar__form') -%}
                    <div class="newsletter-bar__fields">
                        {{ Form.FormField(
                            wrapperModifier = 'is-inline',
                            type = 'email',
                            label = 'Mon courriel',
                            placeholder = 'Saisir une adresse e-mail valide',
                            customAttrs = {
                                'autocomplete': 'email'
                            }
                        ) }}
                    </div>
                    <div class="newsletter-bar__actions">
                        {{ Button(
                            className = 'btn is-primary newsletter-bar__submit js-tooltip is-tooltip-bottom',
                            type = 'submit',
                            text = 'Je m\'inscris <span class="sr-only">à la lettre d\'information</span>',
                            attrs = {
                                'data-content': 'Je m\'inscris à la lettre d\'information'
                            }
                        ) }}
                    </div>
                    <div class="newsletter-bar__links">
                        {{ NewsletterLinks() }}
                    </div>
                {%- endcall -%}
            </div>
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    NewsletterFooter template.
#}
{%- macro NewsletterFooter() -%}
    <section class="newsletter">
        <div class="newsletter__form">
            {% call Form.FormWrapper(legend = 'Lettre d\'information', legendTag = 'h2') %}
                {% call Form.FieldGroup(
                    type = 'email',
                    label = 'Votre courriel',
                    placeholder = 'Saisir un courriel valide...',
                    customAttrs = {
                        autocomplete: 'email',
                        required: 'true'
                    }
                ) %}
                    {{ Button(
                        type = 'submit',
                        className = 'btn is-primary is-small is-sm-text-hidden js-tooltip',
                        icon = 'fas fa-check',
                        text = 'Je m\'inscris <span class="sr-only">à la lettre d\'information</span>',
                        attrs = {
                            'data-content': 'Je m\'inscris à la lettre d\'information'
                        }
                    ) }}
                {% endcall %}
            {% endcall %}
        </div>
        {% if params.links %}
            <div class="newsletter__links-list">
                {{ NewsletterLinks() }}
            </div>
        {% endif %}
    </section>
{%- endmacro -%}

