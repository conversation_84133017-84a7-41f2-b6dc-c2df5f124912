@mixin gradient($useTop: true, $useBottom: true) {
    @if $useTop {
        &::before {
            @include absolute(0, 0);
            @include size(100%, 310px);
            background-image: linear-gradient(to top, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
            content: none;
            pointer-events: none;
            z-index: 1;

            @include breakpoint(medium down) {
                height: 226px;
            }

            @include breakpoint(small down) {
                height: 120px;
            }
        }
    }

    @if $useBottom {
        &::after {
            @include absolute(null, 0, 0);
            @include size(100%, 481px);
            background-image: linear-gradient(180deg, rgba($color-black, 0) 0%, rgba($color-black, 0.52) 64%, rgba($color-black, 0.6) 100%);
            content: none;
            pointer-events: none;

            @include breakpoint(medium down) {
                height: 334px;
            }

            @include breakpoint(small down) {
                height: 247px;
            }
        }
    }
}

.hero-item {
    $this: &;

    min-width: 100%;
    position: relative;

    @include breakpoint(medium down) {
        margin-top: 34px;
    }

    @include breakpoint(small down) {
        margin-top: 28px;
    }

    &::before {
        @include absolute(1px, 15%, null, null);
        @include size(982px, 443px);
        background-color: var(--color-2--1);
        border-radius: 30px;
        clip-path: polygon(0 0, 98% 3%, 100% 75%, 0 19%);
        content: '';
        z-index: 0;

        @media screen and (width >= 1440px) and (width <= 1700px) {
            clip-path: polygon(0 0, 98% 2%, 100% 75%, 0 19%);
            right: 5%;
            top: 3px;
            width: 1030px;
        }

        @media screen and (width >= 1280px) and (width < 1440px) {
            clip-path: polygon(0 0, 98% 2%, 100% 75%, 0 19%);
            right: 5%;
            top: 3px;
            width: 904px;
        }

        @include breakpoint(medium down) {
            @include absolute(7px, auto, null, 13%);
            @include size(100%, 264px);
        }

        @include breakpoint(small down) {
            top: 20px;
        }
    }

    &::after {
        @include absolute(50px, 1390px, null, null);
        @include size(461px, 563px);
        background-image: image('decor/rayonnement-header.svg');
        background-repeat: no-repeat;
        background-size: cover;
        content: '';
        z-index: 0;

        @media screen and (min-width: 1280px) and (max-width: 1700px) {
            background-size: contain;
            left: -106px;
            right: auto;
            width: 497px;
        }

        @include breakpoint(medium down) {
            @include absolute(auto, null, -2px, 43px);
            @include size(160px, 192px);
            background-image: image('decor/rayonnement-header-tablette.svg');
            display: none;
        }

        @include breakpoint(small down) {
            @include absolute(auto, null, 28px, -58px);
            @include size(178px, 216px);
            background-image: image('decor/rayonnement-header-mobile.svg');
        }
    }

    .swiper-slide-active &,
    .has-video & {
        &__video {
            opacity: 1;
            visibility: visible;
        }
    }

    &.swiper-slide:not(.swiper-slide-active) {
        z-index: -1;
    }

    &__media {
        max-height: 100vh;
        min-height: 720px;
        overflow: hidden;
        position: relative;

        @include breakpoint(medium down) {
            min-height: 578px;
        }

        @include breakpoint(small down) {
            max-height: 100%;
            min-height: 100%;
        }

        .has-video & {
            @include responsive-ratio(1920, 1080, 'before');

            @include breakpoint(medium down) {
                @include responsive-ratio(768, 620, 'before');
                max-height: 620px;
            }

            @include breakpoint(small down) {
                @include responsive-ratio(360, 475, 'before');
                max-height: 475px;
            }
        }
    }

    &__link {
        display: block;

        .lazy.lazyloaded:not(.is-no-image) {
            animation: none !important;
        }

        &:focus,
        &:focus-within {
            #{$this}__picture {
                outline: 4px solid var(--color-1--1);
                outline-offset: -6px;
            }

            img {
                transform: none;
            }
        }
    }

    &__picture {
        @include trs;
        @include gradient;
        border-radius: 30px 0 0 30px;
        display: block;
        float: right;
        height: 100%;
        margin-top: 23px;
        position: relative;
        transform: matrix(1, -0.03, 0.03, 1, 11, 0);
        width: 1340px;

        @media screen and (min-width: 1440px) and (max-width: 1700px) {
            width: 1200px;
        }

        @media screen and (min-width: 1280px) and (max-width: 1439px) {
            width: 1047px;
        }

        @include breakpoint(medium down) {
            transform: matrix(1, -0.03, 0.03, 1, 11, 2);
            width: calc(100% - 40px);
        }

        @include breakpoint(small down) {
            padding-bottom: 10px;
            width: calc(100% - 7px);
        }

        img {
            @include trs;
            @include object-fit;
            @include size(100%, auto);
            border-radius: 30px 0 0 30px;
        }
    }

    &__video {
        @include gradient;
        @include trs;
        @include abs-center;
        @include size(100%);
        opacity: 0;
        overflow: hidden;
        visibility: hidden;

        .video {
            background-color: $color-black;
            height: 100%;
            position: relative;

            @include on-event {
                .video-controls__action {
                    opacity: 1;
                }
            }

            &__video-wrap {
                display: block;
                height: 100%;
                overflow: hidden;
            }

            &__controls-wrap {
                @include absolute(null, null, 50%, 50%);
                transform: translate(-50%, -50%);
                z-index: 2;
            }

            .video-controls {
                &__action {
                    @include trs;
                    @include size(50px);
                    background-color: var(--color-1--1);
                    border: 0;
                    border-radius: 50%;
                    color: rgba($color-white, 0.8);
                    cursor: pointer;
                    display: block;
                    opacity: 0;
                    padding: 0;
                    z-index: 1;

                    &:focus {
                        opacity: 1;
                    }
                }

                &__icon {
                    pointer-events: none;
                }
            }
        }

        video {
            @include object-fit;
            @include absolute(0, 0, 0, 0);
            @include size(100%);
            margin: auto;
        }
    }

    &__title {
        display: block;
    }

    &__search {
        @include absolute(null, null, null, 88px);
        width: 798px;
        z-index: 5;

        @include breakpoint(medium down) {
            bottom: 140px;
            left: 96px;
            width: 574px;
        }

        @include breakpoint(small down) {
            left: 0;
            width: 328px;
        }
    }

    &__keywords[class] {
        margin-top: 11px;

        @include breakpoint(medium down) {
            margin-top: 20px;
        }

        @include breakpoint(medium down) {
            margin-top: 15px;
        }
    }
}
