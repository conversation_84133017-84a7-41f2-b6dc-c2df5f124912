.sidebar-menu {
    $this: &;

    margin-bottom: 60px;

    &.desktop {
        display: block;
        @include breakpoint(small down) {
            display: none;
        }
    }

    &.mobile {
        display: none;
        @include breakpoint(small down) {
            display: block;
        }
    }

    &__list {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        @include breakpoint(medium down) {
            justify-content: center;
        }

        @include breakpoint(small down) {
            flex-direction: column;

            &:first-of-type {
                margin-top: 20px;
            }
        }

        #{$this}__link {
            &.is-degradable {
                @include font(var(--typo-1), 1.6rem, var(--fw-normal));
                background-color: transparent !important;
                border: 1px solid var(--color-1--1) !important;
                border-image-slice: 1 !important;
                border-radius: 50px;
                color: $color-1--1 !important;
                display: block;
                padding: 8px 26px;
                text-align: center;
                text-decoration: none;
                text-transform: uppercase;
            }
        }
    }
}

.is-sticky-mobile {
    @include fixed(52px,null,null,0);
    background-color: $color-white;
    padding: 0 15px;
    padding-bottom: 12px;
    width: 100%;
    z-index: 49;

    .click-and-roll {
        margin: 10px 0 0 0 !important;

        &__wrapper {
            max-height: calc(100vh - 220px);
            overflow-y: scroll;
        }
    }

    button {
        background-color: var(--color-1--1);
        color: $color-white;

        span::before {
            color: $color-white;
        }
    }
}

.is-sticky-desktop {
    @include fixed(83px,null,null,null);
    background-color: $color-white;
    left: 50%;
    max-width: 1200px;
    min-height: 56px;
    padding-bottom: 5px;
    transform: translateX(-50%);
    width: 100%;
    z-index: 49;

    @include breakpoint(medium down) {
        max-width: 644px;
    }

    .click-and-roll {
        margin: 10px 0 0 0 !important;

        @include breakpoint(medium down) {
            margin-top: 0 !important;
        }
    }
}

.is-active {
    a {
        background-color: var(--color-1--1) !important;
        border-radius: 20px;

        .sidebar-menu__link.is-degradable {
            color: $color-white !important;
        }
    }
    @include breakpoint(large down) {
        background-color: var(--color-1--1) !important;
        border-radius: 20px;

        .sidebar-menu__link.is-degradable {
            color: $color-white !important;
        }
    }
}
