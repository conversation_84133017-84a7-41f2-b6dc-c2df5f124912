.editions {
    $this: &;
    position: relative;

    @include breakpoint(small down) {
        .home-page & {
            margin-top: 30px;
        }
    }

    &::before {
        @include size(100vw, calc(100% - 230px));
        @include absolute(180px, 510px);
        background-color: var(--color-1--1);
        border-radius: 0 20px 20px 0;
        content: '';
        transform: matrix(1, -0.07, 0.07, 1, 0, 0);

        .home-page & {
            right: 670px;
        }

        @include breakpoint(medium down) {
            content: none;
        }
    }

    &::after {
        @include size(654px, 239px);
        @include absolute(null, null, 17px, null);
        background-color: var(--color-2--1);
        border-radius: 10px;
        content: '';
        transform: matrix(-1, -0.05, 0.05, -1, 0, 0);
        z-index: -1;

        @include breakpoint(medium down) {
            @include size(504px, 239px);
            bottom: 145px;
        }

        @include breakpoint(small down) {
            bottom: 75px;
            left: -180px;
        }
    }

    &.section {
        margin-bottom: 160px;

        @include breakpoint(medium down) {
            margin-bottom: 75px;
        }
    }

    .section {
        &__title {
            @include breakpoint(medium down) {
                margin-bottom: 165px;
            }

            @include breakpoint(small down) {
                margin-bottom: 25px;
            }
        }

        &__more-links {
            justify-content: flex-end;
            margin-top: 75px;

            .btn {
                margin: 0;
            }

            @include breakpoint(medium down) {
                justify-content: center;
                margin-top: 150px;
                padding-left: 0;
            }

            @include breakpoint(small down) {
                justify-content: center;
                margin-top: 95px;
                padding-left: 0;
            }
        }
    }

    &__container {
        @extend %container;
        @extend %container-lg;
        position: relative;

        @include breakpoint(medium down) {
            padding: 0 40px;
        }

        @include breakpoint(small down) {
            padding: 0 12px;
        }
    }

    &__item {
        direction: ltr;
        max-width: 303px;

        @include breakpoint(medium down) {
            max-width: 100%;
        }

        &.swiper-slide-prev {
            visibility: hidden;
        }

        &.swiper-slide-next {
            @include breakpoint(small down) {
                visibility: hidden;
            }
        }

        &.swiper-slide-active {
            pointer-events: auto;

            @include breakpoint(medium down) {
                width: 100% !important;
            }

            .edition-item {
                display: flex;

                @include breakpoint(small down) {
                    display: block;
                }
            }
        }
    }

    .swiper-container-coverflow .swiper-wrapper {
        /* Windows 10 IE 11 fix */
        -ms-perspective: 1200px;
    }
}

.editions-block {
    direction: rtl;
    margin-top: 83px;
    position: relative;

    @include breakpoint(medium down) {
        margin: 0 auto 25px;
        max-width: 688px;
    }

    @include breakpoint(small down) {
        align-items: center;
        display: flex;
        flex-direction: column;
        margin: 0 auto !important;
        max-width: 320px;
    }

    &::before {
        @include size(1300px, 618px);
        @include absolute(150px, null, null, -560px);
        background-color: var(--color-1--1);
        border-radius: 0 20px 20px 0;
        content: none;
        transform: matrix(1, -0.07, 0.07, 1, 0, 0);

        @include breakpoint(medium down) {
            @include size(1000px, calc(100% + 80px));
            content: '';
            left: -470px;
            top: -31px;
        }

        @include breakpoint(small down) {
            @include size(calc(100vw + 100px), calc(100% - 20px));
            border-radius: 0;
            left: 50%;
            top: 65px;
            transform: translateX(-50%) matrix(1, -0.07, 0.07, 1, 0, 0);
        }
    }

    &__container {
        max-width: 1260px;

        @include breakpoint(medium down) {
            max-width: calc(100% - 45px);
        }

        @include breakpoint(small down) {
            max-width: 100%;
            padding: 0;
            width: 100%;
        }
    }

    &__control {
        @include absolute(40%);
        @include font(null, 4rem, var(--fw-light));
        @include size(50px);
        align-items: center;
        background: none;
        border: 0;
        color: $color-white;
        cursor: pointer;
        display: flex;
        justify-content: center;
        padding: 0;
        z-index: 2;

        @include breakpoint(medium down) {
            top: 30%;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include fa-icon-style(false) {
            @include font(null, 4rem, var(--fw-light));
        }

        &.is-prev {
            left: -40px;

            @include breakpoint(medium down) {
                left: 10px;
            }

            @include breakpoint(small down) {
                left: -7px;
            }
        }

        &.is-next {
            left: 464px;
            right: auto;

            @include breakpoint(medium down) {
                left: 395px;
            }

            @include breakpoint(small down) {
                left: auto;
                right: -7px;
            }
        }
    }

    &__pagination {
        @include absolute(null, null, 95px, 343px);
        display: flex;
        rotate: 180deg;
        width: 457px;
        z-index: 2;

        @include breakpoint(medium only) {
            display: none;
        }

        @include breakpoint(small down) {
            justify-content: center;
            position: static;
            text-align: center;
        }

        .swiper-pagination {
            &__bullet {
                @include size(10px);
                background-color: transparent;
                border: 1px solid $color-white;
                border-radius: 5px;
                margin-right: 6px;
                position: relative;

                &.is-active {
                    background-color: $color-white;
                    width: 26px;
                }
            }

            &__bullet-btn {
                @include absolute();
                @include size(10px);
                border-radius: 5px;
                cursor: pointer;
                opacity: 0;
                padding: 0;
            }
        }
    }
}

.carousel-pagination {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
    rotate: 180deg;
    z-index: 1;

    @include breakpoint(large down) {
        @include absolute(null, null, 44px, 317px);
    }

    @include breakpoint(medium down) {
        bottom: 20px;
        left: 275px;
    }

    @include breakpoint (small down) {
        @include reset-position();
        margin: 35px auto 0;
    }

    .carousel-bullet {
        @include size(10px);
        background-color: transparent;
        border: 1px solid $color-white;
        border-radius: 20px;
        cursor: pointer;
        margin: 0 2px;
        transition: background-color 0.3s;

        &.active {
            background-color: $color-white;
            width: 26px;
        }
    }
}
