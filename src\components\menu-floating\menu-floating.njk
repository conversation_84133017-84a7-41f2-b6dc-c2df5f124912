{% from 'views/utils/utils.njk' import svg with context %}
{%- from 'components/main-nav-toggle/main-nav-toggle.njk' import MainNavToggle -%}
{%- from 'components/search/search.njk' import Search -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/header/header-click.njk' import Click -%}

{%- macro MenuFloating(
    fixed = true,
    buttons = [
       {   icon: 'info-circle',
            text: 'mes services'
        },
        {   icon: 'circle-user',
            text: 'mon compte'
        }],
        showTheSearch = true
) -%}
    <div class="menu-floating">
        <button type="button" class="menu-floating__toggle" {{ 'data-sd-toggle=menu-floating' if not fixed }}>
            <span class="menu-floating__toggle-icon" aria-hidden="true"></span>
            <span class="menu-floating__toggle-text">Accès rapides</span>
        </button>
        <ul class="menu-floating__list" {{ 'data-sd-content=menu-floating' if not fixed }}>
              <li class="menu-floating__item">
                {{ Click() }}
            </li>
           
            <li class="menu-floating__item ">
                <a href="#" class="menu-floating__link">
                    <span class="menu-floating__icon">{{ svg('icons/' + buttons[0].icon, 15, 21) }}</span>
                    <span class="menu-floating__text">{{ buttons[0].text | capitalize }}</span>
                </a>
            </li>
          
            <li class="menu-floating__item">
                <a href="#" class="menu-floating__link">
                    <span class="menu-floating__icon">{{ svg('icons/' + buttons[1].icon, 20, 21) }}</span>
                    <span class="menu-floating__text">{{ buttons[1].text | capitalize }}</span>
                </a>
            </li>
             <li class="menu-floating__item">
                <nav class="menu-floating__nav-toggle" role="navigation" aria-label="Navigation principale">
                {% if showTheSearch === true %}
                    {{ MainNavToggle() }}
                {% endif %}
                </nav>
            </li>
        </ul>
    </div>
{%- endmacro -%}
