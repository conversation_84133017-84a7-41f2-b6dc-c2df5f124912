declare const $: any;
import '@fancyapps/fancybox';

export default function accountValidationPopup(): void {
    const selector: HTMLElement | null = document.querySelector('#account-validated-popup');
    const associationCheckbox: HTMLInputElement | null = document.querySelector('.js-association-checkbox');
    const parentFrom = associationCheckbox?.closest('form');
    if (!parentFrom) {
        return;
    }
    parentFrom?.addEventListener('submit', (e) => {
        if (associationCheckbox?.checked && selector) {
            e.preventDefault();
            $.fancybox.open(selector);
        }
    });
}
