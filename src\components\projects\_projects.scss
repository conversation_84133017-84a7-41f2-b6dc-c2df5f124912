.projects {
    margin-bottom: 50px;

    &__container {
        @extend %container;
        @extend %container-fluid;
    }

    &__title {
        @extend %container;
        margin-bottom: -110px;

        @include breakpoint(medium down) {
            margin-bottom: -50px;
        }

        @include breakpoint(small down) {
            margin-bottom: 30px;
        }

        .title[class] {
            background-color: $color-white;
            line-height: 1;
            max-width: 485px;
            padding: 0 90px 45px 0;
            position: relative;
            z-index: 1;

            @include breakpoint(medium down) {
                max-width: 605px;
                padding: 0 60px 42px 0;
            }

            @include breakpoint(small down) {
                padding: 0;
            }

            &::before {
                @include absolute(0, 0, 0);
                @include size(100vw, 100%);
                background-color: $color-white;
                content: '';

                @include breakpoint(small down) {
                    content: none;
                }
            }
        }
    }

    .project-list {
        display: flex;
        justify-content: flex-end;
        margin: -138px auto 0;
        max-width: 1265px;
        padding: 0 40px;

        @include breakpoint(medium down) {
            justify-content: center;
            margin: 45px auto 0;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0 auto;
            max-width: 360px;
            padding: 0 20px;
        }

        &__item {
            border: 15px solid $color-white;
            margin: -7.5px;
            position: relative;

            @include breakpoint(small down) {
                border: 0;
                margin: 0 0 5px;
            }

            &:last-child {
                .project-item {
                    background-color: var(--color-2--1);
                    color: var(--color-1--2);

                    &__image {
                        svg {
                            fill: var(--color-1--2);
                        }
                    }

                    &__title {
                        .underline {
                            @include multiline-underline($color: var(--color-1--2));
                        }
                    }
                }
            }

            &:nth-last-child(2) {
                .project-item {
                    background-color: var(--color-1--1);
                }
            }
        }
    }
}
