.newswall-home {
    $this: &;
    position: relative;

    &::before {
        @include size(94.3%, 27.5%);
        @include absolute(38.1%, null, null, 0);
        background-color: var(--color-1--5);
        border-radius: 20px;
        content: "";
        transform: matrix(1, -0.02, 0.02, 1, 0, 0);
        z-index: -1;

        @include breakpoint(medium down) {
            content: none;
        }
    }

    &.section {
        @include breakpoint(medium down) {
            margin-top: 0;
        }
    }

    &__key-box {
        text-decoration: none;
        width: 50%;
    }

    &__container {
        @extend %container;
        position: relative;

        &::before {
            @include size(100vw, 636px);
            @include absolute(-303px, null, null, -40px);
            background-color: var(--color-1--5);
            border-radius: 20px;
            content: "";
            transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
            z-index: -1;

            @include breakpoint(medium down) {
                content: none;
            }
        }

        .key-box {
            &.section {
                margin: 162px 0 0;

                @include breakpoint(medium down) {
                    margin: 60px auto 0;
                    order: 7;
                    width: 510px;
                }

                @include breakpoint(small down) {
                    // margin-top: 134px !important;
                    display: none;
                }

                .section__title {
                    @include font(null, 6rem, var(--fw-bold));
                    margin-bottom: 0;
                    margin-left: 110px;

                    @include breakpoint(medium down) {
                        float: right;
                        width: calc(100% - 180px);
                    }

                    .title__text {
                        @include breakpoint(medium down) {
                            font-size: 4rem;
                        }
                    }
                }
            }
        }

        .key-box-item {
            flex-direction: row;
            flex-wrap: wrap;
            position: relative;

            @include breakpoint(medium down) {
                width: calc(100% - 140px);
            }

            &__title-wrapper {
                @include font(null, 3.4rem, var(--fw-bold));
                background-color: transparent;
                line-height: 41px;
                margin: 0 0 8px 110px;
                padding: 0;

                @include breakpoint(medium down) {
                    font-size: 3rem;
                    width: calc(100% - 140px);
                }
            }

            &__title {
                color: $color-black;

                @include breakpoint(medium down) {
                    width: fit-content;

                    .home-page & {
                        font-size: 3rem;
                    }
                }
            }

            &__description {
                @include font(null, 1.7rem, var(--fw-normal));
                line-height: 26px;
                margin-left: 110px;
                text-align: left;
                width: 408px;
            }

            &__icon {
                @include size(75px);
                @include absolute(80px, null, null, null);
                margin: 0;

                @include breakpoint(medium down) {
                    .home-page & {
                        top: 60px;
                    }
                }

                &::before {
                    @include size(226px, 188px);
                    @include absolute(-49px, null, null, -70px);
                    background-image: image("decor/rayonnement-header-key-box.svg");
                    background-repeat: no-repeat;
                    background-size: contain;
                    content: "";
                    rotate: 20deg;
                }
            }
        }
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin: 0 auto;
        max-width: 1200px;

        @include breakpoint(medium down) {
            align-items: center;
            flex-direction: column;
            flex-wrap: nowrap;
        }

        .newswall-editorial {
            @include size(472px, 100%);
            align-items: center;
            background-color: $color-white;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba($color-black, 0.16);
            display: flex;
            flex-direction: row-reverse;
            position: relative;

            @include breakpoint(medium down) {
                flex-direction: column-reverse;
                margin-left: 0;
                margin-top: 140px !important;
            }

            @include breakpoint(small down) {
                margin: 0 auto !important;
                width: 320px;
            }

            &:nth-child(odd) {
                margin-left: 70px;
            }

            &:nth-child(1) {
                margin-top: 90px;

                @include breakpoint(medium down) {
                    margin-top: 85px !important;
                    order: 0;
                }

                @include breakpoint(small down) {
                    margin-top: 134px !important;
                }
            }

            &:nth-child(4) {
                margin-top: 150px;

                @include breakpoint(medium down) {
                    order: 2;
                }

                @include breakpoint(small down) {
                    margin-top: 128px !important;
                }
            }

            &:nth-child(5) {
                margin-top: 163px;
                position: relative;

                @include breakpoint(medium down) {
                    order: 4;
                }

                @include breakpoint(small down) {
                    margin-top: 119px !important;
                }
            }

            &:nth-child(8) {
                margin-top: 116px;

                @include breakpoint(medium down) {
                    order: 6;
                }

                @include breakpoint(small down) {
                    margin-top: 137px !important;
                }
            }

            &:nth-child(9) {
                margin-top: 137px;

                @include breakpoint(medium down) {
                    order: 8;
                }

                @include breakpoint(small down) {
                    margin-top: 134px !important;
                }
            }

            &__content {
                padding: 36px 52px 44px 46px;

                @include breakpoint(medium down) {
                    padding: 20px 33px 33px 35px;
                }
            }

            &__before-bloc {
                @include size(217px, calc(100% + 27px));
                @include absolute(-17px, null, null, -10px);
                border-radius: 10px;
                transform: matrix(-1, -0.05, 0.05, -1, 0, 0);
                z-index: -1;

                @include breakpoint(medium down) {
                    @include size(calc(100% + 28px), 145px);
                    left: -15px;
                }

                @include breakpoint(small down) {
                    left: -12px;
                    width: calc(100% + 20px);
                }
            }

            &__title {
                @include font(var(--typo-1), 3.5rem, var(--fw-bold));
                line-height: 40px;
                margin-bottom: 14px;

                @include breakpoint(medium down) {
                    font-size: 3.1rem;
                    margin-bottom: 13px;
                }

                @include breakpoint(small down) {
                    font-size: 2.8rem;
                    margin-bottom: 13px;
                }
            }

            &__text {
                @include font(var(--typo-1), 1.7rem, var(--fw-normal));
                line-height: 26px;
            }

            &__learn-more {
                &::after {
                    @include size(100%);
                    @include absolute(0, null, null, 0);
                    border: 1px solid;
                    content: "";
                    display: block;
                    transition: all 250ms ease-in-out 0ms;
                    z-index: 3;
                }
            }

            &__link {
                @include font(var(--typo-1), 1.3rem, var(--fw-bold));
                background-color: transparent;
                border: none;
                color: var(--color--1-1);
                padding: 0;
                position: static;

                span[class*="fa-"] {
                    color: var(--color--1-2);
                    font-size: 2rem;
                    font-weight: var(--fw-light);
                }

                &::after {
                    @include size(calc(100% + 70px), 100%);
                    @include absolute(0, null, null, -70px);
                    content: "";
                    display: block;
                    transition: all 250ms ease-in-out 0ms;
                    z-index: 3;
                }

                @include on-event() {
                    background-color: transparent;
                    border: none;
                    color: var(--color--1-2);
                }
            }

            &__date {
                @include size(116px);
                @include min-size(116px);
                margin-left: -70px;

                @include breakpoint(medium down) {
                    margin-left: 0;
                    margin-top: -85px;
                }

                .date {
                    @include min-size(100%);
                    border-radius: 50%;
                    padding: 12px 20px;

                    &__wrap {
                        align-items: center;
                    }

                    &__icon {
                        margin-left: 5px;
                        margin-right: 5px;
                    }

                    &::before {
                        content: none;
                    }
                }
            }
        }
    }
}
