.interest-item {
    $this: &;

    background: $color-3--1;
    display: flex;
    flex-direction: row-reverse;
    gap: 12px;
    margin: 0 0 20px;
    padding: 35px;

    @include breakpoint(small down, true) {
        flex-direction: column;
        gap: 20px;
    }

    &__dynamic {
        flex-grow: 1;
        width: 100%;
    }

    &__image-wrapper {
        flex-shrink: 0;
        height: 100%;
        max-width: 349px;

        @include breakpoint(medium down, true) {
            max-width: 246px;
        }

        @include breakpoint(small down) {
            max-width: 767px;
            width: 100%;
        }
    }

    &__image {
        img {
            @include size(100%, auto);
            @include object-fit();
            display: block;
        }
    }

    .chart-data {
        max-width: 506px;

        .is-width-66 & {
            max-width: 306px;

            @include breakpoint(medium down) {
                max-width: 506px;
            }
        }

        &__colorbox {
            top: calc(50%);
            transform: translateY(-50%);
        }
    }

    &__vote,
    &__results {
        @include trs;

        @include breakpoint(medium down) {
            width: 100%;
        }

        @include breakpoint(small down, true) {
            width: 100%;
        }

        &.is-hidden {
            @include max-size(0);
            display: none;
            opacity: 0;
            overflow: hidden;
        }
    }
}
