{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitlePrimary -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/utils/utils.njk' import svg, getImagePath with context -%}
{%- from 'views/core-components/link.njk' import Link -%}
{% from 'views/core-components/image.njk' import Image %}

{#
    LocationContent template.
    Template with a map.
    @param {number} itemsCount - count of events
    @param {boolean} type1 - svg map view
    @param {boolean} type2 - view with an image and a single element in the catalog view
    @param {boolean} descriptionTeaser - text presence
#}

{% set defaultItems = [
    [
        'directories-1', 'équipements'
    ],
    [
        'directories-2', 'Associations'
    ],
    [
        'directories-3', 'Commerçants'
    ]
] %}

{% macro LocationContent(items = defaultItems, itemsCount = 2, type1 = false, type2 = false, type3 = false, titleDirectories = 'Annuaires', teaserDirectories = true, descriptionTeaser = true, list = false, image = false, imageSizes = [
    '320x165?767', '644x332?1279', '716x370'
], descriptionTitle = "Sur la carte") %}
    <div class="location {{ 'js-location is-type-2' if type2 }} {{ 'is-type-3' if type3 }}">
        <div class="location__wrapper">
            {% if titleDirectories or teaserDirectories or itemsCount > 0 %}
                <div class="location-directories">
                    {% if titleDirectories %}
                        <h3 class="location-directories__title">{{ titleDirectories }}</h3>
                    {% endif %}
                    {% if teaserDirectories %}
                        <p class="location-directories__teaser">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod</p>
                    {% endif %}
                    <ul class="location-directories__list-items {{ 'is-three-items' if itemsCount === 3 }}">
                        {%- set size = ('84x84' if itemsCount === 3) or ('123x123' if type3) -%}
                        {%- set dimensions = Helpers.split(size, 'x') -%}
                        {%- for icon, text in items %}
                            {% if loop.index <= itemsCount %}
                                <li class="location-directories__item">
                                    <a href="#" class="location-directories__link">
                                        <span class="location-directories__link-icon" aria-hidden="true">
                                            {{ svg('icons/' + icon, dimensions[0] or 115, dimensions[1] or 115) }}
                                        </span>
                                        <span class="location-directories__link-title">{{ text }}</span>
                                    </a>
                                </li>
                            {% endif %}
                        {%- endfor %}
                    </ul>
                </div>
            {% endif %}
            <div class="location-description">
                {% if list %}
                    <ul class="location-list">
                        {%- for title, area in areas %}
                            <li class="location-list__item">
                                <a href="#" data-area-id="{{ loop.index }}" class="location-list__link">{{ title }}</a>
                            </li>
                        {%- endfor %}
                    </ul>
                {% endif %}
                {% if descriptionTitle %}
                    <h3 class="location-description__title">{{ descriptionTitle }}</h3>
                {% endif %}
                {% if descriptionTeaser %}
                    <p class="location-description__teaser">Retrouvez les services et équipements près de chez vous grâce à la carte interactive</p>
                {% endif %}
                {{ Link(
                    className = 'btn is-small is-ternary location-description__link',
                    text = 'Accéder à la carte interactive',
                    icon = 'far fa-arrow-right'
                ) }}
            </div>

            {% if image %}
                <a href="#" class="location-image__link">
                    {{ Image({
                        className: 'location-image',
                        sizes: imageSizes
                    }) }}
                </a>
            {% else %}
                {{ LocationSvgItem(type1, type2) }}
            {% endif %}
        </div>
    </div>
{% endmacro %}

{#
    LocationHome template.
    Template with a map on the home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {boolean} type1 - svg map view
    @param {boolean} type2 - view with an image and a single element in the catalog view
    @param {boolean} descriptionTeaser - text presence
#}
{% macro LocationHome(itemsCount = 2, type1, type2, type3, titleDirectories = 'Annuaires', teaserDirectories = true, descriptionTeaser = true, image = false) %}
    {% call Section(className = 'location-home', modifier="home-feed", container = 'location-home__container') %}
    <div class="section__title">
        {{ TitlePrimary(
                text = "L'Essonne en carto"
            ) }}
    </div>
    <div class="section__content">
        {{ LocationContent(
                itemsCount = itemsCount,
                type1 = type1,
                type2 = type2,
                type3 = type3,
                titleDirectories = false,
                teaserDirectories = teaserDirectories,
                descriptionTeaser = descriptionTeaser,
                image = true,
                descriptionTitle = false
            ) }}
    </div>
    {% endcall %}
{% endmacro %}

{#
    LocationSvg template.
#}

{% macro LocationSvgItem(type1, type2) %}
    <div class="location-map">
        {% if type1 %}
            <div class="location-map__figure" aria-hidden="true">
                {{ svg('sur-la-carte-2', 322, 254) }}
            </div>
        {% elseif type2 %}
            <div class="location-map__figure">
                <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 364.13 395.24" width="364.13" height="395.24" role="list" aria-label="Notre territoire">
                    {% set areas = [
                        [
                            0,
                            "M127.67,191.08l2.88-3.79,5.74-1.31,2.01-2.56,.72-5.25-.68-3.23-2.54-2.25-1.69,3.07-2.05,1.06-1.17-.02-1.39-3.21-1.12-.36-6.55,1.33-1.13,3.08,.96,1.93-1.09,.09-4.06,.79-3.04-1.23-.14-.04,2.53,2.4-2.55,5.26-4.73,3.31-1.34,4.48-.66,2.04-5.47,1.33-2.39,3.75-.33-.29-2.07,.04-1.58,5.16-2.03-.95-1.79,1.33-6.63,.06-.52-1.95-3.05-1.25-1.86-2.69-1.1-.09-1.13,2.11,.15,4.77-1.89,1.46-3.43,.63-3.52,3.19,.2,1.18,2.79,4.54-3.52,4.32-1.18,1.83-7.02,3.65,1.45,5.67-1.95,1.32,.15,1.17,1.68,1.53,2.64-2.18,2.32,.03,1.86,1.36,3.41-.52,2.29,3.98,.41,4.69,.85,4.62,2.54,5.31-.8,4.61,2.22-.64,3.35,.93,.5-.46,.09-2.36,3.53-4.78,.28-7.14,6.8,.73,2.77,5.06,1.11,.38,.34,1.84,2.16,.16,.27,1.05,1.07,2.03,2.19,.62,.9,2.09,3.14,.95,.17,2.2,5.49,4.71,2.05-2.97-5.41-8.75,.26-7.18,1.08-3.43,.64-2.43,2.13-5.07,4.22-5.54,1.11-.37,2.27,2.59,2.12-.85,3.19,.97,1.55-3.31,3.81-2.98,2.36-.37,4.26-3.7,.11-1.18-2.21-2.8-.07-3.42,1.13-1.86-.83-2-8.1-1.61v-4.67l-5.32-2.23,2.13-4.82,1.03-.29,.4-4.49-2.99-1.76,.76-2.28,3.56,.14Z"
                        ],
                        [
                            1,
                            "M265.97,273.21l-2.87,3.78-1.19,.02-1.17,3.33-.93,.64-4.11-1.76-2.05-4.02-1.75,6.65,2.67,2.31,.33,3.54-1.99,2.59-4.05-.47-1.49-1.92-7.13-4.4-1.33-1.94-2.34,.02-.89,3.42,1.69,1.62,2.32,.34,1.05,4.22,2.2,.82,5.47-1.81,.19,1.17-1.82,1.47-11.37,2.71-.79,.88,2.12,.77,2.77,3.35-1.08,.16-.15,1.09,3.42,2.74,.26,2.18,2.58,3.81-.42,2.28,3.15,3.45,.43,.7-5.85,3.97-1.59,3.11-.3,2.28-1.91,2.77,.48,2.23,2.09,.88,1.05-.39,.76,4.4-2.5,3.75,1.02,2-.96,2.08,2.31,2.49,6.41,2.37,3.17,2.71,.92,2.14-3.31,15.08-4.43,1.25-5.5-2.3,1.21,2.98,5.48,1.44,2.59-2.34,4.54,3.5,4.04,5.86,5.1,2.57,3.26,1.18,2.47,2.5,3.46,.5,1.66,3.08-.68,3.44,.29,1.11,3.19,1.15,3.36-.33-.9-2.86,.96-.32-1.78-4.19,2.74-3.74,4.85-4.41,5.75-5.55,.54-2.35-1.61-4.54,.81-.75-.76-5.43,3.49-.75,1.44-1.94,3.31-1.41,10.72-.55,.04-.49,1.29-1.88,3.15-1.2,.41-3.49,4.12-1.87,2.07-4.1,4.14,2.63,2.8-4.82,.41-5.47,3.86,1.35,1.34-7.14-3.27-3.38-2.67-2.09,1.62-3.01-.29-1.13-2.27-.31,1.91-2.72,5.94-2.85-2.1-4.23-5.12-3.16-.91-.52-2.4,1.65-2.08-.17-2.02-1.22-.96-4.68-2.27,.12-.45,1.09,1.89,2.86-.06,2.22-3.43-.04-3.26-1.35-3.12,1.53,.41,5.79-3.54,.16-5.53-.83-.57-2.28,.95-2.18-1.25-1.78-4.13,3.97-2.91-.95-.4-5.55-.82-1.73-3.02-.04-5.95,4.97-3.08-.56-1.67-2.52,1.6-1.45,1.5-6.29-.53-2.69,.17-1.14,3.14-1.28,1.13-1.98,2.28-.07-3-4.88-2.86-1.77,.96-7.11-6.47-2.81-2.71-2.24-5.13,2.68Z"
                        ],
                        [
                            2,
                            "M324.28,226.51l-3-3.34-2.04-.11-3.35-4.5-3.21-.93-1.86-.1-9.06-3.96,1.03-3.48,4.7-6.93-3.07-1.16-2.37-2.27-.7,2.18-2.84,1.92,.3,3.17-2.26,2.51-1.37,1.23-1.68,1.21-.54,1.92,1.47,1.45-.88,.81-2.32-.45-3.9-2.77-7.04,1.01h-.08l-1.68,4.33-2.29,.52-3.42-.96-3,1.59-1.29-2-2.37-.1-3.07,1.71-.61,2.31-2.36,.34-7.23-6.93-.06-1.12-5.87,2.1-7.53,.31-2.41,.06-.41,1.13-1.23,10.69-1.47,4.42-2.62,1.69-.99-.31-4.3,.77-2.7,1.91,.36,2.2-.76,.85-3.35,.15-.58,.98,1.73,7.76,2.28,.46-1.67,2.91-3.07,1.23,.23,2.23-1.24,4.13-.98,.58-2.13-.76-.45,2.32,6.4,5.53,1.22,4.55,2.46,4.56,.68,1.85,5.15-2.05,2.19,.64,3.68,4.32-1.54,3.02-5.56,4.01,.38,3.48-1.59,1.25,.54,3.59,6.93,.25,.79-.88,11.35-2.72,1.82-1.47-.19-1.17-5.46,1.81-2.2-.82-1.05-4.22-2.32-.34-1.69-1.62,.89-3.42h2.35l1.33,1.92,7.12,4.41,1.49,1.92,4.05,.47,1.99-2.59-.34-3.54-2.66-2.31,1.75-6.65,2.04,4.02,4.12,1.76,.93-.64,1.17-3.33,1.19-.02,2.87-3.78,5.13-2.68,2.71,2.24,6.47,2.81,.55-.68,1.3-5.58-3.61-2.99-4.43-1.47,1.65-4.41,3.54-5.08,.98-1.38,2.75-2.2,4.55,.78,.21-5.86,5.54,1.28,2.61,2.25,1.09-.4,3.19,1.35,.67,3.47,5.14,3.01-2.03-2.87-.09-6.95,1.22-3.26,3.12-1.06-1.57-4.45,1.74-2.96,.95-.65,2.05,.87,2.32-2.53,1.13-.08,1.08,4.39,2.93-3.66,4.48,.47,1.85-2.79-.94-5.63Z"
                        ],
                        [
                            3,
                            "M224.38,159.75l-1.36-2.33-10.4,.53-1.81-.5-1.55,1.4-1.1-2.08-5.41-2.26,1.82-4.12-1.54-2.8,.61-.96-7.09-6.01-5.67-.04-1.88-1.42-3.07,1.58-2.52-2.47-5.22,5.02-2.58,.11-1.02,4.61-3.54,.12-2.15,2.87,3.14,3.31-1.21,3.33-.92,.76-3.11-1.59-.9,.79,4.47,9.2,1.95-1.03,3.03,1.52,3.04,7.06,.85-1.15,1.8,2.84,3.32-.33,1.84,2.89,1.62-.16,1.81-.17,.88,.75-1.71,2.58,2.13,.64,2.9,1.77,5.17-2.7,5.81-.41,1.06-.45,.65-3.32,1.99-1.02,1.19,.1,2.18,2.79,3.5,.55,4.75-2.64,3.58-3.51,3.29-2.65-2.62-10.98Z"
                        ],
                        [
                            4,
                            "M287.01,60.5l-3.18-1.61-2.34,.44-1.32,1.92-4.46,1.49-.34,1.14-2.53-4.89-3.04-3.21-.08,.21-3.32-1.23-2.37-4.02-.79-3.51,.83-3.3-3.52-6.21-6.76-1.91-1.52,1.85-1.5,1.28-1.89-.3-1.45-1.9-2.38,.35-3.35-1.25-6.72,2.47-3.46-.24-1.42,1.93-1.18-.22-3.04-3.3-1.03-3.19-1.85-1.28-2.36,3.56-2.52,6.87,4.38,1.58-.05,7.12,2.14,2.78,1.68,9.2-1.45,3.2,.56,2.29-4.94,12.03-1.75-1.65-1.19,.2-4.71,3.54,1.68,3.01-3.65,4.59,1.01,2.05-.67,.97-5.69,1.02-.38,3.11,4.55,3.15-1.93,4.93-5.32,1.89,.99,3.46-.81,5.93-2.85,3.74,1.91,5.68-.81,.8-2.29-.18-2.29,6.9-.14,2.43,1.09,2.18-.47,2.3,7.07,6.03-.61,.95,1.53,2.81-1.83,4.11,5.4,2.27,1.1,2.09,1.56-1.39,1.81,.5,10.4-.5-.2-1.15,2.41-2.36,.04-2.35,3.47-2.99,1.53-9.18,2.88-5.07,2.6-5.4-.33-2.37,6.04-.65,4.12-7.2,7.13,1.21-.95-7.23,5.39-6.03-2.72-2.12-.2-1.16,2.05-2.82-1.36-1.88-3.49-.06-2.38,2.2-1.86-1.43,4.17-5.47-.91-2.16-.26-1.09,2.07,.9,3.54-2.78,3.16-.76,2.83,2.24,3.51,.72,3.64-2.97,2.36,.23,5.14-2.98,1.4-4.38,5.63-2.09,2.57-2.5,1.36-3.35,3.18-3.63,4.1-2.58,.25-1.18-8.03-5.16Z"
                        ],
                        [
                            5,
                            "M357.19,282.51l-.87-3.8-1.89-.46,3.19-3.27,4.37-.18-2.06-6.35,.65-.9-.56-2.14-1.18-.09-2.35-4.13,.14-3.49-1.29-1.9,.18-6.77-1.43-4.54,5.86-1.4,3.68-2.93-3.32-3.72-5.62-7.76-.61-2.34-2.32-.61,1.57-3.12-1.17-2.02-2.32-3.97,.38-3.36-2.43-1.95-.79,.76-.44-1.83,3.46,.08,4.02-2.29,1.12-1.98-.11-2.3-2.76-1.21-.83-.46-4.35-2.06-3.45,1.15-3.51-.57v-2.62l-4.45,1.35-3.1-1.63-2.98,1.88-4.46-1.68-2.5-2.55-6.28-3.18-.54,1.73,1.09,.47,1.07,3.39-.59,5.8-1.12,2.09-4.35-3.07-1.38,1.62-3.06-1.04-4.7,6.92-1.03,3.48,9.06,3.96,1.86,.1,3.21,.94,3.35,4.5,2.04,.11,2.99,3.34,.94,5.64-1.85,2.79-4.48-.47-2.93,3.66-1.08-4.39-1.13,.09-2.32,2.53-2.04-.87-.95,.64-1.73,2.97,1.57,4.46-3.13,1.06-1.22,3.26,.09,6.94,2.03,2.87,4.65-.43,2.14,1.07-.29,1.99-1.58,1.2,2.29,2.65,6.2,3.2,2.3,.1,2.71,2.17,3.22-1.07,2.42,2.31,.67,4.48-.38,.47-6.15,5.34,1.8,10.26-1.1,1.13-.97-.7-2.32,.59-4.06-5.86-2.35,.41,1.63,3.07-.16,2.34-2.66,5.04,1.76,4.01,2.27-.12,.96,4.68,2.03,1.22,2.08,.17,2.4-1.65,.91,.52,5.12,3.16,2.09,4.23,3.33,.14,4.24-1.6,1.84-.46-.88-8.22,1.21-3.3,3.14,.56,2.5-2.29,3.39,.32,.09-.61,.66-4.22,.9-.57,.53-2.75,1.3,.62,4.7-.72,1.49-1.85-.57-3.65-4.38-2.57Z"
                        ],
                        [
                            6,
                            "M186.35,340.03l-1.47-1.31-2.88,.64-3.2-8.33-2.67-.05-.55-3.65-.48-10.17-7.94-2.72-4.27,.19-1.58-1.29-1.5-3.41-1.67-1.5-2.77,1.96,.59,4.51-.86,2.13-3.01-1.42-.6,3.02,.79,1.63-4.66,.16-1.3-4.35-1.16-.29-3.43,.86-1.38,1.95-1.81-1.28-1-3.1-1.29,.11-1.99-2.65-3.84-2.27-2.9,1.77-1-.54-.37,1.13-2.17,.93-5.69-1.58,.13-2.29-1.62,1.64-2.28,.15-7.68,.61-5.09-2.68-3.52-.03-.13,2.15-1.09-.07-3.79-3.24-4.39,.06-4.9-1.76-2.25-.38-2.32,2.22-4.45-.88,.55,4.76,2.04,5.83-.08,4.15,1.51,3.18,4.17,4.17,2.36,1.45,7.23,7.77,2.04,4.19,.7,4.49-1.07,.43-.49,2.33,4.4,3.81-2.2,5.65-.22,4.86,1.44,2.03,2.69-2.18,2.45,2.53,3.48-.17,6.36,3.19,8.54-3.58,4.52-4.82,4.27,.92,1.85-1,3.95-2.74,2.39-.28,3.11,1.37,1.8,3.06,1.16,.2,3.88-2.46,1.15,.27,2.27-7.43,2.69-1.95,.95,.61,4.19,.41,2.79,1.54,.86-.66-.45-2.13,2.11-2.47,4.33,1.51,1.83,4.21,.44,4.17,6.8,.79,2.62-.5-.05-4.93,2.06-5.33-1.97-1.08,3.3-1.37,2.72-5.38Z"
                        ],
                        [
                            7,
                            "M175.99,200.52l-.11-3.43-5.92-.48-.03-5.89-3.16,1.35-1.65,3.09-4.11,4.08-1.15-.26-6.68-5.18,1.21-5.6,7-3.66,1.19-5.24-.94-1.75,2.31-4.22,2.23-.89,1.51-3.26,2.66-2.38-4.47-9.2-4.01,4.39-2.36-.43-4.42,1.88-.98-.7-1.54-1.04-.57,1.92-3.3-1.67-4.91-.63-3.23,1.45-.67,4.69-2.91,5.1-1.19,.12,2.54,2.25,.69,3.23-.72,5.25-2.01,2.56-5.74,1.31-2.88,3.79-3.57-.14-.76,2.28,2.99,1.76-.4,4.49-1.03,.29-2.13,4.82,5.32,2.23v4.67l8.1,1.61,.83,2-1.13,1.86,.07,3.42,2.21,2.8-.11,1.18-4.26,3.71v1.13l1.04,.43,3.89-1.64,.52,1.01,1-1,5.49,4.21,6.92-1.27-1.17-4.21,2.28-.14,2.74-1.89,4.5-.4,.75-2.04-1.91-5.31,2.39-5.09,2.25,.55,3.38-1.14,3.99-1.9,1.26-1.78,2.18-.18,.59-3.4,1.47-1.73-1.38-1.75Z"
                        ],
                        [
                            8,
                            "M330.77,275.35l-.67-4.48-2.42-2.3-3.23,1.07-2.71-2.17-2.3-.1-6.2-3.19-2.29-2.65,1.58-1.2,.29-1.99-2.13-1.07-4.65,.43-5.14-3.01-.67-3.47-3.19-1.35-1.09,.4-2.61-2.25-5.54-1.28-.21,5.86-4.55-.78-2.76,2.2-.98,1.38-3.54,5.08-1.65,4.41,4.43,1.47,3.61,3-1.3,5.58-.55,.67-.96,7.11,2.87,1.77,2.98,4.88-2.28,.07-1.13,1.98-3.14,1.28-.17,1.14,.53,2.69-1.5,6.29-1.6,1.45,1.67,2.52,3.07,.55,5.95-4.96,3.02,.04,.82,1.72,.4,5.55,2.91,.95,4.13-3.97,1.25,1.78-.95,2.18,.57,2.28,5.53,.83,3.55-.16-.41-5.79,3.12-1.53,3.26,1.36,3.43,.04,.06-2.22-1.88-2.86,.45-1.09-1.76-4.01,2.66-5.04,.16-2.34-1.63-3.07,2.36-.4,4.03,5.85,2.32-.59,.97,.7,1.1-1.13-1.8-10.26,6.16-5.34,.39-.48Z"
                        ],
                        [
                            9,
                            "M124.59,112.7l3.55-4.72-2.76-1.93-2.68-.28-3.09-1.67-3.51,.06-1.95-1.11-1.77-1.54-2.28-5.32-2.29-2.6-6.58-2.11-1.1,3.09-1.12,.32-3.56-5.75-3.32-.82-1.27,5.52-4.48-5.16-1.99-.05,.15,4.59,3.11,3.3-3.04,1.3-3.34-.24-2.86,1.89-1.76,2.93-.79,3.77-1.16,3.83-2.3,5.49-.98,5.98,1.84,3.63,1.86,6.32,.08,.08,1.4,6.87-.98,5.81-3.41,2.88-1.4-.15-3.16-.84-3.18,.7-2.51,5.52-1.51,3.65,2.8,2.27,4.1,5.73,1.46-.3,6.24-.25-1.15,2.06,1.43,3.17-3.29,9.6-3.44-.35-1.02,.56-.29,2.3-2.14,.85,1,3.19,.31,2.35,.53,2.35-4.35,4.12-.02,1.2,2.49,2.58,2.38-.09,2.67,2.27,4.75-.13,1.63,1.72,1.2-.02,1.1,.09,1.86,2.69,3.05,1.25,.52,1.95,6.63-.06,1.79-1.33,2.03,.95,1.58-5.16,2.07-.04,.33,.29,2.39-3.75,5.47-1.33,.66-2.04,1.34-4.48,4.73-3.31,2.55-5.26-2.53-2.4,.14,.04-.02-.32,1.53-4.31,3.34-.96-2.34-3.95,.46-5.67-.99-.58,.91-3-2.06-2.57,2.78-5.02,.98-2.26,.02-2.33-2.22-2.78,2.4-2.45-.04-3.5,1.81-3.1,2.8-1.73-.5-1.03-1.45-5.52-3.09-1.49,.79-.85,2.04,1.05,1.04-3.24,1.14-.13,6.26,2.64,.98,.31,2.96-2.57,.15-4.78-8.59-6.36Z"
                        ],
                        [
                            10,
                            "M162.46,4.86l-3.4,3.36-.98-.71-.71,4.29-1.35,1.69,.93,1.77-3.04,1.4-.22,2.27-4.87,2.7,2.05,4.25-2.66,2.25-3.44,.1-2.92,1.85-.74-4.87-2.33,.37-3.21,3.56-1.03,3.47-.78,.93-1.17-.3,.25,1.45,2.89,3.42,.69,2.13-.96,1.86,1.01,1.99-4.33,8.03,1.8,1.37,.14,2.23-1.85,2.88-3.83,2.44-2.32,.17-.19,1.18,2.18,2.8,5.05,3.13,2.3-.63,2.03,3.82-1.26,3.11,3.15-.36,1.99-2.59,3.14-1.18,3.06,1.55-.32,2.22,2.79,1.97,4.35,1.21,3.28-.93,2.03,.9,.4,3.39-1.59,4.4,.71,2.15-.52,3.49-2.29,2.48,4.53-2.39,3.35-3.38,4-2.14,1.51,2.74,3.23,2.79,.91-2.03,4.14-1.41,2.2,2.69,3.26,1.36,2.05,2.84,5.67-.49,2.26-2.41,11.69,2.78,5.69-1.03,.66-.98-1.01-2.05,3.63-4.6-1.69-3,4.71-3.56,1.19-.2,1.75,1.64,4.92-12.05-.56-2.29,1.44-3.2-1.71-9.2-2.14-2.77,.03-7.12-4.38-1.56-2.31,4.12-6.95,1.88-1.37-6.58-1.09-.33-5.9,1.3-2.38-4.21-4.1-4.3,3.3-5.01,2.81-12.52-.41-.02-3.29-1.09-3.98-5.96-5.38-2.19-2.2,.25-2.67,2.06-1.97-1.07-3.1,1.22h-4.68l-2.07-1.24-.81-2.26-3.05-1.51-1.96-4.34-1.66,4.36Z"
                        ],
                        [
                            11,
                            "M235.47,280.81l-3.68-4.32-2.19-.64-5.15,2.05-.68-1.85-2.46-4.56-1.22-4.55-6.4-5.53,.45-2.32,2.13,.76,.98-.58,1.24-4.13-.23-2.23,3.07-1.23,1.67-2.91-2.28-.46-1.73-7.75,.58-.98,3.35-.15,.76-.85-.36-2.19,2.7-1.91-.59-1.07-3.59-.69-1.94-1.47-7.63-12.44-4.47-1.49-2.14,2.33,.48,3.15-2.2,3.82-3.49-.51-.62-.97-2.28-.17-1.59-2.92-2.03-.84v-3.59l-2.24-5.57-2.28-.09-1.74-2.91-.88,.54-1.78,6.67-1.14-.19-5.04-6.11-3.48-2.53-2.18,.17-1.27,1.78-3.99,1.9-3.38,1.15-2.24-.55-2.39,5.09,1.91,5.31-.75,2.04-4.51,.4-2.73,1.89-2.28,.14,1.17,4.21-6.92,1.27-5.49-4.21-1,1-.52-1.01-3.89,1.64-1.04-.43v-1.13l-2.36,.36-3.82,2.98-1.55,3.31-3.19-.96-2.12,.85-2.27-2.59-1.11,.37-4.22,5.54-2.13,5.07-.64,2.43-1.08,3.43-.26,7.18,5.41,8.74-2.05,2.97,3.05,4.1,5.17,2.91-.78,.85,2.94,5.99,4.38-1.18,2.08,.94,2.47-.06,1.11-3.3,.34-3.54,2.74-3.78,1.72-.77,2.94,.52,6.48,3.55,2.65,.07,8.14-.79,1.07,.48,2,4.16,.74-.92,2.78-5.03,5.8,.33-.23-2.14-1.07,.08-1.09-1.85,4.81-3.05-.26-2.3,3.2-.72,4.22,.47,1.5,4.2,.99,.55,1.93-.6,2.3,1.67,2.55-2.3,2.38-.28,5.15-3.02,3.5-.29-3.31,3.47v1.22l6.06,5.7,1.74-1.53,7.21,3.61,1.71,7.37-7.56-.79-1.77,1.06,2.51,3.97,2.35-.52,1.96,1.23,.21,7.35,2.31,2.74,5.08,.38,2.74-3.32-.39-4.28,1.92-2.92,4.66,.46-.42,4.67,1.59-1.25-.38-3.48,5.56-4.02,1.53-3.02Z"
                        ],
                        [
                            12,
                            "M186.31,186.95l-2.33-.57-.9-.85,.75-.52,.17,.17,.77,.15,.16-.32,.59,.14,.13-.41-.29-.15,.11-.52,.39-.26,.84-1.46-.71-.19,.32-1.28,.46-.14,.39-1.33,.82-.99,1.71-.15,.88,.74-1.7,2.58,2.12,.64,.97,2.64-2.95,.93-1.05-.32-1.63,1.47Z"
                        ],
                        [
                            13,
                            "M185.41,184.05l-.11,.52,.3,.15-.14,.41-.59-.14-.16,.32-.77-.15-.17-.17-.76,.52-.77-.86-2.64-6.66-1.09-.49-2.98,3.74-.74-3.41,2.29-.41,1.29-3.09,.85-1.15,1.8,2.84,3.31-.33,1.84,2.89,1.73-.18-.82,.99-.39,1.33-.46,.14-.32,1.28,.71,.19-.84,1.46-.39,.26Z"
                        ],
                        [
                            14,
                            "M78.27,259.74l-2.22,.64-.76,.78-4.08-1.24-3.24,.36-1.19-2.36-6.58,.44-3.33-2.99-3.46-.69-6.83,1.69-1.11,.28-2.15-2.62-5.75-3.83-3.47,.08-3.15,1.42-1.26,2.21-1.73-1.29-2.03,.93-2.09-.9,.86-3.39-3.34-.87-.98,.65-2.28,5.46-1.17,.3-2.01-1.31-.98,.63,.2,2.32-1.29,3.2-1,5.7,3.19,6.28,.94,.94,2.89,3.57-4.07,6.96-.3,.17-.86,3.14-1.9,1.06-.86,2.02,.03,.08-1.47,2.57-1.27,3.03-5.49,1.55-.4,1.09,10.5,2.14,3.26-1.2,4.09,4.08,.07,3.42,2.08,1.11,.74,.03,2.31-.46,2.57,2.25,1.17-.11,1.7-3.98,3.19-.63,2.35-.02,.95,4.41,1.15,.23,.19-.14,2.3-2.8,5.99-.38,8.13,1.44,.46,.5,2.14,.92,4.12-7.09,3.01-1.07,4.79-4.28,.49-.91-4.76-3.61-.43-5.95,1.39-1.93-.27-3.15-2.57-3.44,1.38-6.38,3.97-1.36,3.03-3.24,4.24-.31,5.25-3.41,.35-1.78-3.36-.95Z"
                        ],
                        [
                            15,
                            "M120.22,281.63l1.77,1.52,5.66,.61,1.81-1.24-.2-1.15-2.08-.94-4.38,1.18-2.94-5.99,.78-.85-5.18-2.91-3.04-4.1-5.49-4.71-.17-2.2-3.14-.95-.9-2.09-2.19-.62-1.06-2.03-.28-1.05-2.16-.16-.34-1.84-1.11-.39-2.77-5.06-6.81-.73-.28,7.14-3.53,4.78-.09,2.36-.5,.46-.36,1.78-5.25,3.41-4.24,.31-3.03,3.24-3.97,1.36-1.38,6.38,2.57,3.44,.27,3.15-1.39,1.93,.43,5.95,4.76,3.61-1.15-4.28,4.25,1.53,4.62-.02,3.09,1.52,6.58,6.14-3.44,2.37-.05,3.21,4.9,1.76,4.39-.07,3.79,3.24,1.09,.07,.13-2.15,3.52,.03,5.09,2.68,7.68-.61,2.28-.15,1.62-1.64-.13,2.29,5.7,1.58,2.17-.93,.37-1.13-1.93-1.07-1.9-4.42-4.06-2.16,.73-.9-.31-6.89-3.37-8.59,3.4-4.73,1.13-.24Z"
                        ],
                        [
                            16,
                            "M73.46,245.83l-.42-4.69-2.28-3.98-3.41,.52-1.87-1.36-2.32-.03-2.64,2.18-1.68-1.53-.15-1.16,1.95-1.32-1.45-5.67,7.02-3.65,1.18-1.82,3.52-4.32-2.79-4.54-.21-1.18,3.52-3.19,3.43-.63,1.89-1.46-.15-4.78,1.13-2.11-1.2,.02-1.63-1.72-4.75,.13-2.67-2.27-2.38,.09-2.49-2.58,.02-1.2,4.34-4.12-.52-2.35-.31-2.35-1-3.19,2.14-.85,.28-2.3,1.02-.56,3.44,.35,3.29-9.6-1.43-3.17,1.15-2.06-6.24,.26-1.46,.3-4.1-5.73-2.8-2.27-5.1,1.24-2.1-.49,.45,1.89-1.07,1.47-.51,2.32,1.11,1.99-4.12,4.4-2.34,.65-.66,3.47-2.18,.07-1.03,1.82-4.01-1.58-.39-.36-3.47-3.95-1.96-.5-3.89,2.5-2.35-.12-2.64-2.26-4.63,5.45-2.81,2.14-3.54,.75-1.52,3.9-.65,2.84-5.06,6.28,1.22,1.81,3.53,.51,.61,2.14-9.67,3.95,1.74,4.43-2.9,5.23,.32,3.57,3.68-4.26,5.57-1.6,2.25,.54,6.06,.23,3.4,1.26,3.92,6.08,.73,4.81,1.4,1.99,.42,.49,.23,1.57-4.23,9.67-2.71,2.22-1,4.65-.38-.75-.85,3.44,1.09,7.17,.98-.66,3.34,.87-.86,3.38,2.09,.91,2.03-.93,1.73,1.29,1.26-2.21,3.15-1.42,3.46-.09,5.75,3.83,2.15,2.62,1.11-.28,6.83-1.69,3.46,.69,3.33,2.99,6.58-.44,1.19,2.36,3.24-.36,4.08,1.24,.76-.78,.8-4.61-2.54-5.31-.85-4.62Z"
                        ],
                        [
                            17,
                            "M251.86,351.61l-3.17-2.71-6.42-2.37-2.31-2.49,.96-2.08-1.02-2,2.51-3.75-.76-4.4-1.05,.39-2.08-.88-.48-2.23,1.91-2.78,.3-2.28,1.59-3.11,5.84-3.96-.43-.71-3.16-3.45,.42-2.28-2.58-3.81-.26-2.18-3.42-2.74,.15-1.09,1.08-.16-2.77-3.35-2.12-.77-6.92-.25-.55-3.59,.42-4.67-4.66-.45-1.92,2.92,.39,4.28-2.74,3.31-5.08-.37-2.31-2.74-.21-7.35-1.96-1.23-2.35,.52-2.51-3.97,1.78-1.06,7.56,.79-1.71-7.37-7.21-3.61-1.74,1.53-6.06-5.7v-1.21l3.31-3.47-3.5,.29-5.15,3.02-2.37,.28-2.55,2.3-2.3-1.67-1.92,.6-.99-.55-1.5-4.2-4.22-.47-3.2,.72,.26,2.3-4.82,3.05,1.08,1.85,1.07-.08,.23,2.14-5.8-.33-2.78,5.03,1,3.26-2.32,4.01,.67,4.59,.56,.33,5.89,4.27-1.33,3.56-1.53,3.09-4.37-1.17-1.07,2.12-.27,4.79,2.31,2.74-1.41,1.32,1.5,3.41,1.57,1.29,4.28-.19,7.94,2.72,.48,10.17,.55,3.65,2.66,.06,3.2,8.33,2.88-.64,1.47,1.31-2.73,5.39-3.3,1.37,1.97,1.08-2.06,5.33,.05,4.93,8.06,.35,4.14-1.98,3.39,.5,.76-3.32,3.77,1.94,2.05-.33,.34-3.44,2.69-1.91,4.48,1.42,2.62,2.38,.6,2.28-1.28,2.51,2.34,1.89,4.14,.5-1.92,1.08-.37,3.29-1.95,1.03,.15,5.66-4.55,3.4,1.96,2,1.04,2.88,2.1-.71-.68,4.38,5.5,1.41,2.99,.27,1.53-5.74,4.69-.67,2.9-2.05,1.98-1.09,2.81-3.52,4.16-1.76-1.21-2.99,5.51,2.3,4.43-1.25,3.31-15.08-.92-2.16Z"
                        ],
                        [
                            18,
                            "M238.04,212.97l-4.41-.24-1.3-1.89,.31-7.18-3.17-6.32-.31,.07-.98-4.57-2.28-.58-.77-4.58-2.79-2.1-1.08-2.1-2.47-1.21,1.75-3.09-.41-2.31-4.75,2.64-3.5-.56-2.18-2.79-1.19-.1-1.99,1.03-.65,3.32-1.06,.45-5.81,.41-5.17,2.7-2.91-1.77,.97,2.64-2.96,.93-1.05-.32-1.63,1.46-1.61,3.14,.58,5.83-1.33,1.93-8.01-.73,.11,3.43,1.39,1.77-1.47,1.73-.59,3.4,3.49,2.53,5.03,6.11,1.14,.19,1.78-6.67,.88-.54,1.74,2.91,2.28,.09,2.24,5.57v3.59l2.04,.84,1.59,2.92,2.29,.17,.62,.97,3.49,.51,2.2-3.82-.48-3.15,2.13-2.33,4.46,1.49,7.63,12.44,1.94,1.47,3.6,.69,.59,1.07,4.3-.77,.99,.31,2.62-1.69,1.47-4.42,1.23-10.69,.41-1.13,2.41-.05-1.39-3.05Z"
                        ],
                        [
                            19,
                            "M327.64,111.29l-4.39,3.17-1.86-2.69,.95-2.19-3.36,.36-.95-.72-.35-4.67,1.69-1.37,3.47,.92,2.28-.74-.21-5.6-3.87-1.31-2.53-2.17-2.16-.22-2.72,2.27-1.11-.43-.78-.9-.05-4.67-5.31-2.55-6.59,2.49-1.97-1.25-.4-3.47-3.14-3.3-6.54,2.46-3.9-2.29-.28-3.49-5.63,2.09-1.4,4.38-5.14,2.98-2.36-.23-3.64,2.96-3.51-.72-2.83-2.23-3.16,.76-3.54,2.78-2.06-.9,.26,1.09,.91,2.16-4.17,5.47,1.86,1.43,2.38-2.2,3.49,.07,1.36,1.88-2.05,2.82,.2,1.16,2.72,2.12-5.39,6.03,.95,7.23-7.13-1.22-4.11,7.2-6.04,.65,.33,2.37-2.6,5.4-2.88,5.07-1.53,9.18-3.47,2.99-.04,2.35-2.41,2.36,.2,1.15,1.35,2.34,2.59,10.97,3.7-1.86,3.24,4.73-.04,2.31,3.17,3.11,.14,.05,6.7-1.46,1.8-1.51,2.08,.82,.78,3.3,1.8,1.05,1.78-.99,4.75,1.74,2.65,1.08-.2-9.04-.68-2.28-1.09-.48,2.1-1.63-1.49-1.79-2.56-6.37,.13-2.29-2.26-3.97,.84-2.29,3.81-2.11,3.57-4.21,1.83-1.19,2.51-2.82,4.98,2.92,4.46,1.31,.1,4.36-1.12,2,3.22,2.76,5.46-3.91,3.39-.69,1.5-3.19,2.19-.92,.66-2.86,3.22-2.59,1.1-.05,1.02,1.86,4.38-1.75,3.39,.86,2.91-2.06,5.6,2.08,.58-1.01-1.2-5.72,5.87,.49,3.47-3.17,1.92-4.22,.8-3.42,3.1-1.85,2.4-.17,2.01-1.1,.96-2.08,.84-5.48-5.73-3.87Z"
                        ],
                        [
                            20,
                            "M324.95,181.7l-4.26-4.23-9.43,.94-1.24-2.05,.71-3.52,.57-2.63,5.66-1.94,5.09-4.92-.34-4.69-4.61,.25-4.39-1.88,.61-2.02-3.61-1.91,1.22-5.52,1.67-1.34,.65-3.3-.78-.41-5.6-2.07-2.9,2.06-3.39-.85-4.38,1.77-1.03-1.86-1.11,.05-3.22,2.6-.66,2.86-2.19,.92-1.49,3.19-3.39,.7-5.45,3.93-3.23-2.76,1.11-2-.11-4.36-4.46-1.3-4.99-2.91-2.5,2.83-1.83,1.19-3.55,4.22-3.8,2.13-.84,2.29,2.27,3.96-.12,2.29,2.58,6.37,1.49,1.79-2.1,1.63,1.09,.48,.69,2.28,.22,9.04-2.65-1.07-4.76-1.73-1.78,1-1.8-1.05-.79-3.3-2.09-.82-1.79,1.52-6.7,1.48-.14-.05-3.18-3.1,.04-2.31-3.25-4.73-3.69,1.86-3.29,2.65-3.58,3.51,.41,2.31-1.75,3.09,2.47,1.21,1.07,2.1,2.79,2.1,.77,4.58,2.28,.58,.98,4.58,.31-.07,3.17,6.31-.31,7.19,1.3,1.89,4.41,.23,1.4,3.05,7.53-.31,5.87-2.1,.06,1.13,7.23,6.93,2.35-.34,.61-2.31,3.07-1.71,2.37,.1,1.29,2,3-1.59,3.42,.97,2.29-.52,1.68-4.33h.08l7.04-1.01,3.9,2.76,2.32,.45,.88-.81-1.47-1.45,.55-1.92,1.68-1.21,1.37-1.23,2.26-2.51-.3-3.17,2.84-1.92,.7-2.18,2.37,2.27,3.07,1.16,3.06,1.04,1.38-1.62,4.35,3.07,1.12-2.08,.59-5.8-1.07-3.39-1.09-.47,.54-1.73,.15-2.16,3.21-.1,3.24-3.16,2.41-3.93-.44-1.12Zm-138.7,5.22l-2.33-.57-1.67-1.7-2.64-6.66-1.09-.49-2.98,3.74-.74-3.42,2.29-.41,1.29-3.09-3.04-7.06-3.03-1.52-1.95,1.03-2.66,2.38-1.52,3.26-2.23,.89-2.31,4.22,.94,1.75-1.19,5.24-6.99,3.66-1.22,5.6,6.68,5.18,1.15,.26,4.11-4.07,1.65-3.09,3.15-1.35,.04,5.89,5.92,.48,8.01,.73,1.33-1.93-.58-5.82,1.61-3.15Z"
                        ],
                        [
                            21,
                            "M209.36,100.51l-4.57-3.13,.38-3.11-11.7-2.78-2.26,2.4-5.66,.5-2.05-2.84-3.26-1.36-2.2-2.69-4.14,1.42-.91,2.02-3.23-2.79-1.51-2.74-4,2.14-3.34,3.39-4.53,2.39,2.29-2.48,.52-3.49-.71-2.15,1.59-4.4-.4-3.39-2.03-.9-3.28,.93-4.35-1.21-2.79-1.97,.33-2.22-3.07-1.55-3.14,1.18-1.98,2.59-3.15,.36,1.26-3.11-2.03-3.82-2.31,.63-5.05-3.13-2.18-2.8,.19-1.17-1.95-.42-1.94,4.85-2.07,.57,.39,1.81-1.17,1.38-3.25,4.48-.25,5.53-2.15,.63-3.55-2.76-7.31,2.64-.24,2.34-3.48,.76-1.57,3.08-5.37,2.09-.08,.11,3.32,.82,3.56,5.75,1.12-.32,1.1-3.09,6.58,2.1,2.29,2.6,2.27,5.32,1.77,1.54,1.96,1.11,3.51-.06,3.09,1.67,2.68,.28,2.76,1.93-3.55,4.72,8.59,6.36-.15,4.78-2.96,2.58-.98-.31-6.27-2.65-1.14,.13-1.04,3.24-2.04-1.05-.79,.85,3.09,1.49,1.45,5.52,.5,1.04-2.8,1.73-1.81,3.1,.04,3.5-2.4,2.45,2.22,2.78-.02,2.33-.99,2.26-2.78,5.02,2.06,2.57-.91,3,.99,.59-.46,5.67,2.35,3.95-3.34,.96-1.53,4.31,.02,.33,3.03,1.23,4.06-.79,1.09-.09-.96-1.93,1.13-3.09,6.55-1.33,1.11,.36,1.4,3.21,1.17,.02,2.05-1.06,1.69-3.06,1.19-.12,2.91-5.1,.67-4.7,3.23-1.45,4.91,.63,3.3,1.67,.57-1.92,1.54,1.03,.98,.71,4.42-1.88,2.36,.42,4.01-4.39,.9-.79,3.11,1.59,.92-.76,1.21-3.33-3.14-3.31,2.15-2.87,3.54-.11,1.03-4.61,2.58-.11,5.21-5.02,2.52,2.47,3.07-1.58,1.88,1.4,5.67,.04,.47-2.3-1.09-2.18,.13-2.43,2.27-6.91,2.28,.18,.81-.81-1.92-5.67,2.84-3.75,.79-5.93-1-3.46,5.32-1.9,1.92-4.94Z"
                        ],
                        [
                            22,
                            "M161.41,289.19l-.56-.33-.67-4.59,2.32-4.01-1-3.26-.74,.92-2-4.16-1.07-.49-8.14,.79-2.65-.07-6.48-3.55-2.94-.52-1.71,.77-2.74,3.78-.34,3.54-1.11,3.3,.89,6.77,1.76,1.46,.25,1.16-3.93,5.29-1.57,1.59-.77,1.44-1.1-.56,.78,1.4-.97,5.7-2.3-.45-2.05,.04,1.9,4.42,1.93,1.07,1,.54,2.89-1.77,3.84,2.27,1.99,2.65,1.29-.11h0l1,3.1,1.81,1.28,1.37-1.96,3.43-.86,1.16,.29,1.3,4.35,4.66-.16-.79-1.63,.6-3.02,3.01,1.42,.86-2.13-.59-4.51,2.77-1.96,1.67,1.5,1.41-1.32-2.31-2.74,.26-4.79,1.07-2.12,4.37,1.16,1.53-3.09,1.33-3.56-5.89-4.27Z"
                        ],
                        [
                            23,
                            "M122.7,305.15l-4.06-2.16,.73-.89-.31-6.9-3.37-8.59,3.39-4.73,1.14-.24,1.77,1.52,5.66,.6,1.81-1.24-.2-1.15,2.47-.06,.89,6.77,1.76,1.46,.25,1.17-3.93,5.29-1.57,1.59-.77,1.44-1.1-.56,.78,1.4-.97,5.7-2.3-.45-2.05,.04Z"
                        ]
                    ]%}
                    {%- for title, area in areas %}
                        <g role="listitem">
                            <a aria-label="{{ title }}" data-area-id="{{ loop.index }}" xlink:href="#" role="link" class="location-map__region">
                                <path d="{{ area }}"/>
                                <g class="sr-only">{{ title }}</g>
                            </a>
                        </g>
                    {%- endfor %}
                </svg>
            </div>
            <p class="tooltip-map">Tooltip</p>
        {% else %}
            <div class="location-map__figure" aria-hidden="true">
                {{ svg('sur-la-carte-1', 322, 254) }}
            </div>
        {% endif %}
    </div>
{% endmacro %}