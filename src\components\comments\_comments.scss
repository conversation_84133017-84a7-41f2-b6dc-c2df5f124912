.comments {
    $this: &;

    margin: 20px 0 30px;

    &__title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        display: flex;
        margin: 0 0 20px;

        @include fa-icon-style(false) {
            color: var(--color-2--1);
            margin-right: 5px;
            margin-top: 4px;
        }
    }

    &__listitems {
        list-style: none;
        margin: 0;
        padding: 0;

        &.is-lvl-2 {
            #{$this}__item {
                padding-left: 100px;

                @include breakpoint(small down) {
                    padding-left: 54px;
                }
            }

            .comment-item__content {
                max-width: none;
            }
        }
    }

    &__item {
        margin: 20px 0;
        position: relative;

        &:first-child {
            margin-top: 0;
        }

        &::before {
            @include absolute(null, null, 0, 32px);
            @include size(2px, calc(100% - 86px));
            background-color: var(--color-1--1);
            content: '';

            @include breakpoint(small down) {
                height: calc(100% - 70px);
                left: 26px;
            }
        }

        .is-lvl-2 & {
            margin: 32px 0 20px;

            &::before {
                background-color: var(--color-1--3);
                left: 132px;

                @include breakpoint(small down) {
                    height: calc(100% - 70px);
                    left: 80px;
                }
            }
        }
    }

    &__form {
        background-color: $color-3--1;
        margin: 50px 0 20px;
        padding: 50px 50px 40px;

        @include breakpoint(small down) {
            padding: 30px 30px 20px;
        }

        .form-avatar__label {
            background-color: $color-white;
            border: 1px solid $color-white;
        }
    }
}
