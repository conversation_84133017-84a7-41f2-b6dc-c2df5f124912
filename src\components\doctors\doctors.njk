{%- from 'views/core-components/infos.njk' import InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'components/contacts/contacts.njk' import ContactsItem -%}

{#
    DoctorsItem template.
    @param {object} settings - user settings object
#}
{%- macro DoctorsItem( imageSizes = ['200x200?1279', '250x250'] ) -%}
    <article class="doctors-item">
        {{ Image({
            className: 'doctors-item__picture',
            sizes: imageSizes,
            type: 'no-image' if (range(10, 20) | random) > 15 else 'default',
            serviceID: range(50) | random
        }) }}
        <div class="doctors-item__content">
            <h3 class="doctors-item__name">
                <a href="single-doctors.html" class="doctors-item__name-link">
                    <span class="underline">
                        <span class="doctors-item__name-title">Docteur</span>
                        <span class="doctors-item__name-first">Laurent</span>
                        <span class="doctors-item__name-last">Duisfugiat</span>
                    </span>
                </a>
            </h3>
            <p class="doctors-item__function">Chef de service</p>
            <p class="doctors-item__services">
                <strong>Services</strong>
                {{ lorem(2) }}
            </p>
        </div>
    </article>
{%- endmacro -%}

{#
    DoctorsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} smCols - mobile columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro DoctorsList(
    listClass = 'doctors-list',
    itemClass = 'doctors-list__item has-mb-1',
    count = 6,
    cols = 1,
    mdCols = 1,
    smCols = false,
    xsCols = false
    ) -%}
    {% call List(
        listClass = listClass,
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
        ) %}
        {{ DoctorsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    DoctorInfo template for headings.
#}
{%- macro DoctorsInfo() -%}
    {% set infoTitles = ['Service', 'Equipe médicale', 'Fonction'] %}
    <div class="doctors-info">
        {% for title in infoTitles %}
            <p class="doctors-info-item">
                <strong class="doctors-info-item__category">{{ title }} :</strong>
                <span class="doctors-info-item__title">{{ lorem(1) }}</span>
            </p>
        {% endfor %}
    </div>
{%- endmacro -%}

{#
    ServicesContactsItem template.
#}

{%- macro DoctorsContact(
    imageSizes = ['170x170']
    ) -%}
    {% set titleId = range(100) | random %}

    <section class="doctors-contact">
        <h2 class="ghost">Coordonnées du médecin</h2>
        <div class="doctors-contact__top">
            <div class="doctors-contact__content">
                {{ Image({
                    className: 'doctors-contact__picture',
                    sizes: imageSizes,
                    serviceID: range(50) | random
                }) }}
                <p class="doctors-contact__information">
                    <strong>Informations :</strong>
                    Proin gravida nibh vel velit auctor aliquet. Aenean sollicitudin, lorem quis bibendum auctor, nisi elit nec sagittis sem nibh id elamet nibh
                </p>
            </div>
            <div class="doctors-contact__wrapper">
                <div class="doctors-contact__col">
                    <p class="doctors-contact__contact">
                        {%- call Link(
                            href = 'mailto:<EMAIL>',
                            className = 'btn is-small',
                            icon = 'far fa-at',
                            attrs = {
                            'aria-describedby': 'services-contact-name-' + titleId
                        }
                            ) -%}
                            <span class="btn__text">Contacter</span>
                            <span class="sr-only">Par courriel</span>
                        {%- endcall -%}
                    </p>
                </div>
                <div class="doctors-contact__col">
                    <p class="doctors-contact__contact">
                        <span class="doctors-contact__contact-title">Ligne directe</span>
                        {{ Link(
                            href = 'tel:0465715233',
                            text = '04 65 71 52 33',
                            textSrOnly = 'Téléphone',
                            className = 'btn is-small',
                            icon = 'far fa-phone'
                        ) }}
                    </p>
                    <p class="doctors-contact__contact">
                        <span class="doctors-contact__contact-title">Portable</span>
                        {{ Link(
                            href = 'tel:0639981234',
                            text = '06 39 98 12 34',
                            textSrOnly = 'Téléphone',
                            className = 'btn is-small',
                            icon = 'far fa-mobile-notch'
                        ) }}
                    </p>
                </div>
            </div>
        </div>
        <div class="doctors-contact__bottom">
            <h3 class="doctors-contact__title">Prenez rendez-vous</h3>
            <div class="doctors-contact__item">
                <p class="doctors-contact__item-title">
                    <span>Secrétariat</span>
                </p>
                <div class="doctors-contact__item-links">
                    {{ Link(
                        href = 'tel:0465715233',
                        text = '04 65 71 52 33',
                        textSrOnly = 'Téléphone',
                        className = 'btn is-small',
                        icon = 'far fa-phone'
                    ) }}
                    {{ Link(
                        href = 'mailto:<EMAIL>',
                        text = 'Courriel',
                        className = 'btn is-small',
                        icon = 'far fa-at'
                    ) }}
                </div>
            </div>
        </div>
    </section>
{%- endmacro -%}
