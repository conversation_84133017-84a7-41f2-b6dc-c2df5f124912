body.is-account-page {

    .site-content__inner {
        &::before {
            background: var(--color-1--2);
            content: '';
            display: block;
            height: 100%;
            left: 0;
            position: absolute;
            top: 300px;
            width: 100%;
            z-index: -1;
        }
    }


    .site-content__wrapper {
        padding: 30px 40px 0;

        @include breakpoint(medium down) {
            padding: 25px 62px 0;
        }

        @include breakpoint(small down) {
            padding: 20px 20px 0;
        }
    }

    .site-content__main {
        background-color: $color-white;
        box-shadow: 0 0 20px rgba($color-black, 0.1);
        padding: 50px;
        position: relative;

        @include breakpoint(small down) {
            padding: 40px 20px;
        }
    }
}

body.is-main-account-page {
    background: linear-gradient(to bottom, $color-3--1 1626px, $color-white 1626px);

    @include breakpoint(medium down) {
        background: linear-gradient(to bottom, $color-3--1 1939px, $color-white 1939px);
    }

    @include breakpoint(small down) {
        background: linear-gradient(to bottom, $color-3--1 1280px, $color-white 1280px);
    }

    .site-content__wrapper {
        padding: 0 40px;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }
}
