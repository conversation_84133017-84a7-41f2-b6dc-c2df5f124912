{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitlePrimary -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper -%}
{% from 'views/core-components/icon.njk' import Icon %}
{%- from 'views/core-components/image.njk' import Image -%}

{#
    OnTheWebItem template.
#}
{%- macro OnTheWebItem(
    title = lorem(4, 'words'),
    iconClassName = 'far fa-arrow-up-right-from-square',
    image = false,
    imageSizes = ['147x170'],
    isReverse = false
) -%}
    <div class="ontheweb-item {{'is-reverse' if isReverse}}">
        <div class="ontheweb-item__content">
            {% if image %}
                {{ Image({
                    className: 'ontheweb-item__picture',
                    sizes: imageSizes,
                    serviceID: range(100) | random,
                    alt: 'image alt'
                }) }}
            {% endif %}

            {% if title %}
                <p class="ontheweb-item__title">
                    <a href="https://www.google.fr" target="_blank">{{ title }}</a>
                    {{ Icon(className = iconClassName) }}
                </p>
            {% endif %}
        </div>
    </div>
{%- endmacro -%}

{#
    OnTheWebCarousel template.
#}
{%- macro OnTheWebCarousel(
    count = 7,
    image = false,
    iconClassName = 'far fa-arrow-up-right-from-square',
    carousel = {
        wrapperClassName: 'ontheweb-carousel',
        wrapperAttrs: {
            'aria-label': 'Tous nos sites'
        },
        wrapperTag: 'ul',
        itemsToShow: [5, 3, 1],
        autoplay: false,
        actions: false,
        arrows: {
            prev: {
                icon: 'far fa-long-arrow-left',
                text: 'Site précédent'
            },
            next: {
                icon: 'far fa-long-arrow-right',
                text: 'Site suivant'
            }
        }
    },
    isReverse = false
) -%}
    {% call CarouselWrapper(carousel) %}
    {% for item in range(count) %}
        <li class="ontheweb-carousel__item swiper-item">
            {{ OnTheWebItem(image = image, iconClassName = iconClassName, isReverse = isReverse) }}
        </li>
    {% endfor %}
    {% endcall %}
{%- endmacro -%}

{#
    OnTheWebHome template.
    Template for on-the-web on home page.
#}
{%- macro OnTheWebHome(
    titleIsHidden = true,
    titleText = 'Sur le web',
    image = false,
    iconClassName = 'far fa-arrow-up-right-from-square',
    carousel = {
        wrapperClassName: 'ontheweb-carousel',
        wrapperAttrs: {
            'aria-label': 'Tous nos sites'
        },
        wrapperTag: 'ul',
        itemsToShow: [5, 3, 1],
        autoplay: false,
        actions: false,
        arrows: {
            prev: {
                icon: 'far fa-long-arrow-left',
                text: 'Site précédent'
            },
            next: {
                icon: 'far fa-long-arrow-right',
                text: 'Site suivant'
            }
        }
    },
    isReverse = false
) -%}
    {% call Section(className = 'ontheweb', container = 'ontheweb__container') %}
    <div class="section__title{{ ' ghost' if titleIsHidden }}">
        {{ TitlePrimary(
                text = titleText
            ) }}
    </div>
    <div class="section__content">
        {{ OnTheWebCarousel(carousel = carousel, image = image, iconClassName = iconClassName, isReverse = isReverse) }}
    </div>
    {% endcall %}
{%- endmacro -%}