.header-nav {
    $this: &;

    padding: 0 15px;

    @include breakpoint(medium down) {
        display: none;
    }

    &__list {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    &__item {
        height: 45px;

        &:last-child {
            .header.has-nav-bottom & {
                #{$this}__link {
                    &::after {
                        content: none;
                    }
                }
            }
        }
    }

    &__link {
        @include trs;
        align-items: center;
        background: none;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        display: flex;
        font-size: 2.2rem;
        font-weight: 700;
        height: 100%;
        justify-content: center;
        letter-spacing: 0;
        padding: 5px 20px;
        position: relative;
        text-decoration: none;
        text-transform: none;

        &::after {
            @include trs;
            @include size(1px, 25px);
            @include absolute(50%, -1px, null, null);
            background-color: var(--color-1--1);
            content: '';
            opacity: 1;
            transform: translateY(-50%);
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;

            &::after {
                opacity: 0;
            }
        }

        .header.has-nav-bottom & {
            color: $color-white;

            @include on-event {
                background-color: $color-white;
                color: var(--color-1--1);

                &::after {
                    opacity: 0;
                }
            }

            &::after {
                background-color: $color-white;
            }
        }

        .header:not(.js-fixed-el):not(.has-nav-bottom) & {
            .has-page-image:not(.has-secondary-heading) &,
            body.home-page &,
            body.home-hospital-page & {
                color: var(--color-1--1);

                &::after {
                    background-color: $color-white;
                    content: none;
                }
            }
        }
    }
}
