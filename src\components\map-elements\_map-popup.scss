.js-map-popup-show,
.js-map-popup-hide,
.js-map-animate-marker {
    * {
        pointer-events: none;
    }
}

.map-popup {
    $this: &;

    background-color: var(--color-1--1);
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.16);
    display: flex;
    flex-direction: column;
    height: 100%;
    pointer-events: auto;
    position: relative;

    .map-travaux & {
        background-color: $color-3--1;
        border-radius: 33px;
    }

    &.is-hidden {
        display: none;
        pointer-events: none;
    }

    & &__close[class] {
        @include absolute(0, 0);
        background-color: var(--color-1--1);
        border: none;
        border-radius: 0;
        z-index: 5;

        @include fa-icon-style(false) {
            @include trs;
            font-size: 1.7rem;
            font-weight: var(--fw-light);
            pointer-events: none;
        }

        @include on-event {
            background-color: var(--color-2--2);
        }
    }

    &__content {
        flex-grow: 1;
        height: 1%;
    }

    &__consequence {
        color: $color-white;
        margin-bottom: 10px;

        .map-travaux & {
            color: $color-3--5;

            @include fa-icon-style(false) {
                color: var(--color-1--1);
            }
        }
    }

    &__teaser {
        color: $color-white;
        margin-bottom: 10px;

        .map-popup & {
            color: $color-black;
        }
    }

    &__image {
        display: table !important;
        margin: 37px auto;

        .map-travaux & {
            margin: 0;
        }

        img {
            border-radius: 10px;
        }

        @include breakpoint(small down) {
            max-height: 154px;
            overflow: hidden;

            img {
                width: 100%;
            }
        }

        + #{$this}__descr {
            padding: 18px 37px 0;
        }
    }

    &__content-scroll {
        height: 100%;
        overflow-y: auto;

        .info-item {
            &.is-itineraire {
                align-items: flex-start;
                justify-content: flex-start;
            }
        }
    }

    &__heading {
        background-color: var(--color-1--1);
        color: $color-white;
        display: flex;
        padding: 20px 50px 20px 20px;
    }

    &__heading-icon {
        color: inherit;
        font-size: 2rem;
        margin-right: 10px;

        @include icon-after($fa-var-map-marker);
    }

    &__heading-content {
        color: inherit;
        flex-grow: 1;
        width: 1%;
    }

    &__heading-category {
        @include font(var(--typo-1), 1rem, var(--fw-bold));
        color: inherit;
        text-transform: uppercase;
    }

    &__heading-title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: inherit;
    }

    &__descr {
        padding: 60px 37px 0;
    }

    &__content-wrap {
        @extend %underline-context;
        display: block;
    }

    &__data {
        padding: 0 37px 40px;

        > *:first-child {
            margin-top: 0;
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    &__date {
        margin: 20px 0;
    }

    &__category {
        margin-bottom: 10px;

        &.theme {
            color: $color-white;
            font-weight: var(--fw-normal);
        }
    }

    &__title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-white;
        line-height: 2.4rem;
        margin-bottom: 20px;

        .underline {
            @include multiline-underline($color: $color-white);
        }
    }

    &__item {
        &.is-schedule {
            color: $color-white;
            font-size: 1.4rem;
        }
    }

    &__info {
        margin: 20px 0 20px;

        .info-item {
            &.is-primary {
                a {
                    color: $color-white;
                }
            }

            color: $color-white;

            &.is-itineraire {
                .info-item {
                    &__text {
                        color: $color-white;
                        text-decoration: underline;

                        @include on-event() {
                            text-decoration: none;
                        }
                    }
                }

                svg {
                    fill: var(--color-2--1);
                }

                a {
                    display: flex;
                }
            }

            span[class*="fa-"] {
                color: var(--color-2--1);
            }
        }

        @include fa-icon-style(false) {
            color: var(--color-2--1);
            font-size: 2rem;
            font-weight: var(--fw-light);
            left: 0;
        }
    }

    &__actions {
        margin: 0 0 20px;

        > *:not(:last-child) {
            margin-bottom: 10px;
        }
    }

    &__event {
        margin-bottom: 5px;
        padding: 0 0 40px;
        position: relative;
        z-index: 2;

        &:first-of-type {
            margin-top: 20px;
        }

        &:last-of-type {
            margin-bottom: 30px;
        }
    }

    &__event-content {
        text-align: center;
    }

    &__event-meta {
        align-items: center;
        border-bottom: 1px solid $color-white;
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        padding-bottom: 15px;

        .map-travaux & {
            border-bottom: none;
            display: block;
            margin-top: -64px;
        }
    }

    &__event-date {
        &.date::before {
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .date {
            &__wrap {
                column-gap: 5px;
            }

            &__time {
                align-items: center;
            }
        }
    }

    &__event-hours {
        color: $color-white;
        font-size: 1.4rem;
        line-height: 1;
        margin-top: 15px;
    }

    &__event-theme.theme {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
        color: $color-white;
        letter-spacing: 2.52px;
        margin-bottom: 10px;
        text-transform: uppercase;

        .map-travaux & {
            color: var(--color-1--1);
        }
    }

    &__event-title {
        font-size: 1.7rem;
        line-height: 1.1;
        margin-bottom: 10px;
    }

    &__event-link-travaux {
        color: $color-white;
        text-decoration: none;

        .map-travaux & {
            color: $color-black;
        }

        &::before {
            @include size(100%);
            @include absolute(0, 0);
            content: "";
        }

        &::after {
            @include trs;
            @include size(100%);
            @include absolute(0, 0);
            content: "";
            opacity: 0;
            z-index: -1;
        }

        @include on-event {
            text-decoration: underline;
        }
    }

    &__event-link {
        @extend %link-block;
        @include trs($duration: 300ms);
        background-image: linear-gradient(transparent calc(100% - 0.09em), currentColor 0.09em);
        background-repeat: no-repeat;
        background-size: 0 100%;
        color: $color-white;
        outline: none;
        text-decoration: none;
        width: calc(100%);

        @include on-event {
            background-size: 100% 100%;
            outline: none;
        }
    }
}
