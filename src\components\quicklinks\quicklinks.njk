{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/carousel.njk' import CarouselWrapper, OneClickCarouselWrapper -%}
{%- from 'views/utils/utils.njk' import svg -%}

{#
    QuickLinksInfo template.
#}
{% macro QuicklinksInfo(nameIcon = 'avatar', title = 'Mes <br>démarches', teaser = 'J’accède à mon compte pour mes démarches, mes abonnements, ...') %}
    <div class="quicklinks-info">
        <div class="quicklinks-info__text">
            <div class="quicklinks-info__svg-wrapper" aria-hidden="true">
                {{ svg('icons/' + nameIcon, 60, 60) }}
            </div>
            <h3 class="quicklinks-info__title">
                <a href="#" class="quicklinks-info__link">
                    <span class="underline">{{ title | safe }}</span>
                </a>
            </h3>
        </div>
        <p class="quicklinks-info__teaser">{{ teaser }}</p>
        <span class="quicklinks-info__icon far fa-long-arrow-right" aria-hidden="true"></span>
    </div>
{% endmacro %}

{#
    QuickLinksItem template.
#}
{%- macro QuickLinksItem(
    nameIcon = 'archery',
    title = lorem(1, 'word'),
    modifier = ''
) -%}
    <div class="quicklink-item {{ modifier }}">
        <div class="quicklink-item__svg-wrapper" aria-hidden="true">
            {{ svg('icons/' + nameIcon, 60, 60) }}
        </div>
        <a href="#" class="quicklink-item__text">
            <span class="underline">{{ title }}</span>
        </a>
    </div>
{%- endmacro -%}

{#
    QuickLinksBlock template.
#}
{%- macro QuickLinksBlock(settings = {}) -%}
    {% set params = Helpers.merge({
        listLinks: [
            ['man-and-trash', 'Propreté'],
            ['payment', 'Paiement en ligne'],
            ['cone', 'Infos travaux'],
            ['man-and-message', 'Je signale...'],
            ['route', 'Annuaires'],
            ['family', 'Portail famille'],
            ['phonendoscope', 'Santé'],
            ['communication', 'Contact']
        ],
        modifier: '',
        type: 'carousel',
        carouselItemsToShow: [4, 3, 1],
        itemsCount: 3,
        carouselAttrs: {
            'aria-label' : 'Liens'
        }
    }, settings) %}
    <div class="quicklinks {{ params.modifier }}">
        {% if params.type === 'list' %}
            <nav class="quicklinks__wrapper">
                <ul class="quicklinks__list">
                    {% for link in params.listLinks %}
                        {% if loop.index <= params.itemsCount %}
                            <li class="quicklinks__list-item">
                                {{ QuickLinksItem(
                                    nameIcon = link[0],
                                    title = link[1]
                                ) }}
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
        {% endif %}
        {% if params.type === 'carousel' %}
            {% call CarouselWrapper(settings = {
                wrapperClassName: 'quicklinks-block',
                wrapperTag: 'ul',
                itemsToShow: params.carouselItemsToShow,
                enableNavigationAlign: false,
                arrows: {
                    outside: true,
                    next: {
                        text: 'Accès rapide suivant',
                        icon: 'far fa-long-arrow-right'
                    },
                    prev: {
                        text: 'Accès rapide précédent',
                        icon: 'far fa-long-arrow-left'
                    }
                },
                actions: false,
                autoplay: false,
                wrapperAttrs: params.carouselAttrs
            }) %}
            {% for link in params.listLinks %}
                <li class="quicklinks-block__item swiper-item">
                    {{ QuickLinksItem(
                            nameIcon = link[0],
                            title = link[1]
                        ) }}
                </li>
            {% endfor %}
            {% endcall %}
        {% endif %}
    </div>
{%- endmacro -%}

{#
    OneClickBlock template.
#}
{%- macro OneClickBlock(settings = {}) -%}
    {% set params = Helpers.merge({
        listLinks: [
            ['ico-vieillir',  'Bien veillir' ],
            [ 'ico-offres-emplois',  'Offres d\'emploi' ],
            [ 'ico-archives',  'Archives départementales' ],
            [ 'ico-annuaires',  'Annuaires' ],
            [ 'ico-tremplin-citoyen',  'Tremplin citoyen' ],
            [ 'ico-environnement',  'Environnement' ],
            [ 'ico-grand-projets',  'Grands projets' ],
            [ 'ico-patrimoine',  'Patrimoine' ],
            [ 'ico-jeunesse',  'Jeunesse' ],
            [ 'ico-handicap',  'Handicap' ],
            [ 'ico-randonnees',  'Randonnées' ],
            [ 'ico-parcs',  'Parcs naturels' ],
            ['ico-vieillir',  'Bien veillir' ],
            [ 'ico-offres-emplois',  'Offres d\'emploi' ],
            [ 'ico-archives',  'Archives départementales' ],
            [ 'ico-annuaires',  'Annuaires' ],
            [ 'ico-tremplin-citoyen',  'Tremplin citoyen' ],
            [ 'ico-environnement',  'Environnement' ],
            [ 'ico-grand-projets',  'Grands projets' ],
            [ 'ico-patrimoine',  'Patrimoine' ],
            [ 'ico-jeunesse',  'Jeunesse' ],
            [ 'ico-handicap',  'Handicap' ],
            [ 'ico-randonnees',  'Randonnées' ],
            [ 'ico-parcs',  'Parcs naturels' ]
        ],
        
        modifier: '',
        type: 'carousel',
        carouselItemsToShow: [3, 3, 3],
        actionsWrapper: true,
        arrows: {
                    outside: true,
                    next: {
                        text: 'Accès rapide suivant',
                        icon: 'far fa-long-arrow-right'
                    },
                    prev: {
                        text: 'Accès rapide précédent',
                        icon: 'far fa-long-arrow-left'
                    }
                },
        carouselAttrs: {
            'aria-label' : 'Liens'
        }
    }, settings) %}
    <div class="quicklinks {{ params.modifier }}">
        {% if params.type === 'list' %}
            <nav class="quicklinks__wrapper">
                <ul class="quicklinks__list">
                    {% for link in params.listLinks %}
                        {% if loop.index <= params.itemsCount %}
                            <li class="quicklinks__list-item">
                                {{ QuickLinksItem(
                                    nameIcon = link[0],
                                    title = link[1]
                                ) }}
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
        {% endif %}
        {% if params.type === 'carousel' %}
            {% call OneClickCarouselWrapper(settings = {
                wrapperClassName: 'quicklinks-block',
                jsonPath : "js/data/one-click.json",
                jsClassName: 'js-swiper-oneclick',
                wrapperTag: 'ul',
                itemsToShow: params.carouselItemsToShow,
                enableNavigationAlign: false,
                enableInteractiveAlign:true,
                pagination: 'outside',
                arrows: {
                    outside: true,
                    next: {
                        icon: 'fal fa-chevron-right'
                    },
                    prev: {
                        icon: 'fal fa-chevron-left'
                    }
                },
                actions: false,
                autoplay: false,
                wrapperAttrs: params.carouselAttrs
            }) %}
            {# {% for link in params.listLinks %}
                <li class="quicklinks-block__item swiper-item">
                    {{ QuickLinksItem(
                            nameIcon = link[0],
                            title = link[1]
                        ) }}
                </li>
            {% endfor %} #}
            {% endcall %}
        {% endif %}
    </div>
{%- endmacro -%}

{#
    QuickLinksContent template.
#}
{%- macro QuickLinksContent(
    className = 'quicklinks-content',
    titleText = 'Accès rapides',
    titleClassName = '',
    blockSettings = {},
     className = 'js-sidebar-title',
    description = false,
    anchors = false,
    textAncre = ''
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText,
                className = className,
                anchors = anchors,
                textAncre = textAncre
            ) }}
    </div>
    <div class="section__content">
        {{ QuickLinksBlock(blockSettings) }}
    </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHome template.
#}
{%- macro QuickLinksHome(
    className = 'quicklinks-home has-info-block',
    titleText = 'Accès rapides',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-home__container') %}
    <h2 class="ghost">{{ titleText }}</h2>
    <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
        {{ QuickLinksBlock(blockSettings) }}
        {{ QuicklinksInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
    </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHomeFluid template.
#}
{%- macro QuickLinksHomeFluid(
    className = 'quicklinks-home is-fluid has-info-block has-box-shadow',
    titleText = 'Accès rapides',
    blockSettings = {
        modifier: 'is-inverted'
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-home__container') %}
    <h2 class="ghost">{{ titleText }}</h2>
    <div class="section__content">
        {{ QuickLinksBlock(blockSettings) }}
        {{ QuicklinksInfo(
                nameIcon = 'family',
                title = 'Portail <br>famille',
                teaser = 'Texte 85 Caractères ipsum dolor amet consetetur sadipscing prelitr diam nonumy eirmod'
            ) }}
    </div>
    {% endcall %}
{%- endmacro -%}

{#
    QuickLinksHomeLight template.
#}
{%- macro QuickLinksHomeLight(
    className = 'quicklinks-home is-light',
    titleText = 'Accès rapides',
    shadowBlock = true,
    blockSettings = {
        modifier: 'is-inverted',
        carouselItemsToShow: [7, 3, 1]
    }
) -%}
    {% call Section(className = className, container = 'quicklinks-home__container') %}
    <h2 class="ghost">{{ titleText }}</h2>
    <div class="section__content {{ 'has-box-shadow' if shadowBlock }}">
        {{ QuickLinksBlock(blockSettings) }}
    </div>
    {% endcall %}
{%- endmacro -%}