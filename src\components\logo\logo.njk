{%- from 'views/utils/utils.njk' import wrapper, getImagePath -%}
{%- from 'views/utils/constants.njk' import kGlobalLinks -%}

{#
    Logo template.
#}
{%- macro Logo(isHome = false, image = 'logo-blue.svg', imageSecondary = 'logo-white.svg' ) -%}
    {% call wrapper(
        className = 'logo',
        tag = 'h1' if isHome,
        href = kGlobalLinks.home,
        attrs = {}
    ) %}
        <span class="logo__image">
           <img
               class="logo__image-default"
               src="{{ getImagePath(image) }}"
               alt="{{ '###SITE_NAME###' if isHome else '###SITE_NAME### (retour à l\'accueil)' }}"
               height="50"
               width="300"
           >
            <img
                class="logo__image-secondary"
                src="{{ getImagePath(imageSecondary) }}"
                alt="{{ '###SITE_NAME###' if isHome else '###SITE_NAME### (retour à l\'accueil)' }}"
                height="94"
                width="275"
            >
        </span>
    {% endcall %}
{%- endmacro -%}



