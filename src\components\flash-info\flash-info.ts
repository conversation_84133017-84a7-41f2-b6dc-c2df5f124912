import { key } from 'ally.js/when/_when';
import { tabFocus } from 'ally.js/maintain/_maintain';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import StratisFactory from '@core/core-base/stratis-factory';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import LocalStorage from '@core/core-base/local-storage';
import { getNextFocusableElement } from '@core/utils/a11y.utils';

interface IFlashInfoOptions extends IGlobalOptions<FlashInfo> {
    storage: string;
    expireTime?: number;
}

class FlashInfo extends StratisElementAbstract {
    public options: IFlashInfoOptions = {
        storage: 'flash-info',
        classList: {
            closeButton: 'js-flash-info-close',
            titleText: 'flash-info__title-text',
            hidden: 'is-hidden',
            visible: 'is-visible',
            popup: 'is-popup',
            bodyOpenedHelper: 'js-flash-info-overflow',
            bannerOpenedHelper: 'js-flash-info-banner-opened',
            headerClass: 'header',
        },
        DOMElements: {},
        expireTime: 30,
        dataset: {},
        onInit: () => null,
    };

    private closeButtons: NodeListOf<HTMLElement> | null = null;
    private focusTarget: HTMLElement | null = null;
    private readonly storage: LocalStorage;
    private readonly $closeHandler = this.closeHandler.bind(this);
    private contentHash = '';

    public constructor(element: HTMLElement, options: Partial<IFlashInfoOptions>) {
        super(element);
        this.setOptions(this.options, options);

        this.storage = new LocalStorage(this.options.storage);

        this.init();
    }

    static create(selector: string, options?: Partial<IFlashInfoOptions>): FlashInfo[] | null {
        return StratisFactory.create(selector, options || {}, FlashInfo);
    }

    protected init(): void {
        this.created = true;
        this.focusTarget = getNextFocusableElement(document.body, document.body) as HTMLElement;

        this.contentHash = this.generateHash(this.getStableContent()).toString();

        this.initEvents();
        this.checkRecordStatus();
    }

    protected initEvents(): void {
        this.closeButtons = this.element.querySelectorAll(`.${this.options.classList.closeButton}`);

        if (this.closeButtons) {
            this.closeButtons.forEach(closeButton => {
                closeButton.addEventListener('click', () => this.removeFlashItem(closeButton));
            });
        }
    }

    private generateHash(str: string): number {
        let hash = 0;
        if (str.length === 0) {
            return hash;
        }
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash |= 0;
        }
        return hash;
    }

    private removeFlashItem(closeButton: HTMLElement): void {
        const id = closeButton.getAttribute('aria-describedby')?.split('-')[1];
        const items = document.querySelectorAll('.flash-info-item');
        if (items.length > 1) {
            items.forEach(item => {
                const itemId = item.getAttribute('id');
                if (id && itemId?.includes(id.toString())) {
                    item.remove();
                }
            });
        } else {
            this.closeHandler();
        }
    }

    private hideItem(): void {
        const isPopupOpened = hasClass(document.body, this.options.classList.bodyOpenedHelper);
        const isBannerOpened = hasClass(document.body, this.options.classList.bannerOpenedHelper);

        removeClass(this.element, this.options.classList.visible);
        addClass(this.element, this.options.classList.hidden);

        if (hasClass(this.element, this.options.classList.popup)) {
            this.$focusHandler?.disengage();
            this.$keyHandler?.disengage();

            if (isPopupOpened && this.focusTarget) {
                this.focusTarget.focus();
                removeClass(document.body, this.options.classList.bodyOpenedHelper);
            }
        } else if (isBannerOpened && this.focusTarget) {
            const header = document.querySelector(this.options.classList.headerClass) as HTMLElement;
            this.focusTarget = getNextFocusableElement(this.element, header) as HTMLElement;
            this.focusTarget.focus();
            removeClass(document.body, this.options.classList.bannerOpenedHelper);
        }
    }

    private showItem(): void {
        if (hasClass(this.element, this.options.classList.visible)) {
            return;
        }

        removeClass(this.element, this.options.classList.hidden);
        addClass(this.element, this.options.classList.visible);

        if (hasClass(this.element, this.options.classList.popup)) {
            const titleText = this.element.querySelector(`.${this.options.classList.titleText}`) as HTMLElement;

            if (titleText) {
                titleText.setAttribute('tabindex', '-1');
                titleText.focus();
            }

            addClass(document.body, this.options.classList.bodyOpenedHelper);

            this.$focusHandler = tabFocus({
                context: this.element,
            });

            this.$keyHandler = key({
                context: document.body,
                escape: () => this.closeHandler(),
            });
        } else {
            addClass(document.body, this.options.classList.bannerOpenedHelper);
        }
    }

    private getStableContent(): string {
        const id = this.element.getAttribute('id') || '';

        const titleMain = this.element.querySelector('.flash-info-item__title-main-text')?.textContent || '';
        const title = this.element.querySelector('.flash-info-item__title')?.textContent || '';
        const teaser = this.element.querySelector('.flash-info-item__teaser')?.textContent || '';

        const combined = `${id}-${titleMain}-${title}-${teaser}`;
        return combined.replace(/\s+/g, '').trim();
    }

    private closeHandler(): void {
        const expireTime = Date.now() + (this.options.expireTime || 30) * 24 * 60 * 60 * 1000;
        const current = this.storage.getObj();
        current[this.contentHash] = {
            expire: expireTime,
        };
        this.storage.setItem(current);

        this.hideItem();
    }

    private checkRecordStatus(): void {
        const stored = this.storage.getObj();

        if (!stored[this.contentHash]) {
            this.showItem();

            this.storage.setItem({
                [this.contentHash]: {
                    expire: 0,
                },
            });
            return;
        }

        const storedExpire = stored[this.contentHash]?.expire || 0;
        if (storedExpire > Date.now()) {
            this.hideItem();
        } else {
            this.showItem();
        }
    }
}

export default FlashInfo;
