import { addClass } from '@core/utils/class.utils';
import { setAttributes } from '@core/utils/attr.utils';
import { getViewport } from '@core/utils/responsive.utils';

const createSidebarMenu = (): void => {
    const sidebarTitles = document.querySelectorAll<HTMLElement>('h2.js-sidebar-title .title__ancre');
    const sidebarLists = document.querySelectorAll<HTMLElement>('.sidebar-menu__list');
    const clickAndRollBlockList = document.querySelectorAll<HTMLElement>('.click-and-roll__block');
    const sidebarMenu = document.querySelectorAll<HTMLElement>('.sidebar-menu');
    // Get all the click-and-roll elements inside the sidebar-menu
    const clickAndRollElements = Array.from(sidebarMenu).flatMap(menu => Array.from(menu.querySelectorAll<HTMLElement>('.click-and-roll')));

    if (sidebarLists.length === 0) {
        return;
    }

    const headerOffset = 128; // Offset to account for fixed header

    sidebarLists.forEach((sidebarList: HTMLElement) => {
        sidebarTitles.forEach((title: HTMLElement, index: number) => {
            const id = createUniqueId(title, index);
            setupParentTitle(title, id);
            const listItem = createSidebarItem(title, id, sidebarLists, clickAndRollBlockList, headerOffset);
            sidebarList.appendChild(listItem);
        });
    });

    // Open all click-and-roll menus by default
    clickAndRollElements.forEach((element: HTMLElement) => {
        addClass(element, 'is-open');
        const button = element.querySelector<HTMLButtonElement>('.click-and-roll__toggle');
        const block = element.querySelector<HTMLElement>('.click-and-roll__block');

        if (button) {
            button.setAttribute('aria-expanded', 'true');
        }

        if (block) {
            block.setAttribute('aria-hidden', 'false');
            block.style.display = 'block';
        }
    });

    window.addEventListener('scroll', () => updateActiveSidebarItem(sidebarTitles, sidebarLists, headerOffset));
};

// Helper function to create a unique ID for each title
const createUniqueId = (title: HTMLElement, index: number): string => {
    const idBase = title.textContent?.trim().toLowerCase().replace(/\s+/g, '-');
    return idBase ? encodeURIComponent(`${idBase}-${index}`) : encodeURIComponent(`title-${index}`);
};

// Helper function to set attributes for the parent title
const setupParentTitle = (title: HTMLElement, id: string): void => {
    const parentTitle = title.closest<HTMLElement>('h2.js-sidebar-title');
    if (parentTitle) {
        parentTitle.setAttribute('tabindex', '-1');
        setAttributes(parentTitle, { id });
    }
};

// Helper function to create a list item for the sidebar
const createSidebarItem = (
    title: HTMLElement,
    id: string,
    sidebarLists: NodeListOf<HTMLElement>,
    clickAndRollBlockList: NodeListOf<HTMLElement>,
    headerOffset: number
): HTMLElement => {
    const listItem = document.createElement('li');
    addClass(listItem, 'sidebar-menu__list-item');

    const link = document.createElement('a');
    addClass(link, 'sidebar-menu__link');
    addClass(link, 'is-degradable');
    link.textContent = title.textContent || '';
    link.setAttribute('href', `#${id}`);

    link.addEventListener('click', (event: MouseEvent) => {
        handleLinkClick(event, title, sidebarLists, clickAndRollBlockList, headerOffset);
    });

    listItem.appendChild(link);
    return listItem;
};

// Helper function to remove the "is-active" class from all list items
const removeActiveClassFromAll = (sidebarLists: NodeListOf<HTMLElement>): void => {
    sidebarLists.forEach((list: HTMLElement) => {
        list.querySelectorAll('.sidebar-menu__list-item').forEach(item => {
            (item as HTMLElement).classList.remove('is-active');
        });
    });
};

// Handle the link click event to scroll and activate the item
const handleLinkClick = (
    event: MouseEvent,
    title: HTMLElement,
    sidebarLists: NodeListOf<HTMLElement>,
    clickAndRollBlockList: NodeListOf<HTMLElement>,
    headerOffset: number
): void => {
    event.preventDefault(); // Prevent default anchor behavior

    // Remove "is-active" class from all sidebar items
    removeActiveClassFromAll(sidebarLists);
    const heightscroll:any = document.querySelector('.click-and-roll__wrapper')?.offsetHeight;
    console.log(heightscroll);

    // Scroll smoothly to the selected title with header offset
    const y = title.getBoundingClientRect().top + window.pageYOffset - headerOffset - heightscroll;
    window.scrollTo({ top: y - 100, behavior: 'smooth' });

    // Hide blocks for mobile devices if needed
    if (clickAndRollBlockList.length > 0 && getViewport() === 'smartphone') {
        clickAndRollBlockList.forEach(block => {
            block.style.display = 'none';
        });
    }
};

// Scroll event handler to update active sidebar item based on scroll position
const updateActiveSidebarItem = (
    sidebarTitles: NodeListOf<HTMLElement>,
    sidebarLists: NodeListOf<HTMLElement>,
    headerOffset: number
): void => {
    let currentIndex = -1;

    sidebarTitles.forEach((title: HTMLElement, index: number) => {
        const rect = title.getBoundingClientRect();
        if (rect.top <= headerOffset + 500) {
            currentIndex = index;
        }
    });

    // Remove "is-active" class from all items
    removeActiveClassFromAll(sidebarLists);

    if (currentIndex !== -1) {
        const activeItem = sidebarLists[getViewport() === 'smartphone' ? 1 : 0].children[currentIndex];
        if (activeItem) {
            activeItem.classList.add('is-active');
        }
    }
};

export default createSidebarMenu;
