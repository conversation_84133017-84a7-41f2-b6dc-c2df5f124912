.site-info {
    $this: &;

    display: flex;

    @include breakpoint(medium down) {
        display: block;
    }

    &__column {
        display: flex;
        flex-grow: 1;

        @include breakpoint(medium down) {
            display: block;
            margin-bottom: 25px;
        }

        + #{$this}__column {
            flex-grow: 0;
            flex-shrink: 0;
            margin: 24px 0 0 93px;
            width: 285px;

            @include breakpoint(medium down) {
                margin: 5px 0 0;
                text-align: center;
                width: 100%;
            }
        }
    }

    &__image {
        flex-shrink: 0;
        margin-right: 25px;

        @include breakpoint(medium down) {
            margin: 0 0 30px;
            text-align: center;
        }

        .logo {
            .is-inverted & {
                @include focus-outline($color: $color-white, $offset: 3px);
            }

            img {
                @include breakpoint(medium down) {
                    width: 233px;
                }

                @include breakpoint(small down) {
                    margin: 0 auto;
                    width: 201px;
                }
            }
        }
    }

    &__content {
        color: $color-black;
        flex-grow: 1;
        padding-top: 24px;
        text-align: center;

        @include breakpoint(medium down) {
            padding-top: 0;
        }

        @include add-inverted-styles {
            color: $color-white;
        }
    }

    &__item {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal), normal);
        line-height: 1.25;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            margin: 0 0 7px;
        }

        @include breakpoint(small down) {
            margin: 0 0 5px;
        }

        @include fa-icon-style(false) {
            font-size: 1.8rem;
            margin-right: 5px;
        }

        a {
            @include focus-outline($offset: 2px);
            color: inherit;
            text-decoration: none;

            @include on-event {
                text-decoration: underline;
            }
        }

        &.is-name {
            @include font(null, 1.8rem, var(--fw-bold));
            margin-bottom: 12px;
        }
    }

    &__list {
        display: block;
    }

    &__list-item {
        display: inline-block;
        margin: 5px;

        @include breakpoint(medium down) {
            display: block;
            margin: 11px auto;
        }

        .btn {
            &.is-small {
                letter-spacing: 1.2px;
                min-height: 44px;
                min-width: 285px;
                padding: 14px 65px;
            }
        }
    }
}
