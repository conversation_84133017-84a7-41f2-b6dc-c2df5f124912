{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/utils/utils.njk' import setAttr with context %}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/core-components/form.njk' as Form -%}
{% from 'components/popup-close-btn/popup-close-btn.njk' import PopupCloseBtn %}

{#
    SearchTags template.
#}
{% macro SearchTags(
    title = false,
     keywords = ['Tremplin Jeune Citoyen', 'Suivre mon dossier MDPHE', 'Offres d’emploi', 'Demande Allocation personnalisée d’autonomie']
) %}
    <div class="search-tags">
        {% if title %}
            <h3 class="search-tags__title">Termes les plus recherchés</h3>
        {% endif %}
        <ul class="search-tags__list" aria-roledescription="liste de raccourcis">
            {% for word in keywords %}
                <li class="search-tags__item">
                    <a href="#" class="tag search-tags__keyword">{{ word | capitalize }}</a>
                </li>
            {% endfor %}
        </ul>
    </div>
{% endmacro %}

{#
    SearchPopup template.
#}
{%- macro SearchPopup(
    tags = true
) -%}
    <div class="popup search-popup" id="search-popup">
        <div class="search-popup__wrapper">
            {{ SearchForm(
                buttonClassName = 'search-popup__submit',
                customPlaceholder = 'Saisir un ou plusieurs mots-clés…',
                legendTag = 'h2'
            )}}
            {% if tags %}
                <div class="search-popup__tags">
                    {{ SearchTags(title = true) }}
                </div>
            {% endif %}
        </div>
        {{ PopupCloseBtn(
            modifier = 'btn is-only-icon is-inverted is-small is-outside',
            ghostText = true,
            tooltip = 'Fermer la recherche'
        ) }}
    </div>
{%- endmacro -%}

{#
    Search template.
#}
{%- macro Search(
    dialogLabelText = 'Recherche',
    ghostText = 'Ouvrir la recherche'
) -%}
    <div class="search">
        <a href="#search-popup"
           data-fancybox
           data-small-btn="false"
           data-toolbar="false"
           data-content="Ouvrir la recherche"
           class="search__toggle js-tooltip"
           data-fancybox-body-class="is-search-opened"
           aria-haspopup="dialog"
           data-dialog-label="{{ dialogLabelText }}"
        >
            {{ Icon('fa fa-search') }}
            <span class="ghost">{{ ghostText }}</span>
        </a>
        <div class="search__popup" hidden>
            {{ SearchPopup() }}
        </div>
    </div>
{%- endmacro -%}

{% set defaultSettings = {
    modifier: '',
    attr: true,
    searchBar: true,
    category: true,
    dateTop: false,
    dateBottom: true
} %}

{#
    SearchItem template.
    @param {object} settings - user settings object
#}
{%- macro SearchItem( settings = {} ) -%}
    {%- set params = Helpers.merge(defaultSettings, settings) -%}
    {%- set relevance = range(30, 100) | random -%}

    <article class="search-item {{ params.modifier }}" {%- if params.attr -%} {{ setAttr('data-document-score', '1') }} {{ setAttr('data-document-id', '#') }} {{ setAttr('data-document-url', '#') }}  {%- endif -%}>
        <h3 class="item-title search-item__title">
            {%- if params.category -%}
                <span class="theme search-item__category">{{ lorem(3, 'word') }}</span>
                <span class="sr-only"> : </span>
            {%- endif -%}
            <a href="#" class="item-title search-item__title-link">
                <span class="underline">{{ lorem(1) | replace('dol', '<mark>dol</mark>') | safe }}</span>
            </a>
        </h3>
        {%- if params.searchBar -%}
            <p class="search-item__relevance">
                <span class="search-item__relevance-label">Pertinence :</span>
                <span class="search-item__relevance-bar">
                    <span class="search-item__relevance-bar-bg tx-solr-relevance" style="width: {{ relevance }}%"></span>
                </span>
                <span class="search-item__relevance-percent">{{ relevance }}%</span>
            </p>
        {%- endif -%}
        {%- if params.dateTop -%}
            <p class="publication is-primary search-item__date">
                <span>Mise à jour le</span>
                <time datetime="2022-03-28">28/03/2022</time>
            </p>
        {%- endif -%}
        <p class="item-teaser search-item__teaser">{{ lorem(3) | replace('dol', '<mark>dol</mark>') | safe }}</p>
        {%- if params.dateBottom -%}
            <p class="publication is-primary search-item__date">
                <span>Publié le</span>
                <time datetime="2022-03-28">28/03/2022</time>
                <span>- Mise à jour le</span>
                <time datetime="2023-03-28">28/03/2023</time>
            </p>
        {%- endif -%}
    </article>
{%- endmacro -%}

{#
    SearchList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro SearchList(
    itemClass = 'has-mb-4',
    count = 12,
    cols = 1,
    mdCols = false,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        itemClass = itemClass,
        tag = 'ol',
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ SearchItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    SearchForm template.
#}
{%- macro SearchForm(
    buttonClassName = '',
    customPlaceholder = 'Que recherchez-vous ?',
    label = 'Rechercher',
    labelModifier,
    textHelp,
    legendTag = 'legend'
) -%}
    {% call Form.FormWrapper(
        legend = 'Formulaire de recherche',
        legendTag = legendTag,
        legendClassName = 'ghost'
    ) %}
        {% call Form.FieldGroup(
            label = label,
            inputType = 'search',
            labelModifier = labelModifier,
            textHelp = textHelp,
            type = 'autocomplete',
            customPlaceholder = customPlaceholder,
            customAttrs = {
                required: 'required'
            }
        ) %}
            <button type="submit"
                    class="js-tooltip {{ buttonClassName }}"
                    data-content="Rechercher sur tout le site"
            >
                {{ Icon('far fa-search') }}
                <span class="ghost">Rechercher sur tout le site</span>
            </button>
        {% endcall %}
    {% endcall %}
{%- endmacro -%}

{#
    SearchRelevantList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro SearchRelevantList(
    itemClass = 'has-mb-4',
    count = 6,
    cols = 1,
    mdCols = false,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        itemClass = itemClass,
        tag = 'ol',
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ SearchItem({
            modifier: 'is-relevant',
            attr: false,
            searchBar: false,
            category: false,
            dateTop: true,
            dateBottom: false
        }) }}
    {% endcall %}
{%- endmacro -%}
