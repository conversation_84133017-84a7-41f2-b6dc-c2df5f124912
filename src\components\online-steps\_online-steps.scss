.online-steps {
    $this: &;

    font-size: 1.7rem;

    &__wrapper {
        display: flex;
        margin-bottom: auto;

        @include breakpoint(medium down) {
            padding-bottom: 30px;
        }

        @include breakpoint(small down) {
            display: block;
            padding-bottom: 40px;
        }
    }

    &__image {
        flex: 1 1 50%;
        margin: 0;
        padding: 5px 36px 0 0;

        @include breakpoint(small down) {
            margin-bottom: 20px;
            padding-right: 0;
        }
    }

    &__caption {
        font-weight: var(--fw-bold);
        margin-top: 1em;
    }

    &__block {
        flex: 1 1 50%;
    }

    &__list {
        list-style: none;
        margin: 0;
        padding: 0;

        @include breakpoint(medium only) {
            column-count: 2;
        }
    }

    &__listitem {
        @include icon-before($fa-var-angle-right);
        padding: 5px 10px 5px 16px;
        position: relative;

        &::after {
            @include absolute(null, null, 0, 0);
            border-top: 1px solid rgba($color-black, 0.1);
            content: '';
            width: 50px;
        }

        &::before {
            @include absolute(null, null, null, 0);
            color: var(--color-1--1);
            font-size: 1em;
            font-weight: var(--fw-normal);
        }

        &.is-more {
            padding-top: 15px;

            &::after {
                content: none;
            }

            #{$this}__link {
                color: $color-3--4;
            }
        }
    }

    &__link {
        color: $color-black;
        text-decoration: none;

        @include on-event {
            text-decoration: underline;
        }
    }

    &__buttons {
        padding: 20px 0;

        @include breakpoint(small down) {
            display: flex;
            justify-content: center;
        }
    }
}
