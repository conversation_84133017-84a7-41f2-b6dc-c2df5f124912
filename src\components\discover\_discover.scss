.discover-content,
.discover-home {
    $this: &;

    &.is-full-width {
        #{$this} {
            &__content {
                @include full-width-block;
            }
        }
    }

    &__container {
        @extend %container;
    }

    & &__list-item {
        margin-bottom: 0;

        &:first-of-type {
            .discover-item {
                > :first-child {
                    border-radius: 25px 0 0 0;

                    img {
                        border-radius: 25px 0 0 0;
                    }
                }

                > :last-child {
                    border-radius: 0 25px 0 0;

                    img {
                        border-radius: 0 25px 0 0;
                    }
                }

                &.is-image-left {
                    > :last-child {
                        border-radius: 25px 0 0 0;

                        img {
                            border-radius: 25px 0 0 0;
                        }
                    }

                    > :first-child {
                        border-radius: 0 25px 0 0;
                    }
                }
            }
        }

        &:last-of-type {
            .discover-item {
                > :first-child {
                    border-radius: 0 0 0 25px;

                    img {
                        border-radius: 0 0 0 25px;
                    }
                }

                > :last-child {
                    border-radius: 0 0 25px 0;

                    img {
                        border-radius: 0 0 25px 0;
                    }
                }

                &.is-image-left {
                    > :last-child {
                        border-radius: 0 0 0 25px;

                        img {
                            border-radius: 0 0 0 25px;
                        }
                    }

                    > :first-child {
                        border-radius: 0 0 25px 0;
                    }
                }
            }
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;

            .discover-item {
                > :last-child {
                    border-radius: 25px 25px 0 0 !important;

                    img {
                        border-radius: 25px 25px 0 0 !important;
                    }
                }

                > :first-child {
                    border-radius: 0 !important;
                }
            }
        }
    }
}

.discover-home {
    &__content {
        @include breakpoint(medium only) {
            padding: 0 22px;
        }
    }

    &.is-full-width {
        .title {
            @include breakpoint(large only) {
                margin-bottom: -90px;
                max-width: 100%;
                padding: 0 65px 38px 0;
                position: relative;

                &::before {
                    @include absolute(0, 0, null, null);
                    @include size(100vw, 100%);
                    background-color: $color-white;
                    content: "";
                    z-index: 1;
                }
            }
        }
    }
}
