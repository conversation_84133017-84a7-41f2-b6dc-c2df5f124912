.navbar {
    $this: &;

    &__wrapper {
        @extend %container;
        background-color: $color-white;
        display: flex;
        justify-content: space-between;
        padding: 32px 40px 3px;
        position: relative;
        z-index: 2;

        @include breakpoint(small down) {
            padding: 20px 16px 20px 20px;
        }
    }

    &__breadcrumbs {
        align-items: center;
        display: flex;
        width: 88%;

        @include breakpoint(medium down) {
            width: 71%;
        }
    }

    &__tools {
        width: 12%;

        @include breakpoint(medium down) {
            width: 29%;
        }
    }

    &.has-negative-margin {
        .has-page-image & {
            margin-top: -190px;

            @include breakpoint(medium down) {
                margin-top: -55px;
            }

            @include breakpoint(small down) {
                margin-top: 0;
            }

            #{$this}__wrapper {
                @include breakpoint(medium down) {
                    margin: 0 62px;
                    width: auto;
                }

                @include breakpoint(small down) {
                    margin: 0;
                }
            }
        }
    }
}
