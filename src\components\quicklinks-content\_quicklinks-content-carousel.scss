.quicklinks-content-block {
    position: relative;

    &__container {
        width: calc(100% - 80px);
    }

    &__wrapper {
        display: flex;
    }

    &__control {
        @include absolute(50%);
        @include font(null, 2rem, var(--fw-normal));
        @include size(50px);
        background: none;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        line-height: 0.9;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        &.is-prev {
            left: -15px;
        }

        &.is-next {
            right: -15px;
        }

        .is-inverted & {
            color: $color-white;

            @include on-event {
                background-color: $color-white;
                color: var(--color-1--2);
            }
        }
    }

    &__item {
        &:first-child {
            margin-left: auto;
        }

        &:last-child {
            margin-right: auto;
        }
    }
}
