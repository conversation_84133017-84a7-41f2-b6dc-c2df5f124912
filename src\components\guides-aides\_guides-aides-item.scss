.guides-aides-item {
    @extend %link-block-context;
    background-color: $color-3--1;
    border-radius: 10px;
    height: 100%;
    margin: 0 auto;
    max-width: 384px;

    @include breakpoint(small down) {
        height: auto;
    }

    &__content {
        border-bottom: 10px;
        padding: 35px 35px 90px;

        @include breakpoint(small down) {
            padding-bottom: 35px;
        }

        > *:last-child {
            margin-bottom: 0;
        }

        .widget & {
            text-align: center;
        }
    }

    &__category {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
        background-color: var(--color-1--1);
        border-radius: 10px 10px 0 0;
        color: $color-white;
        display: block;
        line-height: 1.2;
        padding: 20px 35px;
        text-transform: uppercase;
    }

    &__title {
        font-size: 2.2rem;
        margin-bottom: 20px;
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__teaser {
        font-size: 1.4rem;
        margin-top: 10px;
    }

    .section {
        &__more-links {
            @include absolute(null, null, 20px, 50%);
            transform: translateX(-50%);
            z-index: 5;

            @include breakpoint(small down) {
                bottom: 0;
                left: 0;
                margin-top: 20px;
                position: relative;
                transform: none;
            }

            .btn {
                background-color: var(--color-1--1);
                border-color: var(--color-1--1);
                color: $color-white;
                min-height: 44px;
                padding: 14px 30px;
                z-index: 1;

                @include on-event() {
                    background-color: var(--color-2--1);
                    border-color: var(--color-2--1);
                }

                span[class*=fa-] {
                    color: $color-white;
                }
            }
        }
    }
}
