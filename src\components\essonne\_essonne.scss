.essonnes-home {
    position: relative;

    @include breakpoint(large only) {
        margin: 230px 0 40px !important;
    }

    &::before {
        @include size(48.3%, 100%);
        @include absolute(-70px, 0, null, null);
        background: var(--color-1--5);
        border-radius: 0 20px 20px 0;
        content: "";
        transform: matrix(-1, -0.02, 0.02, -1, 0, 0);

        @include breakpoint(medium down) {
            @include size(585px, calc(100% - 150px));
            top: 120px;
            transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
        }

        @include breakpoint(small down) {
            @include size(calc(100vw + 50px), calc(100% - 240px));
            @include absolute(null, -25px, null, -25px);
            border-radius: 0;
            top: 190px;
            transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
        }
    }

    &.section {
        @include breakpoint(medium down) {
            margin: 75px 0;
        }

        @include breakpoint(small down) {
            margin: 89px 0 70px;
        }
    }

    .section {
        @include breakpoint(medium down) {
            margin: 75px 0 !important;
        }

        &__title {
            @include absolute();
            align-items: flex-start;
            flex-direction: column;
            margin-bottom: 0;
            width: 400px;

            .title__text {
                &::before {
                    rotate: -11deg;
                    top: -11px;
                }
            }

            @include breakpoint(medium down) {
                @include reset-position();
                align-items: center;
                margin-left: 0;
                width: 100%;
            }

            @include breakpoint(small down) {
                align-items: center;
                margin-left: 0;
            }
        }
    }

    &__container {
        @extend %container;
        position: relative;

        @include breakpoint(medium only) {
            padding: 0 62px;
        }
    }

    &__content {
        display: grid;
        grid-column-gap: 0;
        grid-row-gap: 0;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(1, 1fr);

        @include breakpoint(medium down) {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            margin-top: 129px;
        }

        @include breakpoint(small down) {
            display: block;
            margin-top: 35px;
        }
    }

    &__more-links {
        justify-content: flex-end !important;
        margin-right: -50px;
        transform: translateY(-55px);

        .btn {
            font-size: 1.3rem;

            @include breakpoint(small down) {
                font-size: 1.2rem;
            }
        }

        @include breakpoint(medium down) {
            margin: 20px auto 0;
            transform: none;
            width: fit-content;
        }

        @include breakpoint(small down) {
            margin: 60px auto 20px;
        }
    }
}
