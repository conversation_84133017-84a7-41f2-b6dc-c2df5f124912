.hero-search {
    border-radius: 40px;
    box-shadow: 0 0 16px rgba($color-black, 0.35);
    position: relative;

    @include breakpoint(small down) {
        display: flex;
        justify-content: center;
        margin: 0 auto;
        max-width: 311px;
    }

    &__btn {
        @include trs;
        @include size(80px);
        background-color: $color-white;
        border: 0;
        border-radius: 0 40px 40px 0;
        color: var(--color-1--1);
        cursor: pointer;
        font-size: 2.4rem;
        margin-left: -1px;
        padding-right: 42px;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        @include breakpoint(medium down) {
            @include size(63px);
        }
    }

    .form {
        &__controls-group {
            display: flex;
            margin-bottom: 0;
            position: relative;

            &::after {
                @include breakpoint(medium down) {
                    @include absolute(-54px, null, null, -64px);
                    @include size(160px, 192px);
                    background-image: image('decor/rayonnement-header-tablette.svg');
                    background-repeat: no-repeat;
                    background-size: cover;
                    content: '';
                    z-index: -1;
                }
            }
        }

        &__field-wrapper {
            flex-grow: 1;
            width: 1%;

            &.js-autocomplete.is-visible {
                .form__field {
                    color: $color-black;
                }
            }

            .js-autocomplete-input-clear {
                @include breakpoint(small down) {
                    right: 10px;
                }
            }
        }

        &__field {
            border: none;
            border-radius: 40px 0 0 40px;
            color: var(--color-1--1);
            font-size: 2.2rem;
            height: 80px;
            padding: 15px 20px 15px 42px;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                height: 63px;
                padding: 10px 20px 10px 25px;
            }

            &::-ms-clear {
                display: none;
            }

            &:focus ~ .form__field-placeholder,
            &:not(:focus):valid ~ .form__field-placeholder {
                font-size: 1.2rem;
                left: 19px;
                top: 9px;
                transform: translateY(0);
            }
        }

        &__field-placeholder {
            @include trs;
            @include absolute(50%, 20px, null, 42px);
            color: var(--color-1--1);
            font-size: 2.2rem;
            overflow: hidden;
            pointer-events: none;
            text-overflow: ellipsis;
            transform: translateY(-50%);
            white-space: nowrap;

            @include breakpoint(medium down) {
                font-size: 1.8rem;
                left: 25px;
            }

            @include breakpoint(small down) {
                font-size: 1.6rem;
            }
        }
    }
}
