.quicklinks-block {
    position: relative;

    .click__block & {
        padding-bottom: 70px;

        .quicklink-item__text {
            @include font(var(--typo-1), 1.1rem, var(--fw-bold));
            color: $color-black;

            .underline {
                @include multiline-underline($color: $color-black);
            }
        }
    }

    &__wrapper {
        display: flex;
    }

    &__control {
        @include absolute(null, null, -15px, null);
        @include font(null, 2rem, var(--fw-normal));
        @include size(50px);
        background: none;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        line-height: 0.9;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        &.is-prev {
            font-size: 4rem;
            left: 20px;
        }

        &.is-next {
            font-size: 4rem;
            right: 20px;
        }

        .is-inverted & {
            color: $color-white;

            @include on-event {
                background-color: $color-white;
                color: var(--color-1--2);
            }
        }
    }

    &__pagination {
        @include absolute(null, null, 30px, 44%);
        display: flex;
        z-index: 2;

        @include breakpoint(small down) {
            justify-content: center;
            margin: 40px 0 0 0 !important;
            text-align: center;
        }

        .swiper-pagination {
            &__bullet {
                @include size(10px);
                background-color: transparent;
                border: 1px solid $color-3--4;
                border-radius: 5px;
                margin-right: 6px;
                position: relative;

                &.is-active {
                    background-color: var(--color-1--1);
                    width: 26px;
                }
            }

            &__bullet-btn {
                @include absolute();
                @include size(10px);
                border-radius: 5px;
                opacity: 0;
                padding: 0;
            }
        }
    }
}

.quicklinks-block__container.no-swiper {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.quicklinks-block__container.no-swiper .swiper-wrapper {
    display: flex;
    flex-wrap: wrap;
}

.quicklinks-block__container.no-swiper .swiper-slide {
    flex: 1 1 30%;
    max-width: 100%;
}

.quicklinks {
    @include breakpoint(small down) {
        width: 100%;
    }
}
