.location {
    $this: &;

    &.is-type-3 {
        &::before {
            @include size(100%, calc(100% + 40px));
            @include absolute();
            background-color: var(--color-1--5);
            border-radius: 20px;
            content: "";
            transform: matrix(-1, -0.02, 0.02, -1, 90, 0);

            @include breakpoint(medium down) {
                @include size(100vw, 558px);
                transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
            }

            .home-feed & {
                content: none;
            }
        }

        #{$this}__wrapper {
            align-items: flex-end;
            flex-direction: column-reverse;
            min-height: 260px;
            padding-top: 50px;
            width: calc(100% - 215px);

            @include breakpoint(medium down) {
                margin: 0 auto;
                width: fit-content;
            }

            @include breakpoint(small down) {
                align-items: center;
                flex-direction: column;
                margin: 40px auto 0;
                width: 100%;
            }
        }
    }
}

.location-directories {
    $this: &;

    .is-type-3 & {
        display: flex;
        flex-direction: column;
        max-width: 520px;
        padding: 20px 70px 70px;
        width: 100%;

        @include breakpoint(medium down) {
            align-items: center;
            margin: 0;
            max-width: 320px;
            padding: 40px;
        }

        @include breakpoint(small down) {
            margin-bottom: 40px;
            max-width: none;
        }

        #{$this}__title {
            color: $color-white;
            display: block;
            margin: 0 0 0 145px;
            transform: translateY(60px);

            @include breakpoint(medium down) {
                margin: 0 0 5px;
                transform: none;
            }

            @include breakpoint(small down) {
                font-size: 3.5rem;
                line-height: 4rem;
            }
        }

        #{$this}__list-items[class] {
            margin-top: 0;
        }

        #{$this}__link {
            @include focus-outline($color: $color-white);
            align-items: flex-end;
            flex-direction: row;
            padding: 0;

            @include breakpoint(medium down) {
                flex-direction: column;
                text-align: center;
            }

            @include on-event {
                #{$this}__link-title {
                    background-color: var(--color-2--3);
                    border-color: var(--color-2--3);
                    color: var(--color-1--2);
                }
            }
        }

        #{$this}__link-icon {
            @include size(123px);
            margin: 0 30px 0 0;

            @include breakpoint(medium down) {
                @include size(92px);
                margin: 0 auto 10px;
            }

            svg {
                fill: $color-white;
            }
        }

        #{$this}__link-title {
            border-color: $color-white;
            color: $color-white;
            //margin: 0 0 5px;
            min-height: 45px;
            padding: 10px 20px;
        }
    }
}

.location-map {
    .is-type-3 & {
        bottom: 30px;
        right: 330px;
        top: auto;
        transform: none;

        @include breakpoint(medium down) {
            bottom: auto;
            right: calc(50% - 390px);
            top: -80px;
            transform: translateX(-50%);
        }

        @include breakpoint(small down) {
            @include reset-position;
            margin-bottom: -80px;
            transform: none;
        }
    }
}

.location-description {
    $this: &;

    .is-type-3 & {
        max-width: none;
        min-height: auto;
        padding: 0;
        width: 100%;

        @include breakpoint(medium down) {
            align-items: center;
            justify-content: flex-end;
            padding: 0 0 65px 37px;
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;
            margin-top: -40px;
            padding: 0;
        }

        #{$this}__title {
            margin: 0 0 10px;
        }
    }
}

.location-image {
    border-radius: 10px;

    &__link {
        border-radius: 10px;
        position: relative;

        &::before {
            @include size(415px, 223px);
            @include absolute(null, null, -60px, -200px);
            content: "";
            background: {
                image: image("decor-carto.svg");
                repeat: no-repeat;
            }
            z-index: 2;

            @include breakpoint(medium down) {
                @include absolute(-100px, null, 100%, calc(50% - 156px));
                @include size(312px, 168px);
                background-size: cover;
            }

            @include breakpoint(small down) {
                left: calc(50% - 113px);
                @include size(226px, 121px);
            }
        }

        &::after {
            @include size(100%);
            @include absolute(0, null, null, -25px);
            background-color: var(--color-1--1);
            border-radius: 10px;
            content: "";
            transform: matrix(0.99, -0.1, 0.1, 0.99, 0, 0);
            z-index: 0;

            .home-feed & {
                @include breakpoint(small down) {
                    left: -12px;
                }
            }
        }
    }

    img {
        @include size(716px, 369px);
        border-radius: 10px;
        max-height: none;
        max-width: none;
        position: relative;
        z-index: 1;

        @include breakpoint(medium down) {
            @include size(643px, 332px);
        }

        @include breakpoint(small down) {
            @include size(100%);
        }
    }
}
