.edition-item {
    @extend %link-block-context;
    cursor: pointer;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-left: -200px;
    position: relative;
    width: 637px;

    @include breakpoint(medium down) {
        margin-left: 0;
        padding-left: 43px;
        padding-top: 8px;
        width: 100%;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
        padding-left: 0;
        text-align: center;
    }

    &__image {
        @include size(303px, 427px);
        display: inline-block;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(199px, 281px);
            margin-right: 13px;
            margin-top: 39px;
            z-index: 1;
        }

        @include breakpoint(small down) {
            @include size(158px, 223px);
            margin: 0 auto;
        }

        img {
            @include object-fit;
            @include size(100%);
            background-color: $color-white;
            box-shadow: 0 0 20px rgba($color-black, 0.16);
        }
    }

    &__content {
        display: none;
        flex-grow: 1;
        padding: 0 0 0 80px;
        width: 1%;

        @include breakpoint(medium down) {
            padding: 0 0 0 22px;
        }

        @include breakpoint(small down) {
            padding: 34px 0 0;
            width: 100%;
        }

        .swiper-slide-active & {
            background-color: var(--color-1--1);
            display: block;
            margin-left: -196px;
            max-width: 385px;
            padding-left: 0;
            padding-top: 42px;

            @include breakpoint(medium down) {
                margin-left: 0;
                max-width: 338px;
                padding-top: 0;
            }

            @include breakpoint(small down) {
                padding-top: 20px;
            }
        }
    }

    &__category {
        color: $color-white;
        font-weight: var(--fw-normal);
    }

    &__title {
        &.is-large {
            color: $color-white;
            font-size: 3.5rem;

            .underline {
                @include multiline-underline($color: $color-white);
            }

            @include breakpoint(medium down) {
                font-size: 3.1rem;
            }

            @include breakpoint(small down) {
                font-size: 2.2rem;
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &::after {
            width: calc(100% + 196px);
        }

        &:focus {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__subtitle {
        @include font(null, 3rem, var(--fw-normal));
        color: $color-white;
        display: block;
        margin: 11px 0 0;

        @include breakpoint(medium down) {
            @include font(null, 2.4rem, var(--fw-normal));
            margin-top: 10px;
        }

        @include breakpoint(small down) {
            font-size: 1.9rem;
            margin-top: 5px;
        }
    }

    &__teaser {
        color: $color-white;
        margin-top: 20px;

        @include breakpoint(medium down) {
            margin-top: 24px;
        }

        @include breakpoint(small down) {
            font-size: 1.5rem;
            margin-top: 5px;
        }
    }

    &__publication {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        color: $color-3--4;
        display: block;
        margin-top: 32px;
        padding-left: 45px;
        position: relative;
        text-transform: uppercase;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include absolute(7px, null, null, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: "";

            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__number {
        @include font(null, null, var(--fw-bold), normal);
        display: block;
    }

    &__actions {
        margin-top: 45px;
        position: relative;
        z-index: 3;

        @include breakpoint(medium down) {
            margin: 35px auto 0;
            max-width: 180px;
        }

        .home-page & {
            .document-actions {
                &__item {
                    @include size(80px);
                    border-radius: 50%;
                    padding: 0;

                    @include breakpoint(medium down) {
                        @include size(60px);
                    }

                    @include breakpoint(small down) {
                        @include size(45px);
                    }
                }

                &__link {
                    &.is-download {
                        @include min-size(100%);
                        @include size(80px);
                        background-color: $color-white;
                        margin-right: 12px;

                        @include breakpoint(medium down) {
                            @include size(60px);
                        }

                        @include breakpoint(small down) {
                            @include size(45px);
                            padding: 0;
                        }

                        @include fa-icon-style(false) {
                            color: var(--color-1--1);
                            font-size: 3rem;
                            font-weight: var(--fw-light);

                            @include breakpoint(medium down) {
                                font-size: 2rem;
                            }

                            @include breakpoint(small down) {
                                font-size: 1.3rem;
                            }
                        }
                    }

                    &.is-read {
                        @include min-size(100%);
                        @include size(80px);
                        background-color: var(--color-2--1);

                        @include breakpoint(medium down) {
                            @include size(60px);
                        }

                        @include breakpoint(small down) {
                            @include size(45px);
                            padding: 0;
                        }

                        @include fa-icon-style(false) {
                            color: var(--color-1--2);
                            font-size: 3rem;
                            font-weight: var(--fw-light);

                            @include breakpoint(medium down) {
                                font-size: 2rem;
                            }

                            @include breakpoint(small down) {
                                font-size: 1.3rem;
                            }
                        }
                    }
                }
            }
        }

        @include breakpoint(small down) {
            display: block;
        }

        .document-actions {
            @include breakpoint(small down) {
                align-items: center;
                justify-content: center;
            }
        }
    }
}
