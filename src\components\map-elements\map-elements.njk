{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/core-components/dropdown.njk' import Dropdown -%}
{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/infos.njk' import CreateInfoItem, InfoItem, InfoBlock -%}
{%- from 'views/core-components/secondary.njk' import Date, Ripple -%}
{%- from 'views/utils/utils.njk' import svg with context -%}

{#
    MapHomeLink template.
#}
{%- macro MapHomeLink(
    icon = 'fas fa-home',
    title = 'Accuel'
    ) -%}
    <a href="{{ kGlobalLinks.home }}" class="map-home-link">
        {{ Icon(className = icon, title = title) }}
        <span class="map-home-link__text">Accuel</span>
    </a>
{%- endmacro -%}

{#
    MapListLink template.
#}
{%- macro MapListLink(
    icon = 'fas fa-list'
    ) -%}
    <a href="{{ kGlobalLinks.listNews }}" class="map-list-link">
        {{ Icon(icon) }}
        <span class="map-list-link__text">Vue liste</span>
    </a>
{%- endmacro -%}

{#
    MapMapsSwitcher template.
#}
{%- macro MapMapsSwitcher(
    links = [ 'Annuaire', 'Agenda', 'Projects' ]
    ) -%}
    {% call Dropdown({
        wrapper: {
            className: 'map-maps-switcher'
        },
        toggle: {
            text: 'Annuaire'
        }
    }) %}
    <ul class="map-maps-switcher__list">
        {% for item in links %}
            <li class="map-maps-switcher__item">
                {{ Icon('far fa-caret-right') }}
                <a href="#" class="map-maps-switcher__link">{{ item }}</a>
            </li>
        {% endfor %}
    </ul>
    {% endcall %}
{%- endmacro -%}

{#
    MapRouteLink template.
#}
{%- macro MapRouteLink() -%}
    {% call CreateInfoItem() %}
    <a href="https://google.com" class="js-map-route">
        {{ Icon('far fa-location-arrow') }}
        Itinéraire
    </a>
    {% endcall %}
{%- endmacro -%}

{#
    MapPopup default settings.
    @prop {boolean} useEvents - add elements for map of events.
    @prop {boolean} useTitleLink - add link for title .
    @prop {boolean} useActions - add actions.
    @prop {boolean} mapPopupHeading - add heading to popup.
#}
{% set defaultPopupParams = {
    useEvents: false,
    useTitleLink: false,
    useActions: false,
    schedules: false,
    mapPopupHeading1: false,
    mapPopupHeading2: false,
    mapPopupHeading3: false,
    mapPopupHeading: false,
    imageSize : ['230x153']
}%}

{#
    MapPopup template.
#}
{% macro MapPopup(settings = {}) %}
    {% set params = Helpers.merge(defaultPopupParams, settings) %}
    {% set uid = Helpers.unique() %}

    <article class="map-popup js-map-popup" data-uid="1">
        {% call Button(
            className = 'btn is-small is-only-icon is-secondary map-popup__close js-map-popup-hide',
            tooltip = 'Fermer cette fiche',
            icon = 'far fa-times'
        ) %}
        <span class="ghost">Fermer cette fiche</span>
        {% endcall %}
        {% if params.mapPopupHeading %}
            <div class="map-popup__top">
                <div class="map-popup__heading">
                    <span class="map-popup__heading-icon" aria-hidden="true"></span>
                    <div class="map-popup__heading-content">
                        <span class="map-popup__heading-category">Category</span>
                        <h2 class="map-popup__heading-title">Item title</h2>
                    </div>
                </div>
            </div>
        {% endif %}
        <div class="map-popup__content">
            <div class="map-popup__content-scroll">
                <div class="map-popup__content-wrap{{ ' link-block-context' if params.useTitleLink }}">
                    {{ Image({
                        className: 'map-popup__image',
                        sizes: params.imageSize,
                        serviceID: 15,
                        alt: ''
                    }) }}
                    {%- if params.mapPopupHeading1 -%}
                        <div class="map-popup__descr">
                            <h3 class="map-popup__title js-title-tag-to-change" id="{{ uid }}">
                                <span class="theme map-popup__category">Category 1, Category 2</span>
                                <span class="sr-only">:</span>
                                {%- if params.useTitleLink -%}
                                    <a href="#" class="underline">{{ lorem(5, 'word') | capitalize }}</a>
                                {%- else -%}
                                    {{ lorem(5, 'word') | capitalize }}
                                {%- endif -%}
                            </h3>
                            {% call InfoBlock(modifier = 'map-popup__info') %}
                            {{ CreateInfoItem(
                                    href = false,
                                    icon = 'far fa-map-marker-alt',
                                    ghost = 'Adresse :',
                                    text = '6 Place du Château, 41000 Blois'
                                ) }}
                            {% endcall %}
                        </div>
                    {%- endif -%}
                </div>

                {%- if params.mapPopupHeading2 -%}
                    <div class="map-popup__data">
                        {% call InfoBlock(modifier = 'map-popup__info') %}
                        {{ InfoItem('phone', className = 'is-primary') }}
                        {{ InfoItem('fax', ghost = '123', label = '', className = 'is-primary', isLink=true) }}
                        {% endcall %}
                        {% call InfoBlock(modifier = 'map-popup__info') %}
                        {{ InfoItem('email', className = 'is-primary') }}
                        {{ InfoItem('website', className = 'is-primary') }}
                        {{ InfoItem('itineraire', className = 'is-primary') }}
                        {% endcall %}
                        {%- if params.useEvents -%}
                            {%- for item in range(3) -%}
                                <article class="map-popup__event">
                                    <div class="map-popup__event-meta">
                                        {{ Date({ className: 'map-popup__event-date' }) }}
                                        <p class="map-popup__event-hours">
                                            <time datetime="22:30">22H30</time>
                                            <span>à</span>
                                            <time datetime="23:30">23H30</time>
                                        </p>
                                    </div>
                                    <div class="map-popup__event-content">
                                        <p class="theme map-popup__event-theme">Theme 1</p>
                                        <h3 class="map-popup__event-title">
                                            <a href="#" class="map-popup__event-link">{{ lorem(1) }}</a>
                                        </h3>
                                    </div>
                                </article>
                            {%- endfor -%}
                        {%- endif -%}

                        {%- if params.schedules -%}
                            <p class="map-popup__item is-schedule">
                                <span class="map-popup__schedules">Horaires :</span>
                                <span>Lundi au vendredi de 9h à 13h et de 14h à 17h.</span>
                            </p>
                        {%- endif -%}

                        {%- if params.useActions -%}
                            <div class="map-popup__actions">
                                {{ Button(
                                    className = 'btn is-primary',
                                    icon = 'far fa-info-circle',
                                    text = 'En savoir plus'
                                ) }}
                            </div>
                        {%- endif -%}
                    </div>
                {%- endif -%}

                {%- if params.mapPopupHeading3 -%}
                    <article class="map-popup__event">
                        <div class="map-popup__event-meta">
                            {{ Date({ className: 'map-popup__event-date' }) }}
                        </div>
                        <div class="map-popup__event-content">
                            <p class="map-popup__event-theme">Theme 1</p>

                            <h3 class="map-popup__event-title">
                                <a href="#" class="map-popup__event-link-travaux">{{ lorem(1) }}</a>
                            </h3>
                            <p class="map-popup__teaser">{{ lorem(2) }}</p>
                            <div class="map-popup__consequence">
                                <p>
                                    <span class="fal fa-location-dot"></span>
                                    <b>Canton :</b>
                                </p>
                                <ul>
                                    <li>
                                        <b>Nom de la voirie :</b> lorem
                                </li>
                                    <li>
                                        <b>Sens concerné :</b> lorem
                                </li>
                                </ul>
                            </div>
                            <div class="map-popup__consequence">
                                <p>
                                    <span class="fal fa-circle-info"></span>
                                    <b>Conséquences :</b>
                                </p>
                                <ul>
                                    <li>Route barrée</li>
                                    <li>Vitesse limitée à 30km/h</li>
                                </ul>
                            </div>
                        </div>
                    </article>
                {%- endif -%}
            </div>
        </div>
    </article>
{% endmacro %}

{#
    MapSearchResults template.
    @prop {string} get - set seeting to get results or toggle.
    @prop {string} toggleText - set text of toggle .
    @prop {bumber} count - count of results.
#}
{% macro MapSearchResults(get = 'results', toggleText = 'Afficher les résultats', count = 10) %}
    {% if get === 'toggle' %}
        {% call Button(
            className = 'map-search-toggle js-map-template-toggle js-disable-expanded',
            icon = false,
            attrs = {
                'aria-controls': 'map-search-column',
                'data-alt-text': 'Cacher les résultats'
            }
            ) %}
        <span class="map-search-toggle__text">{{ toggleText }}</span>
        <span class="map-search-toggle__icon" aria-hidden="true"></span>
        {% endcall %}
    {% elif get === 'results' %}
        <div class="map-search-results">
            <ul class="map-search-results__list">
                {% for item in range(count) %}
                    {% set imagePath = Helpers.path.images + '/map/map-marker-results-' + range(1, 3) | random + '.png' %}
                    <li class="map-search-results__item">
                        <button data-uid="{{ loop.index }}" aria-controls="{{ loop.index }}" type="button" class="map-search-result js-map-popup-show">
                            <span class="map-search-result__wrap">
                                <span class="map-search-result__content">
                                    <span class="map-search-result__title">
                                        <span class="map-search-result__category">{{ lorem(range(2, 5) | random, 'words') }}</span>
                                        <span class="sr-only">:</span>
                                        {{ lorem(1) }}
                                    </span>
                                </span>
                                <span class="map-search-result__image" aria-hidden="true">
                                    {{ svg('map/map-marker-results', 30, 37) }}
                                </span>
                            </span>
                        </button>
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
{% endmacro %}

{#
    Search results bottom block
#}
{%- macro MapSearchResultsBottom() -%}
    <div class="map-search-results-bottom">
        {{ Link(
            className = 'btn is-small',
            text = 'Vue liste',
            icon = 'far fa-list'
        ) }}
    </div>
{%- endmacro -%}

{%- macro LayerSwitcher() -%}
    <div class="layer-switcher js-dropdown">
        <button type="button" class="layer-switcher__toggle js-dropdown-toggle js-tooltip is-tooltip-left" data-content="Fond de carte">
            <span aria-hidden="true" class="far fa-layer-group"></span>
            <span class="sr-only">Fond de carte</span>
        </button>
        <div class="layer-switcher__block js-dropdown-block js-map-layers-select">
            <a href="#" class="layer-switcher__item is-active" data-layer-type="osm">
                Vue carte
            </a>
            <a href="#" class="layer-switcher__item" data-layer-type="aero">
                Vue satellite
            </a>
        </div>
    </div>
{%- endmacro -%}

{%- macro MapHint(isLocaliser = false) -%}
    <div class="map-hint js-dropdown">
        <button type="button" class="map-hint__toggle js-dropdown-toggle js-tooltip {% if isLocaliser %} is-tooltip-right {% else %} is-tooltip-left {% endif %}" data-content="Aide à la navigation au clavier">
            <span aria-hidden="true" class="fa-solid fa-question"></span>
            <span class="sr-only">Aide à la navigation au clavier</span>
        </button>
        <div class="map-hint__block js-dropdown-block">
            <p class="map-hint__text">Lorsque la carte est sélectionnée au clavier, vous pouvez utiliser les touches + et &minus; du clavier pour effectuer un zoom avant ou arrière, ainsi que les touches haut, bas, droite et gauche du clavier pour déplacer la carte.</p>
        </div>
    </div>
{%- endmacro -%}

{%- macro UserPosition() -%}
    <button class="user-position-button js-user-geoposition js-tooltip is-tooltip-left" type="button" data-content="Me géolocaliser">
        <span aria-hidden="true" class="far fa-location"></span>
        {{ Ripple() }}
        <span class="sr-only">Me géolocaliser</span>
    </button>
{%- endmacro -%}

{%- macro ZoomControll(isLocaliser = false) -%}
    <div class="ol-zoom ol-unselectable ol-control" style="pointer-events: auto;">
        <button class="ol-zoom-in js-tooltip {% if isLocaliser %} is-tooltip-right {% else %} is-tooltip-left {% endif %}" data-content="Zoom +" type="button">
            <span class="sr-only">Zoom</span>
            +
        </button>
        <button class="ol-zoom-out js-tooltip {% if isLocaliser %} is-tooltip-right {% else %} is-tooltip-left {% endif %}" data-content="Zoom &minus;" type="button">
            <span class="sr-only">Zoom</span>
            &minus;
        </button>
    </div>
{%- endmacro -%}
