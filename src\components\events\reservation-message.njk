{%- from 'views/utils/utils.njk' import svg -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}


{% set pageConfig = {
    hasPreloader: false
} %}

{% macro ReservationMessageCreate(
    danger = false,
    iconName,
    title,
    subTitle,
    smallDescription = '',
    btnText
    ) %}
    <div class="reservation-message">
        <div class="reservation-message__wrapper">
            {% if title %}
                <h2 class="reservation-message__title js-negative-focus">{{ title }}</h2>
            {% endif %}
            {% if iconName %}
                <div class="reservation-message__icon{{ ' is-red' if danger }}" aria-hidden="true">
                    {{ svg( 'account/' + iconName, 80, 75 ) }}
                </div>
            {% endif %}
            {% if subTitle %}
                <p class="reservation-message__subtitle">{{ subTitle | safe }}</p>
            {% endif %}
            {% if smallDescription %}
                <p class="reservation-message__small-description">{{ smallDescription | safe }}</p>
            {% endif %}

            {{ caller() if caller }}
        </div>
    </div>
{% endmacro %}

{#
    Inscription
#}
{% macro Inscription() %}
    {% call ReservationMessageCreate(
        iconName = false,
        title = 'Titre de l\'événement - Réservation',
        smallDescription = 'Les données personnelles enregistrées ici seront supprimées à l\'expiration de l\'événement et ne serviront à nos équipes qu\'à gérer votre demande de réservation.Vous ne pouvez réserver qu\'une seule fois.'
    ) %}

        <div class="reservation-message__info">
            <p><strong>Date de réservation:</strong> 13 mai 2023 - 14h</p>
            <p><strong>Le maximal de places réservables par une personne:</strong> 4</p>
            <p><strong>Lieu:</strong> {{ lorem(1) }}</p>
        </div>
        {%- call Form.FormWrapper(
            legend = false,
            className = 'popup-reservation__form js-validator-form'
            ) -%}
            <fieldset class="form__fieldset">
                {% call Form.FormGroup() %}
                    <div class="col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Nom',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'family-name'
                            }
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Prénom',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'given-name'
                            }
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        {%- call Form.FormField(
                            type = 'email',
                            label = 'Courriel',
                            required = true,
                            customAttrs = {
                            'autocomplete' : 'email',
                            'aria-describedby': 'input-help-1'
                        }
                            ) -%}
                            <p class="text-help" id="input-help-1">Saisir une adresse électronique valide (ex. : <EMAIL>)</p>
                        {%- endcall -%}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'tel',
                            label = 'Téléphone',
                            customAttrs = {
                            'autocomplete' : 'tel',
                            'aria-describedby': 'input-help-3'
                        }
                            ) %}
                            <p class="text-help" id="input-help-3">Saisir les 10 chiffres de votre numéro de téléphone (ex. : 06 39 98 12 34)</p>
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'text',
                            label = 'Nombre de places souhaitées',
                            required = true
                            ) %}
                        {% endcall %}
                    </div>
                    
                    <div class="col-xs-12">
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    </div>
                    <div class="col-lg-6 col-xs-12 self-center-xs">###CAPTCHA###</div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center is-margin-top') %}
                {{ Button(
                    className = 'btn is-ternary',
                    type = 'submit',
                    icon = false,
                    text = 'Recevoir une alerte !'
                ) }}
            {% endcall %}
        {%- endcall -%}
    {% endcall %}
{% endmacro %}

{#
    Votre inscription a bien été Validé !
#}
{% macro InscriptionHasBeenValidated() %}
    {% call ReservationMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Votre inscription pour l’alerte en cas de places disponibles a bien été validée !',
        subTitle = 'Un courriel vient de vous être envoyé sur l\'adresse électronique fournie dans le formulaire d\'inscription.'
    ) %}
        <div class="account-message__description">
            <p>Merci de l'intérêt que vous portez à notre site !</p>
        </div>
    {% endcall %}
{% endmacro %}

{#
    Vous avez déjà inscrit à l'alerte pour cet événement  !
#}
{% macro InscriptionHasBeenCreated() %}
    {% call ReservationMessageCreate(
        iconName = 'Forbidden',
        title = 'Titre de l\'événement - Recevez une alerte en cas de places indisponibles',
        subTitle = 'Vous avez déjà inscrit à l\'alerte pour cet événement !'
    ) %}
    {% endcall %}
{% endmacro %}


{#
    PopupConfirmCanceling template.
#}
{% macro PopupConfirmCanceling() %}
    {% call ReservationMessageCreate(
        danger = true,
        iconName = 'popup-icon-5',
        title = 'Confirmer l\'annulation',
        smallDescription = 'Vous êtes sur le point de vous désinscrire de l’alerte.
    Vous ne recevrez plus de notification pour vous signaler que des places sont disponibles pour l\'événement.'
    ) %}
        <div class="buttons-group is-center">
            {{ Button(
                className = 'btn is-bordered account-message__remove',
                icon = false,
                text = 'Confirmer',
                attrs = {
                    'data-src': '#popup-delete-confirm',
                    'data-fancybox': 'is-reservation-message',
                    'data-small-btn': 'false',
                    'data-toolbar': 'false',
                    'data-fancybox-body-class': 'is-popup-opened',
                    'data-dialog-label': 'Annulation de la réservation',
                    'aria-haspopup': 'dialog',
                    'aria-label': 'Annulation de la réservation - fenêtre modale'
                }
            ) }}
            {{ Button(
                className = 'btn is-bordered',
                icon = false,
                text = 'Annuler',
                attrs = {
                    'data-fancybox-close': '' | safe
                }
            ) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
    ReservationHasBeenCanceled template.
#}
{% macro InscriptionHasBeenCanceled() %}
    {% call ReservationMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Vous êtes bien désinscrit des alertes en cas de places disponibles !',
        subTitle = '« Un courriel vient de vous être envoyé sur l\'adresse électronique fournie dans le formulaire d\'inscription.'
        ) %}
        <div class="account-message__description">
            <p>Merci de l'intérêt que vous portez à notre site !</p>
        </div>
    {% endcall %}
{% endmacro %}


{#
    Réservation
#}
{% macro Reservation() %}
    {% call ReservationMessageCreate(
        iconName = false,
        title = 'Titre de l\'événement - Réservation',
        smallDescription = 'Les données personnelles enregistrées ici seront supprimées à l\'expiration de l\'événement et ne serviront à nos équipes qu\'à gérer votre demande de réservation.Vous ne pouvez réserver qu\'une seule fois.'
    ) %}

        <div class="reservation-message__info">
            <p><strong>Date de réservation:</strong> 13 mai 2023 - 14h</p>
            <p><strong>Le maximal de places réservables par une personne:</strong> 4</p>
            <p><strong>Lieu:</strong> {{ lorem(1) }}</p>
        </div>
        {%- call Form.FormWrapper(
            legend = false,
            className = 'popup-reservation__form js-validator-form'
            ) -%}
            <fieldset class="form__fieldset">
                {% call Form.FormGroup() %}
                    <div class="col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Nom',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'family-name'
                            }
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Prénom',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'given-name'
                            }
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        {%- call Form.FormField(
                            type = 'email',
                            label = 'Courriel',
                            required = true,
                            customAttrs = {
                            'autocomplete' : 'email',
                            'aria-describedby': 'input-help-1'
                        }
                            ) -%}
                            <p class="text-help" id="input-help-1">Saisir une adresse électronique valide (ex. : <EMAIL>)</p>
                        {%- endcall -%}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'tel',
                            label = 'Téléphone',
                            customAttrs = {
                            'autocomplete' : 'tel',
                            'aria-describedby': 'input-help-3'
                        }
                            ) %}
                            <p class="text-help" id="input-help-3">Saisir les 10 chiffres de votre numéro de téléphone (ex. : 06 39 98 12 34)</p>
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'text',
                            label = 'Nombre de places souhaitées',
                            required = true
                            ) %}
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'text',
                            label = 'Nombre de places enfants souhaitées',
                            required = true
                            ) %}
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {% call Form.FormField(
                            type = 'text',
                            label = 'Nombre de places PMR souhaitées',
                            required = true
                            ) %}
                        {% endcall %}
                    </div>
                    <div class="col-xs-12">
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    </div>
                    <div class="col-lg-6 col-xs-12 self-center-xs">###CAPTCHA###</div>
                {% endcall %}
            </fieldset>
            {% call Form.FormActions(className = 'is-center is-margin-top') %}
                {{ Button(
                    className = 'btn is-ternary',
                    type = 'submit',
                    icon = false,
                    text = 'Je réserve !'
                ) }}
            {% endcall %}
        {%- endcall -%}
    {% endcall %}
{% endmacro %}

{#
    Votre reservation a bien été Validé !
#}
{% macro ReservationHasBeenValidated() %}
    {% call ReservationMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Votre réservation a bien été validée !',
        subTitle = 'Un courriel vient de vous être envoyé sur l\'adresse électronique fournie dans le formulaire de réservation.'
    ) %}
        <div class="account-message__description">
            <p>Merci de l'intérêt que vous portez à notre site !</p>
        </div>
    {% endcall %}
{% endmacro %}

{#
    Vous avez déjà effectué une réservation pour cet événement  !
#}
{% macro ReservationHasBeenCreated() %}
    {% call ReservationMessageCreate(
        iconName = 'Forbidden',
        title = 'Titre de l\'événement - Réservation',
        subTitle = 'Vous avez <b> déjà effectué une réservation</b> pour cet événement !'
    ) %}
    {% endcall %}
{% endmacro %}


{#
PopupConfirmCanceling template.
#}
{% macro PopupConfirmCanceling() %}
    {% call ReservationMessageCreate(
        danger = true,
        iconName = 'popup-icon-5',
        title = 'Confirmer l\'annulation',
        smallDescription = 'Vous êtes sur le point d\'annuler votre réservation.'
    ) %}
        <div class="buttons-group is-center">
            {{ Button(
                className = 'btn is-bordered account-message__remove',
                icon = false,
                text = 'Confirmer',
                attrs = {
                    'data-src': '#popup-delete-confirm',
                    'data-fancybox': 'is-reservation-message',
                    'data-small-btn': 'false',
                    'data-toolbar': 'false',
                    'data-fancybox-body-class': 'is-popup-opened',
                    'data-dialog-label': 'Annulation de la réservation',
                    'aria-haspopup': 'dialog',
                    'aria-label': 'Annulation de la réservation - fenêtre modale'
                }
            ) }}
            {{ Button(
                className = 'btn is-bordered',
                icon = false,
                text = 'Annuler',
                attrs = {
                    'data-fancybox-close': '' | safe
                }
            ) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
ReservationHasBeenCanceled template.
#}
{% macro ReservationHasBeenCanceled() %}
    {% call ReservationMessageCreate(
        iconName = 'popup-icon-1',
        title = 'Votre réservation a bien été annulée !',
        subTitle = 'Un courriel vient de vous être envoyé sur l\'adresse électronique fournie dans le formulaire de réservation.'
        ) %}
        <div class="account-message__description">
            <p>Merci de l'intérêt que vous portez à notre site !</p>
        </div>
    {% endcall %}
{% endmacro %}
