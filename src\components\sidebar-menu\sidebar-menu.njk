{%- from 'views/core-components/click-and-roll.njk' import ClickAndRoll -%}

{% macro SidebarMenu(desktop = true) %}
    <div class="sidebar-menu {% if desktop %}desktop stickyDiv{% else %}mobile stickyDivSmartphone{% endif %}">
        {% call ClickAndRoll({
            toggle: {
                wrapperTag: 'div',
                text: 'Sommaire'
            }
        }) %}
            <div class="sidebar-menu__content">
                <nav role="navigation" aria-label="Sommaire de la page">
                    <ul class="sidebar-menu__list"></ul>
                </nav>
            </div>
        {%- endcall -%}
    </div>
    <div class="startPosition"></div>
{% endmacro %}