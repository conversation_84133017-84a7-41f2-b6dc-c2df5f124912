.main-nav-toggle {
    $this: &;

    @include size(102px, 60px);
    @include trs;
    align-content: center;
    align-items: center;
    background-color: transparent;
    border: 1px solid $color-white;
    border-radius: 40px;
    color: $color-white;
    cursor: pointer;
    display: flex;
    justify-content: center;
    padding: 20px;
    position: relative;

    @include breakpoint(medium down) {
        @include size(82px, 48px);
        padding: 0;
    }

    @include breakpoint(small down) {
        @include size(45px);
    }

    @include on-event {
        background-color: var(--color-2--2);
    }

    &::before {
        @include absolute(50%, null, null, 50%);
        @include size(65px, 51px);
        background-image: image('ico-menu.svg');
        background-repeat: no-repeat;
        background-size: 100% 53px;
        content: '';
        transform: translate(-50%, -50%);
    }

    &__bars {
        position: relative;
    }

    &__text {
        @include trs;
        color: inherit;
        font-size: 0;
        font-weight: var(--fw-normal);
        position: absolute;
    }

    &.is-open {
        @include breakpoint(large only) {
            #{$this}__bar {
                left: 0;

                &,
                &:first-child,
                &:last-child {
                    opacity: 0;
                    top: 50%;
                }

                &:first-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(45deg);
                }

                &:last-child {
                    opacity: 1;
                    transform: translateY(-50%) rotate(-45deg);
                }
            }

            #{$this}__text {
                opacity: 0;
            }
        }
    }
}
