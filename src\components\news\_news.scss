// News section global
.news-content,
.news-widget {
    .news-list {
        &__item {
            @include breakpoint(large) {
                margin-bottom: 30px;
                padding-left: 0;
            }
        }
    }
}

.is-width-33 {
    &.news-widget {
        .section {
            &__more-links {
                justify-content: flex-start;
                margin-left: auto;
                margin-right: auto;
                width: fit-content;
    
                @include breakpoint(medium down) {
                    justify-content: center;
                    margin-left: auto;
                    margin-right: auto;
                }
    
                .btn {
                    &.is-link {
                        background-color: var(--color-1--1);
                        border: 1px solid var(--color-1--1);
                        border-radius: 40px;
                        color: $color-white;
                        padding: 24px 45px;
                    }
                }
            }
        }

        .news-list {
            &__item {
                @include breakpoint(large) {
                    margin-bottom: 0;
                }
            }
        }
    }
}

.news-content {
    .section {
        &__more-links {
            justify-content: flex-start;
            margin-left: auto;
            margin-right: auto;
            width: fit-content;

            @include breakpoint(medium down) {
                justify-content: center;
                margin-left: auto;
                margin-right: auto;
            }

            .btn {
                &.is-link {
                    background-color: var(--color-1--1);
                    border: 1px solid var(--color-1--1);
                    border-radius: 40px;
                    color: $color-white;
                    padding: 24px 45px;
                }
            }
        }
    }
}

.news-home {
    &.section {
        @include breakpoint(large only) {
            margin: 60px 0 80px 0;
        }

        @include breakpoint(small down) {
            margin: 45px 0 60px;
        }
    }

    .section {
        &__title {
            align-items: flex-start;
            flex-direction: column;
            margin-bottom: 0;

            @include breakpoint(medium down) {
                margin-left: 41px;
            }

            @include breakpoint(small down) {
                align-items: center;
                margin-left: 0;
            }
        }
    }

    &__container {
        @extend %container;
        position: relative;

        @include breakpoint(medium only) {
            padding: 0 62px;
        }
    }

    &__content {
        @include breakpoint(medium only) {
            padding: 0;
        }
    }

    &__more-links {
        .btn {
            font-size: 1.3rem;
        }
        @include breakpoint(medium down) {
            margin-top: 55px;
        }

        @include breakpoint(small down) {
            margin-top: 35px;

            .news-home & {
                margin-top: 13px;
            }
        }
    }
}
