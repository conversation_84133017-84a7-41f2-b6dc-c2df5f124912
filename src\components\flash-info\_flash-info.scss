// Flash info layout styles
.flash-info {
    background-color: var(--color-1--1);
    display: none;
    padding: 34px 0 110px;
    position: relative;

    @include breakpoint(medium down) {
        background-color: $color-3--6;
        padding: 34px 0 67px;
    }

    @include breakpoint(small down) {
        padding: 30px 25px 45px;
    }

    &:not(&.is-popup) {
        .flash-info-container {
            width: 100%;
        }

        &::before {
            @include absolute(null, null, 0, -30px);
            @include size(calc(100% + 10px), 100%);
            background-color: $color-3--6;
            border-radius: 0 0 20px 0;
            content: "";
            transform: rotate(-1deg);
            transform-origin: 0 0;

            @include media-min(1400) {
                width: calc(100% - 37px);
            }

            @include media-min(1800) {
                max-width: 90vw;
            }

            @include breakpoint(medium down) {
                content: none;
            }
        }
    }

    &.is-hidden {
        display: none;
    }

    &.is-visible {
        display: block;
    }

    &__wrapper {
        @extend %container;
        align-items: center;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        padding-left: 38px;
        padding-right: 30px;
        width: 100%;

        @include breakpoint(medium down) {
            padding: 0 100px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__items {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        row-gap: 30px;

        @include breakpoint(medium down) {
            width: 100%;
        }

        @include breakpoint(small down) {
            min-width: 280px;
            width: 100%;
        }
    }
}
