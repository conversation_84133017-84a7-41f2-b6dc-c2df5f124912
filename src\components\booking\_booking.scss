.booking {
    &__content {
        max-height: 0;
        opacity: 0;
        overflow: hidden;
        transform: translateY(-10px);
        transition-duration: 350ms;
        transition-property: max-height, visibility, opacity, transform;
        transition-timing-function: ease-in-out;
        visibility: hidden;
        will-change: transform;

        > *:first-child {
            margin-top: 30px;
        }

        &.is-visible {
            max-height: 1000px;
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }
    }

    &__info {
        margin: 30px 0;
    }

    &__wrapper {
        display: flex;
        margin: 30px -10px 0;
        overflow: auto;

        &:empty {
            display: none;
        }
    }

    &__list {
        box-sizing: border-box;
        display: block;
        margin: 10px 0;
        padding: 0 10px;
        width: 100%;
    }

    &__list-wrap {
        max-height: 300px;
        overflow: auto;
    }

    &__period {
        --bk-period-color: #{$color-black};
        --bk-period-bg: #{$color-3--1};

        @include trs(background-color);
        background-color: var(--bk-period-bg);
        border: 0;
        color: var(--bk-period-color);
        cursor: pointer;
        display: block;
        font-size: 1.4rem;
        margin: 1px 0;
        padding: 8px 12px;
        pointer-events: auto;
        width: 100%;

        &:hover {
            --bk-period-bg: #{$color-3--2};
            outline-offset: -2px;

            ~ .booking__period:not(.js-disabled):not(.is-active):not(.is-disabled) {
                --bk-period-bg: #{$color-3--1};
            }
        }

        &:focus {
            box-shadow: none;
            outline-offset: -2px;
        }

        &.is-disabled {
            --bk-period-color: #{$color-3--2};
            --bk-period-bg: #{$color-3--1};
            cursor: not-allowed;
        }

        &.is-active {
            background-color: #a2d9ef;
            font-weight: var(--fw-bold);
        }
    }

    &__day {
        text-align: center;
    }

    .js-disabled {
        background-color: $color-3--1;
        color: $color-3--2;
        cursor: not-allowed;
    }
}

.booking-carousel {
    $this: &;
    border: 1px dashed $color-3--3;
    margin: 0;
    overflow: hidden;
    padding: 30px 80px;
    position: relative;

    @include breakpoint(medium down) {
        padding: 30px 40px;
    }

    &__wrapper.is-active {
        &:hover {
            .swiper-slide .booking__period:not(.js-disabled):not(.is-active):not(.is-disabled) {
                background-color: lighten(#a2d9ef, 15%);
            }

            .booking__day {
                &:hover {
                    + .booking__list-wrap {
                        .booking__period:not(.js-disabled):not(.is-active):not(.is-disabled) {
                            background-color: $color-3--1 !important;
                        }
                    }
                }
            }
        }
    }

    &__carousel {
        margin-left: -10px;
        width: calc(100% + 20px);
    }

    &__control {
        --bc-control-indent: 10px;

        @include absolute(50%);
        @include size(50px);
        align-items: center;
        background-color: transparent;
        border: 0;
        cursor: pointer;
        display: flex;
        font-size: 4rem;
        justify-content: center;
        padding: 0;
        transform: translateY(-50%);
        z-index: 10;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        &.is-prev {
            left: var(--bc-control-indent);

            @include breakpoint(medium down) {
                --bc-control-indent: 1px;
            }
        }

        &.is-next {
            right: var(--bc-control-indent);

            @include breakpoint(medium down) {
                --bc-control-indent: 1px;
            }
        }
    }

    .swiper-slide {
        @include trs;

        &:hover {
            ~ .swiper-slide .booking__period:not(.js-disabled):not(.is-active):not(.is-disabled) {
                background-color: $color-3--1 !important;
            }
        }

        &[aria-hidden='true'] {
            opacity: 0;
            visibility: hidden;
        }

        &[aria-hidden='false'] {
            opacity: 1;
            visibility: visible;
        }
    }
}

.booking-form {
    .radio-checkbox {
        &__label {
            display: inline-block;
        }
    }
}
