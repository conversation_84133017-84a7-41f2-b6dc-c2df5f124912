{% from 'views/core-components/dropdown.njk' import Dropdown %}
{%- from 'components/quicklinks/quicklinks.njk' import QuickLinksItem -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'components/lang/lang.njk' import Lang -%}
{%- from 'components/logo/logo.njk' import Logo -%}
{%- from 'components/quicklinks/quicklinks.njk' import OneClickBlock -%}

{% set defaultclick = [
    { icon: 'ico-vieillir', title: 'Bien veillir' },
    { icon: 'ico-offres-emplois', title: 'Offres d\'emploi' },
    { icon: 'ico-archives', title: 'Archives départementales' },
    { icon: 'ico-annuaires', title: 'Annuaires' },
    { icon: 'ico-tremplin-citoyen', title: 'Tremplin citoyen' },
    { icon: 'ico-environnement', title: 'Environnement' },
    { icon: 'ico-grand-projets', title: 'Grands projets' },
    { icon: 'ico-patrimoine', title: 'Patrimoine' },
    { icon: 'ico-jeunesse', title: '<PERSON><PERSON><PERSON>' },
    { icon: 'ico-handicap', title: 'Handicap' },
    { icon: 'ico-randonnees', title: 'Randonnées' },
    { icon: 'ico-parcs', title: 'Parcs naturels' }
] %}

{#
click template.
@params {object[]} click - click array.
#}
{%- macro Click(click = defaultclick ) -%}
    {% call Dropdown({
        inheritClass: false,
        wrapper: {
            className: 'click'
        },
        toggle: {
            className: 'click__toggle',
            text: 'En 1 Clic',
            textClassName: 'click__toggle-text',
            icon: {
                className: 'click__toggle-icon'
            }
        },
        dropdown: {
            className: 'click__block'
        }
    }) %}
        <div class="click__top-components">
            <div class="click__back-wrap js-one-click-return-wrap">
                
            </div>
            <div class="click__close-wrap">
                {%- call Button(
                    className = 'btn is-small is-only-icon main-nav__close-button js-main-nav-close js-dropdown-toggle js-one-click-close',
                    tooltip = 'Fermer',
                    icon = 'fal fa-times'
                    ) -%}
                    <span class="ghost">Fermer</span>
                {%- endcall -%}
            </div>
        </div>
        {{ OneClickBlock() }}

        {# <ul class="list click__list">
            {%- for click in click -%}
                <li class="click__item">
                    {{ QuickLinksItem(
                        nameIcon = click.icon,
                        title = click.title
                        )
                    }}
                </li>
            {%- endfor -%}
        </ul> #}
    {% endcall %}
{%- endmacro -%}
