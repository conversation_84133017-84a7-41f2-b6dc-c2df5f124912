.overlay-label {
    $this: &;

    position: relative;

    &::after {
        @include triangle("bottom", $color-white, 15px, 10px);
        @include absolute(100%, null, null, 50%);
        content: "";
        transform: translateX(-50%);
    }

    &__content {
        background-color: $color-white;
        margin-bottom: 10px;
        max-height: 95px;
        overflow-y: auto;
        padding: 10px 15px;
    }

    //&:not(.is-marker-cluster) {
    //    pointer-events: none;
    //}

    &.is-marker {
        #{$this}__content {
            margin-bottom: 35px;
        }
    }

    &.is-marker-cluster {
        #{$this}__content {
            margin-bottom: 15px;
        }
    }
}

.map-feature-message {
    @include font(var(--typo-1), 1.3rem, var(--fw-light));
    line-height: 1.49;

    &__button {
        background: transparent;
        border: 0;
        white-space: nowrap;

        @include on-event {
            cursor: pointer;
            text-decoration: underline;
        }
    }
}

.map {
    @include size(100%, 600px);
    display: flex;
    overflow: hidden;
    position: relative;

    @include media-max(440) {
        flex-direction: column;
        height: 675px;
    }

    .map-page & {
        @include size(100%);

        @include media-max(767, landscape) {
            flex-direction: row;
        }
    }

    &__container {
        @include size(1%, 100%);
        flex-grow: 1;
        position: relative;

        @include breakpoint(small down) {
            width: 100%;
        }

        .map-page & {
            @include media-max(767, landscape) {
                margin: 0;
                width: 60%;
            }
        }
    }

    &__element {
        @include size(100%);
        @include focus-outline();
    }

    &__tools {
        @include absolute(10px !important, null, null, 5px !important);
        align-items: flex-start;
        display: flex;
        z-index: 10;

        @include breakpoint(medium down) {
            right: 10px;
        }

        @include breakpoint(small down) {
            bottom: 10px;
            top: unset;
        }

        .map-localiser &,
        .map-detail & {
            @include absolute(15px, auto, auto, 15px);
        }

        .map-page & {
            @include absolute(10px !important, 5px !important, null, auto !important);
        }
    }

    &__popup {
        @include size(337px, auto);
        @include absolute(50%, 0, auto, null);
        max-height: calc(100% - 130px);
        transform: translateY(-50%);

        .map-travaux & {
            width: 278px;
        }

        @include breakpoint(medium down) {
            right: 33px;
            z-index: 8;
        }

        @include breakpoint(small down) {
            max-height: 100%;
            position: static;
            transform: translateY(0);
            width: 100%;
        }

        &.is-hidden {
            display: none;
        }

        .map-page & {
            overflow-y: auto;

            @include breakpoint(medium down) {
                bottom: 20px;
                right: 60px;
                top: auto;
                transform: translateY(0);
            }

            @include breakpoint(small down) {
                right: 0;
            }
        }

        .map-localiser &,
        .map-detail & {
            background-color: var(--color-1--1);
            bottom: 0;
            max-height: 100%;
            right: 0;
            top: 0;
            transform: none;

            @include breakpoint(medium down) {
                max-height: 100%;
                position: static;
                transform: translate(0);
            }
        }
    }

    .ol-scale-line {
        background: rgba($ol-blue-color, 0.75);
    }

    .ol-attribution {
        a {
            color: rgba($ol-blue-color, 0.85);
        }
    }
}
