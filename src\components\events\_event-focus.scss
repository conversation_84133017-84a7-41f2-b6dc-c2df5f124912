//genial decor for home style
@mixin home-styled-decor {
    &::before {
        @include absolute(0, null, null, 50%);
        @include size(1000vw, calc(100% - 111px));
        background-color: $color-white;
        content: "";
        transform: translateX(-500vw);
        z-index: -1;

        @include breakpoint(medium down) {
            background-color: var(--color-1--2);
            height: 100%;
        }

        @include breakpoint(small down) {
            height: calc(100% + 20px);
            top: -20px;
        }
    }
}

.event-focus {
    $this: &;
    margin-left: 11rem;
    padding-bottom: 0;
    position: relative;

    @include breakpoint(medium down) {
        margin-left: 0;
        margin-top: 110px;
        padding-bottom: 20px;
    }

    &__content-wrapper {
        align-items: center;
        display: flex;
        z-index: 1;

        .event-badge {
            @include size(90px);
            left: -49px;
            margin-top: 0;
            position: relative;

            @include breakpoint(medium down) {
                @include size(57px);
                bottom: 28px;
                left: -15px;
            }

            @include breakpoint(small down) {
                @include size(57px);
                bottom: 0;
                left: 37px;
                top: -54px;
            }
        }

        @include breakpoint(medium down) {
            .event-focus__text {
                margin-left: 0;
            }
        }

        @include breakpoint(small down) {
            .event-focus__text {
                margin-left: 63px;
            }
        }
    }

    // styles for HP with background
    .events-home & {
        &::before {
            @include absolute(null, null, 0, 50%);
            @include size(100vw, 100%);
            background-color: var(--color-1--2);
            content: "";
            transform: translateX(-50%);

            @include breakpoint(medium down) {
                height: 20%;
            }
        }

        #{$this} {
            &__text {
                @include home-styled-decor();
                @include breakpoint(medium down) {
                    background-color: var(--color-1--2);
                }
            }

            &__category {
                @include breakpoint(medium down) {
                    color: var(--color-2--1);
                }
            }

            &__title {
                @include breakpoint(medium down) {
                    color: $color-white;
                }

                .underline {
                    @include breakpoint(medium down) {
                        @include multiline-underline($color: $color-white);
                    }
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(medium down) {
                        color: var(--color-1--3);
                    }
                }
            }
        }
    }

    &__wrapper {
        @extend %link-block-context;
        align-items: center;
        display: flex;
        flex-direction: row-reverse;
        position: relative;
        z-index: 1;

        @include breakpoint(medium down) {
            flex-direction: column-reverse;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__picture-link {
        flex-shrink: 0;
        width: auto;
        z-index: -1;

        @include breakpoint(medium down) {
            width: 100%;
        }
    }

    &__picture {
        @include size(432px);
        background-color: transparent !important;
        border-radius: 50%;
        
        @include breakpoint(medium down) {
            border-radius: 0;
            margin: 0 auto;
            -webkit-mask-image: url(../images/Mask-Image-Evts.svg);
            mask-image: url(../images/Mask-Image-Evts.svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
        }

        @include breakpoint(small down) {
            @include size(282px);
            margin: 0 auto;
        }

        img {
            @include object-fit();
            @include size(auto, 100%);
            border-radius: 50%;
            
            @include breakpoint(medium down) {
                @include size(432px);
                border-radius: 0;
                margin: 0 auto;
                -webkit-mask-image: url(../images/Mask-Image-Evts.svg);
                mask-image: url(../images/Mask-Image-Evts.svg);
                -webkit-mask-repeat: no-repeat;
                mask-repeat: no-repeat;
                -webkit-mask-size: contain;
                mask-size: contain;
            }

            @include breakpoint(small down) {
                @include size(282px);
            }
        }
    }

    &__content {
        align-items: center;
        background-color: $color-white;
        flex-direction: row;
        flex-grow: 1;
        height: 432px;
        margin-left: -45px;
        padding-top: 77px;
        position: relative;
        word-break: break-word;

        @include breakpoint(medium down) {
            align-items: flex-start;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            height: auto;
            margin: -108px 0 0;
            max-width: 432px;
            padding: 0;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0;

            .single-event-focus & {
                align-items: center;
                background-color: transparent;
                flex-direction: column;
                margin-top: -77px;
            }
        }
    }

    &__date {
        @include absolute(null, null, null, -50px);

        &::before {
            content: none;
        }

        @include breakpoint(medium down) {
            @include size(116px);
            left: auto;
            margin: -85px 0 0 0;
            top: 0;
        }

        &.is-large {
            @include breakpoint(medium down) {
                @include min-size(auto);
            }

            @include breakpoint(small down) {
                margin-left: auto;
                margin-right: auto;
                margin-top: -80px;
                padding: 0;
            }
        }

        .single-event-focus & {
            border: none;

            .is-day {
                font-size: 2.5rem;
            }
            
            .is-month {
                font-size: 1rem;
            }
        }

        @include breakpoint(small down) {
            margin: -20px 0 0 -20px;
        }
    }

    &__text {
        background-color: $color-white;
        padding: 40px 0 52px 0;

        &::before {
            @include absolute(null, null, null, 59px);
            @include size(1px, 45%);
            background-color: $color-3--3;
            content: "";
            margin-top: 10px;

            @include breakpoint(medium down) {
                .single-event-focus & {
                    height: 15%;
                    left: 15px;
                }
            }

            @include breakpoint(small down) {
                height: 83px;
                left: 106px;

                .single-event-focus & {
                    content: none;
                }
            }
        }

        @include breakpoint(medium down) {
            min-height: 108px;
            padding: 26px 40px 20px 25px;
            width: 384px;
        }

        @include breakpoint(small down) {
            margin-left: 120px;
            margin-top: -40px;
            padding: 18px 0;
            width: auto;

            .single-event-focus & {
                margin: 0 auto;
                margin-top: -14px;
                max-width: 320px;
                padding-left: 0;
                padding-top: 0;
                text-align: center;
            }
        }

        .single-event-page & {
            padding: 40px 0 52px 82px;

            @include breakpoint(medium down) {
                padding: 40px 0 52px 0;
                text-align: center;

                &::before {
                    content: none;
                }
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__title {
        &.item-title {
            font-size: 3.5rem;

            @include breakpoint(medium down) {
                font-size: 2.8rem;
            }
        }
    }

    &__category {
        @include font(null, 1.4rem, var(--fw-bold));
        color: var(--color-1--1);
        letter-spacing: 2.52px;
        line-height: 17px;
        margin-top: 8px;
        text-transform: uppercase;
        z-index: 2;
    }

    &__type {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-top: 14px;
        z-index: 2;

        @include breakpoint(medium down) {
            font-size: 1.2rem;
        }

        @include breakpoint(small down) {
            margin-top: 8px;
        }
    }

    &__nbplaces {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-top: 8px;
        text-transform: uppercase;
        z-index: 2;

        @include breakpoint(medium down) {
            @include absolute(-20px, 0, null, 0);
            background-color: var(--color-1--1);
            color: #fff;
            margin: 0 auto;
            padding: 10px 20px;
            text-align: center;
            width: max-content;
        }

        @include breakpoint(small down) {
            font-size: 1rem;
            font-weight: var(--fw-bold);
            top: -66px;
        }
    }

    &__time-place {
        margin-top: 7px;

        .time-place__item {
            margin-right: 16px;

            .far {
                color: var(--color-1--1);
            }

            &.is-time {
                color: $color-black;
                flex-shrink: 0;

                @include breakpoint(medium down) {
                    font-size: 1.2rem;
                }
            }

            &.is-place {
                color: $color-black;

                @include breakpoint(medium down) {
                    font-size: 1.2rem;
                }
            }

            @include breakpoint(large only) {
                font-size: 1.2rem;
                font-weight: var(--fw-normal);
            }
        }
    }

    &__actions {
        @include absolute(35px, 45px);
        z-index: 11;
    }

    &__teaser {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: $color-black;
        margin-top: 14px;

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }
    }
}

.events-focus-content {
    .single-event-page & {
        margin-bottom: 104px;

        @include breakpoint(medium down) {
            margin-bottom: 20px;
        }

        .section__title {
            margin-bottom: 51px;

            @include breakpoint(small down) {
                justify-content: center;
            }
        }
    }
}
