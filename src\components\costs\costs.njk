{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    CostsItem template.
#}
{%- macro CostsItem() -%}
    <div class="cost-item">
        <p class="cost-item__description">{{ lorem(5) }}</p>
        <div class="cost-item__chart js-bubble-chart">
            <div class="cost-item__canvas">
                <canvas aria-hidden="true"></canvas>
            </div>
            <div class="cost-item__legend">
                <h3 class="cost-item__title">Total <strong>16.2 €</strong> Dépensé</h3>
                <ul data-chart-data data-chart-hide-quantity class="cost-item__list">
                    {% for value, text in [
                        ['13', 'Dépensé'],
                        ['2.2', 'Dépensé'],
                        ['1 ', 'Dépensé']
                    ] %}
                        <li class="cost-item__element js-chart-item js-chart-label" data-index="{{ loop.index }}" data-value="{{ value }}">
                            <span class="cost-item__colorbox js-chart-data-colorbox"></span>
                            <span class="cost-item__amount">{{ value }} €</span>
                            {{ text }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    CostsContent template.
    @param {string} titleText - section title text.
#}
{%- macro CostsContent(
    titleText = 'Bon à savoir'
) -%}
    {% call Section(className = 'costs', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ CostsItem() }}
        </div>
    {% endcall %}
{%- endmacro -%}

{#
    CostsSG template.
    Stylegudie template.
#}
{%- macro CostsSG() -%}
    {% call SG.Section('costs') %}
        <h2 class="styleguide-section__title">Bon à savoir</h2>
        {%- call SG.Preview() -%}
            {{ CostsItem() }}
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

