{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/utils/constants.njk' import kMainNav -%}
{%- from 'components/main-nav-toggle/main-nav-toggle.njk' import MainNavToggle -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'components/lang/lang.njk' import Lang -%}

{%- macro NavItem(item = {}, level = 0) -%}
    <li class="main-nav__nav-item {{ 'has-dropdown js-main-nav-ddm' if item.dropdown }}">
        <span class="main-nav__nav-item-actions">
            {{ Icon('fas fa-long-arrow-right') if level === 2 }}
            <a href="#" class="main-nav__nav-link {{ 'is-active' if item.current }}">
                <span class="main-nav__nav-link-text">{{ item.text }}</span>
            </a>
            {% if item.dropdown %}
                <button type="button" class="main-nav__nav-toggle js-main-nav-ddm-toggle">
                    <span class="main-nav__nav-toggle-icon" aria-hidden="true"></span>
                    <span class="main-nav__nav-toggle-text">{{ item.text }}</span>
                </button>
            {% endif %}
            {% set level = level + 1 %}
        </span>
        {% if item.dropdown %}
            <div class="main-nav__nav-dropdown is-level-{{ level }}  js-main-nav-ddm-block">
                {% if level === 1 %}
                    <p class="main-nav__nav-item-parent">
                        <a href="#" class="main-nav__nav-link {{ 'is-active' if item.current }}">
                            {{ Icon('fas fa-long-arrow-right') }}
                            Accéder au portail "{{ item.text }}"
                        </a>
                        <button
                            type="button"
                            class="main-nav__close-submenu js-tooltip"
                            data-content="Fermer {{ item.text }}"
                        >
                            {{ Icon('far fa-times') }}
                            <span class="ghost">Fermer {{ item.text }}</span>
                        </button>
                    </p>
                {% endif %}
                <ul class="main-nav__nav-dropdown-list" tabindex="-1">
                    {% for subItem in item.dropdown %}
                        {{ NavItem(item = subItem, level = level) }}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    </li>
{%- endmacro -%}

{%- macro MainNavDefault(usePageImage) -%}
    <nav class="main-nav js-main-nav" role="navigation" aria-label="Navigation principale">
        <div class="main-nav__container js-main-nav-container">
            <div class="main-nav__block js-main-nav-block">
                <div class="main-nav__top-components">
                    <div class="main-nav__close-wrap">
                        {%- call Button(
                            className = 'btn is-small is-only-icon main-nav__close-button js-main-nav-close',
                            tooltip = 'Fermer le menu',
                            icon = 'far fa-times'
                            ) -%}
                            <span class="ghost">Fermer</span>
                        {%- endcall -%}
                    </div>
                    <div class="main-nav__lang">
                       <!-- GTranslate will be inserted via JS if it's activated in header.njk and core.ts -->
                        {{ Lang() if not usePageImage}}
                    </div>
                </div>
                <div class="main-nav__nav">
                    <ul class="main-nav__nav-list">
                        {% for item in kMainNav %}
                            {{ NavItem(item = item) }}
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    <div class="main-nav-overlay" id="js-main-nav-overlay"></div>
{%- endmacro -%}
