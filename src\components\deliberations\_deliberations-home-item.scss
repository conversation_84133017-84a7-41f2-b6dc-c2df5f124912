.deliberations-home-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    padding-bottom: 82px; // Need for section button position

    @include breakpoint(medium down) {
        padding-bottom: 0;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
        text-align: center;
    }

    &__image {
        @include size(282px, auto);
        display: inline-block;
        flex-shrink: 0;
        margin-bottom: -82px; // Need for section button position
        position: relative;
        transform: rotate(-5deg);

        @include breakpoint(medium down) {
            margin-bottom: 0;
            width: 175px;
        }

        @include breakpoint(small down) {
            width: 160px;
        }

        img {
            box-shadow: 0 0 20px rgba($color-black, 0.16);
        }
    }

    &__content-wrapper {
        flex-grow: 1;
        padding: 53px 0 0 87px;

        @include breakpoint(medium down) {
            padding: 0 0 0 34px;
        }

        @include breakpoint(small down) {
            padding: 32px 0 0;
        }
    }

    &__content {
        flex-grow: 1;
    }

    &__category {
        margin-bottom: 13px;
    }

    &__title {
        max-width: 100%; //For IE
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__subtitle {
        @include font(null, 3.5rem, var(--fw-normal));
        display: block;
        margin: 5px 0 0;

        @include breakpoint(medium down) {
            font-size: 2.5rem;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
        }
    }

    &__teaser {
        margin: 32px 30px 0 0;

        @include breakpoint(medium down) {
            margin: 23px 0 0;
        }
    }

    &__actions {
        align-items: flex-end;
        display: flex;
        flex-shrink: 0;
        margin-top: 25px;
        position: relative;
        z-index: 5;

        @include breakpoint(small down) {
            display: block;
        }

        .document-actions {
            @include breakpoint(small down) {
                align-items: center;
            }
        }
    }

    &__publication[class] {
        margin-top: 25px;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__status {
        margin: 25px 0 0;
    }
}
