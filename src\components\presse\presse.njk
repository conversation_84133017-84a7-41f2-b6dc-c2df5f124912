{%- from 'components/share/share.njk' import Share -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/utils/utils.njk' import setAttr -%}
{%- from 'views/core-components/list.njk' import List -%}
{% from Helpers.constants import kGlobalLinks %}

{#
    MajorProjectsItem template.
    @param {object} settings - user settings object
#}
{% set defaultSettingsItem = {
    title: lorem(1),
    teaser: lorem(2),
    category: lorem(range(1, 3) | random, 'words'),
    imageSizes: ['480x320'],
    tag: 'h3',
    role: false,
    ariaLevel: false,
    date: true
} %}

{% macro PresseItem(settings = {}) %}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}
    <article class="presse-item">
        {{ Image({
            sizes: params.imageSizes,
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(50) | random
        }) }}
        <div class="presse-item__content">
            {% if params.category %}
                <p class="presse-item__category">{{ params.category }}</p>
            {% endif %}
            {% if params.title %}
            <{{ params.tag }} class="item-title presse-item__title"
                {{ setAttr('role', params.role) }}
                {{ setAttr('aria-level', params.ariaLevel) }}>
                <a href="single-presse.html" class="presse-item__title-link">
                    <span class="underline">{{ params.title }}</span>
                </a>
            </{{ params.tag }}>
        {% endif %}
        {% if params.teaser %}
            <p class="item-teaser presse-item__teaser">{{ params.teaser }}</p>
        {% endif %}
        {% if params.date %}
            <div class="presse-item__date">
                <span>Publié le</span>
                <time datetime="2021-11-27">27/11/2021</time>
                <span>- Mise à jour le</span>
                <time datetime="2022-01-13">13/01/2022</time>
            </div>
        {% endif %}
        </div>
    </article>
{% endmacro %}

{#
    PresseList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
#}
{%- macro PresseList(
    itemClass = 'has-mb-6',
    cols = 4,
    mdCols = 2,
    smCols = 1,
    xsCols = 1,
    count = 10
    ) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ PresseItem() }}
    {% endcall %}
{%- endmacro -%}
