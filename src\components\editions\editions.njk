{% from 'views/utils/constants.njk' import kGlobalLinks %}
{% from 'views/core-components/section.njk' import Section %}
{% from 'views/core-components/carousel.njk' import CarouselWrapper %}
{% from 'views/core-components/title.njk' import TitlePrimary %}
{% import 'views/core-components/secondary.njk' as Secondary %}
{% from 'views/core-components/link.njk' import Link %}
{% from 'views/core-components/image.njk' import Image %}
{% from 'views/core-components/icon.njk' import Icon %}

{#
    EditionsItem template.
#}
{% macro EditionItem(
    imageSizes = ['303x427'],
    category = 'Lorem ipsum',
    title = 'Essonne & Vous, le magazine de votre Département',
    subtitle = 'Septembre 2099 - N°999',
    teaser = 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore',
    documents = range(1, 3) | random,
    icons = [
        ['download', 'Télécharger (3,9 Mo)', 'Télécharger Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-arrow-to-bottom', '#'],
        ['read', 'Feuilleter', 'Feuilleter Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-book-open', 'https://google.fr/']
    ]
) %}
    <article class="edition-item">
        {{ Image({
            sizes: imageSizes,
            className: 'edition-item__image',
            type: 'no-image' if range(0,2) | random else 'default',
            serviceID: range(50) | random
        }) }}
        <div class="edition-item__content">
            <h3 class="item-title is-large edition-item__title">
                {%- if category %}
                    <span class="theme is-large edition-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {%- endif %}
                <a href="{{ kGlobalLinks.singlePublications }}" class="edition-item__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
                {%- if subtitle %}
                    <span class="edition-item__subtitle">{{ subtitle }}</span>
                {%- endif -%}
            </h3>
            {%- if teaser %}
                <p class="item-teaser edition-item__teaser">{{ teaser }}</p>
            {%- endif -%}
            {%- if documents === 1 -%}
                <div class="edition-item__actions">
                    {{ Secondary.DocumentActions({icons : icons}) }}
                </div>
            {% else %}
                {%- if publication === 1 -%}
                    <p class="edition-item__publication">
                        <span class="edition-item__number">{{ documents }} documents</span>
                        <span>Publié le </span>
                        <time datetime="2018-11-26">26 novembre 2018</time>
                    </p>
                {%- endif -%}
            {%- endif -%}
        </div>
    </article>
{% endmacro %}

{#
    EditionsCarousel template.
#}
{% macro EditionsCarousel(
    ariaLabelCarousel = 'En kiosque',
    itemsCount = 6,
    icons= [
        ['download', false, 'Télécharger Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-arrow-to-bottom', '#'],
        ['read', false, 'Feuilleter Nom du doc n°998 - septembre 2099 lorem ipsum dolor sit amet', 'fas fa-book-open', 'https://google.fr/']
    ]
) %}
    {% call CarouselWrapper({
        effect: "coverflow",
        grabCursor: true,
        centeredSlides: false,
        slidesPerView: 1,
        wrapperClassName: 'editions-block',
        wrapperModifier: 'no-animate',
        jsClassName: 'js-edition-swiper-coverflow',
        perspective: false,
        wrapperAttrs: {
            'aria-label': ariaLabelCarousel
        },
        loop: false,
        itemsToShow: [3, 1, 1],
        actions: false,
        pagination: 'outside',
        arrows: {
            next: {
                icon: 'far fa-chevron-right'
            },
            prev: {
                icon: 'far fa-chevron-left'
            }
        }
    }) %}
        {% for item in range(itemsCount) %}
            <div class="editions__item swiper-item" aria-label="Lorem ipsum Magazine de la ville Septembre 2099 - N°999" role="group">
                {{ EditionItem(documents = 1, icons = icons) }}
            </div>
        {% endfor %}
        {% endcall %}
        <div class="carousel-pagination">
            {% for i in range(itemsCount) %}
                <span class="carousel-bullet {% if i == 0 %}active{% endif %}"></span>
            {% endfor %}
        </div>
{% endmacro %}

{#
    EditionsHome template.
    Template for events on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{% macro EditionsHome(
    titleText = 'En kiosque',
    moreButton = true
) %}
    {% call Section(className = 'editions', container = 'editions__container') %}
        <div class="section__title">
            {{ TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ EditionsCarousel(
                ariaLabelCarousel = titleText
            ) }}
        </div>
        {% if moreButton %}
            <div class="section__more-links">
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listPublications,
                        text = 'Toutes les publications',
                        className = 'btn is-sm-small is-primary',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{% endmacro %}
