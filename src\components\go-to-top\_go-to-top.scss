.go-to-top {
    --to-top-btn-size: 45;

    bottom: calc(var(--to-top-btn-size) / 2 * 1px);
    display: block;
    opacity: 1;
    position: relative;
    right: 20px;
    visibility: visible;
    z-index: 19;

    &__text {
        margin: 0;
        position: absolute;
        right: 0;
        text-align: right;
    }

    &__link {
        @include font(null, 1.2rem, var(--fw-normal));
        @include size(calc(var(--to-top-btn-size) * 1px));
        @include trs;
        align-items: center;
        background-color: var(--color-1--1);
        border-radius: 40px;
        color: $color-white;
        display: inline-flex;
        justify-content: center;
        text-decoration: none;

        @include on-event {
            background-color: var(--color-2--2);
            color: $color-white;
        }
    }

    &.is-hidden {
        display: none;
        opacity: 0;
        visibility: hidden;
    }
}
