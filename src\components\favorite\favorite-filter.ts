import { addClass, removeClass } from '@core/utils/class.utils';

export function checkAndHandleEmptyFavorites(): void {
    const noFavoritesMessage = document.querySelector('.no-favorites') as HTMLElement;
    if (!noFavoritesMessage) {
        return;
    }

    const favoritesContainer = document.querySelector('.favorites') as HTMLElement;
    if (!favoritesContainer) {
        removeClass(noFavoritesMessage, 'is-hidden');
        return;
    }

    const allFavoriteItems = favoritesContainer.querySelectorAll('.favorite-item');

    if (allFavoriteItems.length === 0) {
        favoritesContainer.remove();
        removeClass(noFavoritesMessage, 'is-hidden');
    } else {
        addClass(noFavoritesMessage, 'is-hidden');
    }
}

export function observeFavorites(): void {
    const favoritesContainer = document.querySelector('.favorites');
    if (!favoritesContainer) {
        checkAndHandleEmptyFavorites();
        return;
    }

    const lists = favoritesContainer.querySelectorAll('.list');

    lists.forEach(list => {
        const observer = new MutationObserver(() => {
            checkAndHandleEmptyFavorites();
        });

        observer.observe(list, {
            childList: true,
            subtree: true,
        });
    });

    checkAndHandleEmptyFavorites();
}
