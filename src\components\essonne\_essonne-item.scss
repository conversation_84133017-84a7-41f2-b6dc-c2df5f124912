.essonnes-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    flex-direction: column-reverse;
    height: fit-content;
    justify-content: flex-end;
    margin: 0 auto;
    width: 100%;

    @include breakpoint(medium down) {
        align-items: center;
        flex-direction: column-reverse;
    }

    @include breakpoint(small down) {
        padding-left: 0;
    }

    @include on-event() {
        #{$this}__image {
            img {
                transform: matrix(0.99, 0.03, -0.04, 0.99, 0, 0);
            }
        }

        .essonnes-item__category {
            background-color: var(--color-1--1);
            color: $color-white;
        }
    }

    &:nth-child(1) {
        align-items: flex-end;
        flex-direction: row-reverse;
        grid-area: 1 / 2 / 2 / 4;
        justify-content: flex-start;
        margin-bottom: 80px;

        @include breakpoint(medium down) {
            align-items: center;
            flex-direction: column-reverse;
            grid-area: 1 / 1 / 2 / 2;
            margin-bottom: 0;
            margin-top: 15px;
        }

        @include breakpoint(small down) {
            margin-bottom: 58px;
        }

        #{$this}__content {
            max-width: 224px;

            @include breakpoint(medium down) {
                max-width: 260px;
            }

            @include breakpoint(small down) {
                max-width: 320px;
            }
        }

        #{$this}__title {
            padding: 22px 0 7px 24px;

            @include breakpoint(medium down) {
                padding: 0;
            }
        }

        #{$this}__category {
            @include breakpoint(medium down) {
                margin-left: auto;
                margin-right: auto;
                margin-top: -13px;
                position: relative;
                z-index: 1;
            }
        }

        #{$this}__image {
            &::before {
                @include size(374px, 280px);
                @include absolute(-15px, null, null, -10px);
                background-color: var(--color-1--1);
                border-radius: 10px;
                content: "";
                transform: matrix(0.99, -0.14, 0.14, 0.99, 0, 0);
                z-index: -1;

                @include breakpoint(medium down) {
                    @include size(212px, 150px);
                    @include absolute(auto, null, 6px, -6px);
                    transform: matrix(0.99, -0.14, 0.14, 0.99, 0, 0);
                }
            }
        }
    }

    &:nth-child(2) {
        grid-area: 2 / 1 / 3 / 2;
        margin-right: 67px;
        max-width: 333px;

        @include breakpoint(medium down) {
            grid-area: 1 / 2 / 2 / 3;
            margin-bottom: 0;
            margin-right: 0;
            max-width: 100%;
        }

        @include breakpoint(small down) {
            margin-bottom: 50px;
        }

        #{$this}__title {
            margin-top: -15px;
            padding-left: 33px;
            z-index: 1;

            @include breakpoint(medium down) {
                padding-left: 0;
            }
        }

        #{$this}__category {
            margin-bottom: 9px;

            @include breakpoint(medium down) {
                margin-left: auto;
                margin-right: auto;
            }
        }

        #{$this}__image {
            &::before {
                @include size(317px, 254px);
                @include absolute(null, null, 40px, 30px);
                background-color: var(--color-2--1);
                border-radius: 10px;
                content: "";
                transform: matrix(-0.97, -0.26, 0.26, -0.97, 0, 0);
                z-index: -1;

                @include breakpoint(medium down) {
                    @include size(150px, 147px);
                    @include absolute(auto, null, 20px, 45px);
                    transform: matrix(-0.97, -0.26, 0.26, -0.97, 0, 0);
                    z-index: -1;
                }
            }
        }

        #{$this}__content {
            &::after {
                margin-left: 30px;
                margin-top: 11px;
            }
        }
    }

    &:nth-child(3) {
        grid-area: 2 / 2 / 3 / 3;

        @include breakpoint(medium down) {
            grid-area: 2 / 1 / 3 / 2;
        }

        @include breakpoint(small down) {
            margin-bottom: 37px;
        }

        #{$this}__title {
            margin-top: -15px;
            padding: 0 0 0 45px;
            width: 252px;
            z-index: 1;
        }

        #{$this}__image {
            &::before {
                @include size(100%, 360px);
                @include absolute(null, null, 20px, 20px);
                background-color: var(--color-1--1);
                border-radius: 10px;
                content: "";
                transform: matrix(0.99, -0.1, 0.1, 0.99, 0, 0);
                z-index: -1;

                @include breakpoint(medium down) {
                    @include size(150px, 203px);
                    @include absolute(auto, null, 14px, 25px);
                    transform: matrix(0.99, -0.1, 0.1, 0.99, 0, 0);
                    z-index: -1;
                }
            }
        }

        #{$this}__content {
            &::after {
                margin-left: 42px;
                margin-top: 11px;
            }
        }
    }

    &:nth-child(4) {
        grid-area: 2 / 3 / 3 / 4;

        @include breakpoint(medium down) {
            grid-area: 2 / 2 / 3 / 3;
        }

        @include breakpoint(small down) {
            margin-bottom: 60px;
        }

        #{$this}__title {
            margin-top: -15px;
            padding-left: 50px;
            width: 252px;
            z-index: 1;
        }

        #{$this}__image {
            &::before {
                @include size(377px, 260px);
                @include absolute(42px, null, null, 20px);
                background-color: var(--color-2--1);
                border-radius: 10px;
                content: "";
                transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);
                z-index: -1;

                @include breakpoint(medium down) {
                    @include size(208px, 144px);
                    @include absolute(auto, null, -5px, 15px);
                    transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);
                    z-index: -1;
                }
            }
        }

        #{$this}__content {
            &::after {
                margin-left: 47px;
                margin-top: 11px;
            }
        }
    }

    &__image {
        background-color: transparent !important;
        border-radius: 10px;
        display: block;
        position: relative;
        width: fit-content;

        img {
            @include object-fit();
            @include size(auto, 100%);
            border-radius: 10px;

            @include breakpoint(small down) {
                height: auto;
            }
        }
    }

    &__content {
        @include line-decor(25px, 4px, "after", $color: var(--color-1--1));
        flex: 1;

        &::after {
            margin-left: 24px;
            transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);

            @include breakpoint(medium down) {
                margin-left: auto !important;
                margin-right: auto;
                margin-top: 0 !important;
            }
        }

        @include breakpoint(medium down) {
            flex-grow: 1;
            margin: 0 0 0 -17px;
            max-width: 100%;
        }

        @include breakpoint(small down) {
            margin-left: -15px;
            max-width: 68.5%;
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    &__title {
        &::after {
            margin-top: 13px;
            position: absolute;
            transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);

            @include breakpoint(small down) {
                margin-top: 7px;
            }
        }

        @include breakpoint(medium down) {
            font-size: 2.4rem;
            padding-left: 0 !important;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 3rem, var(--fw-bold));
        display: block;
        line-height: 34px;
        margin-top: 5px;
        width: 100%;

        @include breakpoint(medium down) {
            margin-top: 0;
            text-align: center;
        }
    }

    &__category {
        background-color: #fff;
        border: 1px solid var(--color-1--1);
        border-radius: 15px;
        font-size: 1.3rem;
        font-weight: var(--fw-normal);
        letter-spacing: 0;
        line-height: 14px;
        padding: 6px 14px;
        position: relative;
        text-transform: capitalize;
        width: fit-content;
        z-index: 1;

        @include breakpoint(medium down) {
            margin-bottom: 0 !important;
            margin-left: auto;
            margin-right: auto;
        }

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }
}
