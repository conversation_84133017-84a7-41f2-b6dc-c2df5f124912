.newswall-item {
    $this: &;

    @extend %link-block-context;
    align-items: center;
    display: flex;
    flex-direction: column;
    max-width: 50%;
    position: relative;
    width: 563px;
    z-index: 1;

    @include breakpoint(medium down) {
        flex-direction: row-reverse !important;
        justify-content: center !important;
        margin: 60px auto 0 !important;
        max-width: 100%;
        width: 100%;
    }

    @include breakpoint(small down) {
        flex-direction: column !important;
        width: 320px;
    }

    &.is-facebook {
        #{$this}__text {
            position: relative;

            @include breakpoint(medium down) {
                font-size: 2rem;
            }

            &::before {
                @include absolute(null, null, null, 11px);
                @include size(122px, 4px);
                background-color: $facebook-bg;
                content: "";
                transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
            }
        }

        #{$this}__icon {
            background-color: $facebook-bg;
        }
    }

    &.is-instagram {
        @include breakpoint(medium down) {
            height: 230px;
            order: 3;
        }

        @include breakpoint(small down) {
            height: auto;
            margin-top: 52px !important;
        }

        #{$this}__text {
            position: relative;

            &::before {
                @include absolute(20px, null, null, 11px);
                @include size(122px, 4px);
                background: $instagram-bg;
                content: "";
                transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
            }
        }

        #{$this}__icon {
            background: $instagram-bg;

            @include breakpoint(small down) {
                background: transparent linear-gradient(196deg, #fec35e 0%, #d42d76 46%, #4d61d3 100%) 0% 0% no-repeat padding-box;
            }
        }
    }

    &.is-x {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        position: relative;

        @include breakpoint(medium down) {
            order: 5;
        }

        @include breakpoint(small down) {
            margin-top: 54px !important;
        }

        #{$this}__text {
            margin-top: 0;
            order: 3;
            padding: 33px 102px 33px 73px;
            position: relative;
            margin-bottom: 60px;

            @include breakpoint(medium down) {
                margin-left: 0;
                margin-right: 25px;
                padding: 0;
            }

            @include breakpoint(small down) {
                font-size: 2rem;
                line-height: 24px;
                margin-top: 12px;
                padding-left: 67px;
                padding-right: 0;
            }

            &::before {
                @include breakpoint(large only) {
                    @include size(calc(100% + 59px), calc(100% + 30px));
                    @include absolute(-25px, null, null, -30px);
                    background-color: $color-1--3;
                    border-radius: 15px;
                    clip-path: polygon(0 0, 100% 0, 100% 93%, 5% 100%);
                    content: '';
                    opacity: 0.5;
                    z-index: -1;
                }

                @include breakpoint(small down) {
                    @include absolute(-5px !important, null, null, -8px !important);
                    @include size(85px !important, 4px);
                    background-color: $color-black;
                    content: "" !important;
                    transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
                }
            }
        }

        #{$this}__image {
            margin-top: 29px;

            @include breakpoint(large only) {
                order: 4;
            }
        }

        #{$this}__icon {
            background-color: $x-bg;
            margin-bottom: -12px;

            @include breakpoint(medium only) {
                left: 32px;
                order: 2;
                position: relative;
            }

            @include breakpoint(small down) {
                @include absolute(0, null, null, null);
            }
        }
    }

    &.is-linkedin {
        margin-top: 130px;

        @include breakpoint(medium down) {
            order: 9;
        }

        @include breakpoint(small down) {
            margin-top: 68px !important;
        }

        #{$this}__icon {
            background-color: $linkedin-bg;
        }

        #{$this}__text {
            position: relative;

            &::before {
                @include absolute(-13px, null, null, 11px);
                @include size(122px, 4px);
                background-color: $linkedin-bg;
                content: "";
                transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
            }
        }
    }

    &.is-youtube {
        margin-top: -130px;

        @include breakpoint(medium down) {
            height: 230px;
            order: 10;
        }

        @include breakpoint(small down) {
            height: auto;
            margin-top: 68px !important;
        }

        #{$this}__image {
            @include breakpoint(medium only) {
                order: -1;
            }
        }

        #{$this}__icon {
            background-color: $youtube-bg;

            @include breakpoint(medium down) {
                margin-left: 30px;
                margin-right: -30px;
            }
        }

        #{$this}__text {
            position: relative;

            @include breakpoint(medium down) {
                order: 0;
            }

            &::before {
                @include absolute(20px, null, null, 11px);
                @include size(122px, 4px);
                background-color: $youtube-bg;
                content: "";
                transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
            }
        }
    }

    &.is-tiktok {
        margin-top: 140px;

        @include breakpoint(medium down) {
            order: 11;
        }

        @include breakpoint(small down) {
            margin-top: 70px !important;
        }

        #{$this}__icon {
            background-color: $tiktok-bg;
        }

        #{$this}__text {
            position: relative;

            &::before {
                @include absolute(-10px, null, null, 11px);
                @include size(122px, 4px);
                background-color: $tiktok-bg;
                content: "";
                transform: matrix(-0.26, -0.97, 0.97, -0.26, 0, 0);
            }
        }
    }

    &__account-informations {
        order: 2;
        padding-left: 28px;
    }

    &__icon {
        @include size(80px);
        @include font(var(--tyop-1), 3rem, var(--fw-normal));
        align-items: center;
        border-radius: 40px;
        color: $color-white;
        display: flex;
        justify-content: center;
        margin-bottom: -43px;
        text-decoration: none;
        z-index: 2;

        &::after {
            @include size(100%);
            @include absolute(0, 0, 0, 0);
            content: "";
        }

        @include breakpoint(medium only) {
            @include size(60px);
            font-size: 2.5rem;
            margin-bottom: 0;
            margin-left: -30px;
            min-width: 60px;
        }

        @include breakpoint(small down) {
            @include min-size(40px);
            @include size(40px);
            font-size: 1.8rem;
            margin-bottom: -23px;
            min-width: 40px;
        }

        svg {
            @include size(26px, 100%);
            fill: $color-white;

            @include breakpoint(small down) {
                width: 15px;
            }
        }
    }

    &__picture {
        z-index: 1;
    }

    &__image {
        -webkit-mask-image: image("Mask-Img-Social-Desktop.svg");
        mask-image: image("Mask-Img-Social-Desktop.svg");
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: contain;
        mask-size: contain;
        width: 563px;

        @include breakpoint(medium down) {
            width: 320px;
        }

        img {
            -webkit-mask-image: image("Mask-Img-Social-Desktop.svg");
            mask-image: image("Mask-Img-Social-Desktop.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
        }
    }

    &__account-name {
        @include font(var(--tyop-1), 2.4rem, var(--fw-normal));
        text-decoration: none;
        z-index: 1;

        @include breakpoint(medium down) {
            font-size: 2rem;
        }
    }

    &__account-id {
        @include font(var(--tyop-1), 2.4rem, var(--fw-normal));
        margin-bottom: 10px;
        text-decoration: none;
        z-index: 1;

        @include breakpoint(medium down) {
            font-size: 2rem;
        }
    }

    &__text {
        @include font(var(--tyop-1), 2.4rem, var(--fw-normal));
        margin-top: 20px;
        padding-left: 104px;
        text-decoration: none;
        z-index: 1;

        @include breakpoint(medium only) {
            font-size: 2rem;
            margin-left: 25px;
            margin-top: 0;
            order: -1;
            padding-left: 0;
            width: 267px;

            &::before {
                content: none !important;
            }
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: 24px;
            margin-top: 12px;
            padding-left: 67px;
            padding-right: 0;

            &::before {
                content: "" !important;
                left: -8px !important;
                top: -5px !important;
                width: 85px !important;
            }
        }
    }

    &:nth-child(odd) {
        #{$this}__image {
            -webkit-mask-image: image("Mask-Img-Social-Desktop-Left.svg");
            mask-image: image("Mask-Img-Social-Desktop-Left.svg");

            @include breakpoint(large only) {
                height: 403px;
            }

            img {
                -webkit-mask-image: image("Mask-Img-Social-Desktop-Left.svg");
                mask-image: image("Mask-Img-Social-Desktop-Left.svg");
            }
        }

        #{$this}__text {
            margin-top: 14px;
        }
    }
}
