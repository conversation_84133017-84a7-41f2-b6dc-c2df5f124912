.quicklinks-content {
    margin: 10px 0 40px;

    .quicklinks-content-inner {
        margin: 0 50px;
        padding: 25px 0;

        @include breakpoint(medium down) {
            margin: 0;
        }

        @include breakpoint(small down) {
            margin: 0 25px;
        }
    }

    .quicklinks-content-block {
        &__container {
            width: calc(100% - 115px);
        }
    }

    &.is-width-33,
    &.is-width-66 {
        margin-top: 0;
    }

    &.is-width-33 {
        .quicklinks-content-inner {
            &__list {
                align-items: center;
                display: flex;
                flex-direction: column;

                @include breakpoint(medium only) {
                    flex-direction: row;
                    justify-content: center;
                }
            }

            &__list-item {
                min-width: 140px;

                &:not(:last-child) {
                    margin-bottom: 35px;

                    @include breakpoint(medium only) {
                        margin: 0;
                    }
                }
            }
        }
    }
}
