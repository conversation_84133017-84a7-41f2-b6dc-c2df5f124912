{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/core-components/secondary.njk' as Secondary -%}
{%- from 'views/utils/constants.njk' import kGlobalLinks -%}

{#
    DeliberationsListItem template.
    @param {object} settings - publication item settings.
#}
{%- macro DeliberationsListItem(
    meta = 'pdf',
    link = 'single-deliberations.html',
    imageSize = [['177x250'], ['250x177'], ['250x250']] | random,
    category = 'Dolor sit amet',
    title = 'Lorem ipsum dolor sit amet consectur elis',
    subtitle = 'Septembre 2099 - N°999',
    status = false,
    documents = range(1, 5) | random
) -%}
    <article class="deliberations-list-item">
        <a href="./{{ link }}" class="deliberations-list-item__title-link">
            {{ Image({
            className: 'deliberations-list-item__image',
            sizes: imageSize,
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(100) | random,
            alt: ''
        }) }}
            <div class="deliberations-list-item__content">
                <h3 class="item-title deliberations-list-item__title">
                    {% if category %}
                        <span class="theme deliberations-list-item__category">{{ category }}</span>
                        <span class="sr-only">:</span>
                    {% endif %}
                    <span class="underline">{{ title }}</span>
                    {%- if subtitle -%}
                        <span class="deliberations-list-item__subtitle">{{ subtitle }}</span>
                    {%- endif -%}
                </h3>
                {%- if deadline -%}
                    <div class="deliberations-list-item__deadline">
                        {{ Secondary.Deadline(className = 'is-small') }}
                    </div>
                {%- endif %}
                {%- if status %}
                    <div class="deliberations-list-item__status">
                        {{ Secondary.Status(type = status) }}
                    </div>
                {%- endif %}
                {%- if documents !== 1-%}
                    <p class="publication is-primary deliberations-list-item__publication">
                        <span class="publication__number">{{ documents }}
                            documents</span>
                        <span>Publié le
                        </span>
                        <time datetime="2018-03-28">28/03/2018</time>
                    </p>
                {%- endif -%}
                {%- if documents === 1 -%}
                    <div class="deliberations-list-item__actions">
                        <span class="telecharger-item__meta" id="{{ metaUid }}" aria-hidden="true">
                            <span class="telecharger-item__meta-text">{{ meta }}</span>
                            <span class="telecharger-item__size">
                                {{ range(2,10) | random }}.{{ range(2,50) | random }}
                                        Mo</span>
                                </span>
                                {{ Secondary.DocumentActions() }}
                            </div>
                        {% endif %}
                    </div>
                </article>
            </a>
        {%- endmacro -%}

        {#
    DeliberationsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
        {%- macro DeliberationsList(
    itemClass = 'has-mb-5',
    count = 12,
    cols = 4,
    mdCols = 1,
    smCols = false,
    xsCols = false
) -%}
            {% call List(itemClass = itemClass, count = count, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols) %}
            {{ DeliberationsListItem() }}
            {% endcall %}
        {%- endmacro -%}

        {#
    ListPublicMarket template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
        {%- macro ListPublicMarket(
    listClass = 'is-pablic-market',
    itemClass = 'has-mb-5',
    count = 12,
    cols = 4,
    mdCols = 1,
    smCols = false,
    xsCols = false
) -%}
            {% call List(listClass = listClass, itemClass = itemClass, count = count, cols = cols, mdCols = mdCols, smCols = smCols, xsCols = xsCols) %}
            {{ DeliberationsListItem(
            link = kGlobalLinks.singlePublicMarket,
            imageSize = ['177x250'],
            date = 'Date limite <time datetime="2022-06-22">22/06/2022</time>' | safe,
            status = ['new', 'in-progress', 'assign'] | random,
            deadline = false
        ) }}
            {% endcall %}
        {%- endmacro -%}