.user-position-button {
    @include trs($prop: opacity);
    @extend %button;
    @extend %button-size-small;

    background-color: $color-white;
    border: unset;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.16);
    display: flex;
    justify-content: center;
    margin-top: 10px;
    min-width: 45px;
    padding: 1.1em;

    .is-popup-open & {
        @include breakpoint(small down) {
            opacity: 0;
        }
    }

    @include fa-icon-style {
        color: var(--color-1--1);
        font-size: 1.6rem;
    }

    @include on-event {
        @include fa-icon-style {
            color: $color-white;
        }
    }

    .lds-ripple {
        display: none;
    }

    &.is-loading {
        background-color: var(--color-1--1);
        
        @include fa-icon-style {
            display: none;
        }

        .lds-ripple {
            display: block;
            margin-left: 0;
        }
    }

    .map-page & {
        margin-top: 0;
    }
}
