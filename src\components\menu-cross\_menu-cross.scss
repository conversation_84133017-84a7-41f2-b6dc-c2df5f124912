.menu-cross {
    $this: &;

    &__link {
        @include font(var(--typo-1), 1.3rem, var(--fw-normal), normal);
        @include trs;
        @include focus-outline($offset: 2px);
        background-color: transparent;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: flex;
        letter-spacing: 1.04px;
        padding: 0;
        position: relative;
        text: {
            align: center;
            decoration: none;
            transform: uppercase;
        }
        width: 100%;

        @include add-inverted-styles {
            color: $color-white;
        }

        @include on-event() {
            text-decoration: underline;
        }
    }

    &__list[class] {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    &__item {
        margin: 5px 0;
        padding: 0 14px;

        @include breakpoint(small down) {
            margin: 2px 0;
        }
    }

    &__text {
        color: inherit;
        flex-grow: 1;
        font: inherit;
    }
}
