.header-account {
    $this: &;

    &__container {
        @extend %container;
        position: relative;
        z-index: 2;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    &__wrapper {
        align-items: flex-end;
        display: flex;
        justify-content: space-between;
        margin-bottom: 40px;
        padding: 62px 0 0;
        width: 100%;

        @include breakpoint(medium down) {
            justify-content: center;
            padding-top: 30px;
        }

        @include breakpoint(small down) {
            margin-bottom: 38px;
        }
    }

    &__info {
        align-items: center;
        display: flex;
        max-width: 100%;

        @include breakpoint(medium down) {
            align-items: center;
            gap: 12px;
            justify-content: center;
            max-width: 100%;
            width: 100%;
        }
    }

    &__right {
        @include font(null, 2.8rem, var(--fw-normal));
        align-items: flex-end;
        color: var(--color-1--1);
        display: flex;
        flex-direction: column;
        margin-bottom: 40px;
        text-decoration: none;

        @include breakpoint(medium down) {
            align-items: center;
            margin: 5px auto 0;
        }

        @include on-event {
            #{$this}__return-text {
                text-decoration: none;
            }
        }

        &:focus {
            outline-offset: 3px;
        }

        strong {
            font-weight: var(--fw-black);
        }
    }

    &__return {
        color: $color-3--5;
        font-size: 1.4rem;
        line-height: 1.15;
        margin-top: 20px;
        text-decoration: none;

        span {
            display: inline-block;
        }

        &-text {
            text-decoration: underline;
        }

        @include fa-icon-style {
            margin-right: 3px;
        }
    }

    &__title {
        font-size: 5.5rem;
        font-weight: var(--fw-black);
        line-height: 1;

        @include breakpoint(medium down) {
            font-size: 4.5rem;
            text-align: center;
        }

        @include breakpoint(small down) {
            font-size: 3.8rem;
        }
    }

    &__icon {
        margin-right: 14px;

        @include breakpoint(medium down) {
            margin: 0 0 8px;
            text-align: center;
        }

        &::before {
            color: var(--color-1--1);
            font-size: 6rem;

            @include breakpoint(small down) {
                font-size: 5rem;
            }
        }
    }

    &__description {
        color: $color-black;
        font-size: 1.6rem;
        font-weight: var(--fw-normal);
        line-height: 3rem;
        margin-top: 20px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: 2.5rem;
            margin-top: 10px;
            max-width: 768px;
            text-align: center;
        }
    }
}
