{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'components/popup-close-btn/popup-close-btn.njk' import PopupCloseBtn -%}

{% macro Popup(
    id = '',
    iconName= '',
    modifier = '',
    closeBtn = {
        modifier: 'popup__close-btn',
        ghostText: true,
        tooltip: 'Fermer les filtres'
    }
) %}
    <div class="popup {{ modifier }}" id="{{ id }}">
        {{ caller() if caller }}
        {% if closeBtn %}
            {{ PopupCloseBtn(
                modifier = closeBtn.modifier,
                ghostText = closeBtn.ghostText,
                tooltip = closeBtn.tooltip,
                attrs = {
                    'aria-haspopup': 'true'
                }
            ) }}
        {% endif %}
    </div>
{%- endmacro -%}

