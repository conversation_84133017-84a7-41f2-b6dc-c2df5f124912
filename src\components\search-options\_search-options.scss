.search-facet-options,
.search-sort-options {
    $this: &;
    border-radius: 17px;

    &__item {
        align-items: flex-start;
        display: flex;
        margin-bottom: 5px;
    }

    &__decor {
        @include icon-before($fa-var-angle-right);
        color: var(--color-1--1);
        font-size: 1.7rem;
        font-weight: var(--fw-bold);
        margin-right: 5px;
    }

    &__link {
        @include font(var(--typo-1), 1.7rem, var(--fw-light));
        align-items: flex-start;
        color: $color-3--5;
        display: flex;
        line-height: 1.55;
        text-decoration: none;

        @include on-event {
            .search-facet-options,
            .search-sort-options {
                &__link-text {
                    text-decoration: underline;
                }
            }
        }
    }

    &__link-text,
    &__link-meta {
        pointer-events: none;
    }

    &__link-text {
        line-height: inherit;
    }

    &__link-meta,
    &__remove {
        margin-left: 5px;
    }

    &__remove {
        @include icon-before($fa-var-times);
        font-size: 1.3rem;
        font-weight: var(--fw-bold);
        margin-top: 4px;
    }
}
