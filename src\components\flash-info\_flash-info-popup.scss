/*
 * @name flash-info is-popup.
 */

.flash-info {
    $this: &;

    &.is-popup {
        @include fixed(0, 0, 0, 0);
        align-items: center;
        background-color: rgba($color-black, 0.8);
        padding: 0 60px;
        z-index: 101;

        @include breakpoint(medium down) {
            padding: 0 20px;
        }

        &.is-visible {
            display: flex;
        }

        #{$this}__wrapper {
            background-color: $color-white;
            border-radius: 10px;
            display: block;
            max-height: 70vh;
            max-width: 690px;
            overflow-y: auto;
            padding: 0 0 28px;
            position: relative;

            @include breakpoint(medium down) {
                padding: 0;
            }
        }
    }
}

body.js-flash-info-overflow {
    height: 100vh;
    overflow: hidden;
}
