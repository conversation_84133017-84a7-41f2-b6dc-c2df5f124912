{% from 'views/utils/constants.njk' import kGlobalLinks %}
{% from 'views/core-components/widget.njk' import Widget %}
{% from 'views/core-components/section.njk' import Section %}
{% from 'views/core-components/list.njk' import List %}
{% from 'views/core-components/image.njk' import Image %}
{% from 'views/core-components/link.njk' import Link %}
{% from 'views/core-components/title.njk' import TitlePrimary, TitleWidget, TitleRTE %}
{% from 'views/core-components/secondary.njk' import Date %}
{%- from 'components/share/share.njk' import Share -%}
{%- from 'views/core-components/secondary.njk' import TagLinks, TimePlace -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/utils/utils.njk' import svg -%}
{% from 'views/core-components/carousel.njk' import CarouselWrapper %}

{#
    EventsItem template.
#}
{%- macro EventsItem(
    title = lorem(5, 'words'),
    timePlace = true,
    category = lorem(range(1, 3) | random, 'words'),
    imageSizes = ['285x285'],
    useShare = false,
    tag = 'h3',
    type = 'Stand-up, humour, tout public',
    className = '',
    time = {
        start: Helpers.time({ h: 14 }) | replace('-', ':')
    },
    nbPlaces = 121,
    badge = {
        pictogram: 'far fa-eye',
        title: 'RDV nature' if (range(0, 2) | random) == 1 else None
    },
    iconBadge = svg('icons/leaf') if (range(0, 2) | random) == 1 else None
) -%}
    <article class="event-item {{ className }}">
        <div class="event-item__wrap">
            {%- if badge.title and iconBadge %}
                <div class="event-badge">
                    <div class="event-badge__Pictogramm">{{ iconBadge }}</div>
                    <div class="event-badge__title">{{ badge.title }}</div>
                </div>
            {%- elif badge.title %}
                <div class="event-badge">
                    <div class="event-badge__title">{{ badge.title }}</div>
                </div>
            {%- elif iconBadge %}
                <div class="event-badge">
                    <div class="event-badge__Pictogramm">{{ iconBadge }}</div>
                </div>
            {%- endif %}
            <div class="event-item__content {{ 'event-item__content-without-cercle' if not (badge.title and badge.title | length > 0 ) and not (iconBadge) }}">
                {%- if category %}
                    <p class="event-item__category">{{ category }}</p>
                {%- endif %}
                {%- if title %}
                    <{{ tag }} class="item-title event-item__title">
                        <a href="{{ kGlobalLinks.singleEvents }}" class="event-item__title-link">
                            <span class="underline">{{ title }}</span>
                        </a>
                    </{{ tag }}>
                {%- endif %}
                {%- if type %}
                    <p class="event-item__type">{{ type }}</p>
                {%- endif %}
                {%- if timePlace %}
                    {{ TimePlace(className = 'event-item__time-place', time = time) }}
                {%- endif %}
                {%- if nbPlaces > 0 %}
                    <p class="event-item__nbplaces">{{ nbPlaces }} Places restantes</p>
                {%- else %}
                    <p class="event-item__nbplaces">Complet</p>
                {%- endif %}
            </div>
            {{ Date({ className: 'event-item__date' }) }}
        </div>
        {{ Image({
            className: 'event-item__image',
            sizes: imageSizes,
            alt: 'image alt',
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(100) | random
        }) }}
        {%- if useShare %}
            <div class="event-item__actions">
                {{ Share() }}
            </div>
        {%- endif %}
    </article>
{%- endmacro -%}

{#
    EventsLinksItem template.
#}

{% macro EventsLinksItem(title = lorem(3, 'word'), icon = 'archery') %}
    <div class="eventlinks-item">
        <div class="eventlinks-item__svg-wrapper" aria-hidden="true">
            {{ svg('icons/' + icon) }}
        </div>
        <a href="#" class="eventlinks-item__link">
            <span class="underline">{{ title | safe }}</span>
        </a>
        <span class="eventlinks-item__icon far fa-long-arrow-right" aria-hidden="true"></span>
    </div>
{% endmacro %}

{% set defaultQuickLinksParams = {
    listClass: 'eventlinks-list',
    itemClass: 'eventlinks-list__item',
    cols: 3,
    mdCols: 2,
    smCols: 1,
    linksList: [
        ['man-and-trash', 'Propreté'],
        ['payment', 'Paiement en ligne'],
        ['cone', 'Infos travaux'],
        ['man-and-message', 'Je signale...'],
        ['route', 'Annuaires'],
        ['family', 'Portail famille']
    ]
}%}

{#
    EventsLinksList template.
#}

{% macro EventsLinksList(settings = {}) %}
    {% set params = Helpers.merge(defaultQuickLinksParams, settings) %}
    <ul class="list {{ params.listClass }} is-columns-{{ params.cols }} is-columns-md-{{ params.mdCols }} is-columns-sm-{{ params.smCols }}">
        {% for linkIcon, linkTitle in params.linksList %}
            <li class="list__item {{ params.itemClass }}">
                {{ EventsLinksItem(icon = linkIcon, title = linkTitle) }}
            </li>
        {% endfor %}
    </ul>
{% endmacro %}

{#
    EventsBanner template.
#}

{%- macro EventsBanner(
    title = 'Découvrez toute la programmation culturelle!',
    imageSizes = ['320x135?479', '600x250'],
    className = ''
    ) -%}
    <div class="event-banner {{ className }}" style="background-color: #c5c5c5;">
        <div class="event-banner__content">
            <h3 class="item-title event-banner__title">
                <a href="#" class="event-banner__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
            </h3>
            {{ Icon('fas fa-long-arrow-right') }}
        </div>
        {{ Image({
                className: 'event-banner__image',
                sizes: imageSizes,
                type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
                serviceID: range(100) | random,
                alt: 'image alt'
            }) }}
    </div>
{%- endmacro -%}

{#
    EventsFocus template.
#}
{%- macro EventsFocus(
    title = 'Lorem ipsum dolor',
    titleBold = 'groupe Belle aventure',
    timePlace = true,
    category = 'Festival',
    type = 'Stand-up, humour, tout public',
    imageSizes = [ '282x282?767', '432x432'],
    useShare = false,
    className = '',
    time = {
        start: Helpers.time({ h: 14 }) | replace('-', ':')
    },
    displayNbPlaces = true,
    nbPlaces = 121,
    badge = {
        pictogram: 'far fa-eye',
        title: 'RDV nature'
    },
    moreButton = false,
    proposerButton = false,
    teaser = false
) -%}
    <div class="event-focus {{ className }}">
        <article class="event-focus__wrapper">
            <div class="event-focus__content">
                <div class="event-focus__content-wrapper">
                    <div class="event-badge">
                        <div class="event-badge__Pictogramm">{{ svg('icons/leaf') }}</div>
                        <div class="event-badge__title">{{badge.title}}</div>
                    </div>
                    <div class="event-focus__text">
                        {%- if category %}
                            <p class="event-focus__category">{{ category }}</p>
                        {%- endif %}
                        <h3 class="item-title is-large event-focus__title">
                            <a href="#" class="event-focus__title-link">
                                <span class="underline">{{ title }}</span>
                            </a>
                        </h3>
                        {%- if type %}
                            <p class="event-focus__type">{{ type }}</p>
                        {%- endif %}
                        {%- if teaser %}
                            <p class="event-focus__teaser">{{ teaser }}</p>
                        {%- endif %}
                        {%- if timePlace %}
                            {{ TimePlace(className = 'event-focus__time-place', time = time) }}
                        {%- endif %}
                        {%- if displayNbPlaces %}
                            {%- if nbPlaces >0 %}
                                <p class="event-focus__nbplaces">{{ nbPlaces }}
                                    Places restantes</p>
                            {%- else %}
                                <p class="event-focus__nbplaces">Complet</p>
                            {%- endif %}
                        {%- endif %}
                    </div>
                </div>

                {% if moreButton or proposerButton %}
                    <div class="section__more-links is-left">
                        {% if proposerButton %}
                            {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un événement',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                        {% endif %}
                        {% if moreButton %}
                            {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Tous les événements',
                        className = 'btn is-primary',
                        icon = false
                    ) }}
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            {{ Date({
                className: 'is-vertical is-large event-focus__date'
            }) }}
            <a href="#" class="event-focus__picture-link" role="paragraph" tabindex="-1">
                {{ Image({
                    sizes: imageSizes,
                    className: 'event-focus__picture',
                    serviceID: range(50) | random,
                    alt: 'image alt'
                }) }}
            </a>
        </article>
        {% if useShare %}
            <div class="event-focus__actions">
                {{ Share() }}
            </div>
        {% endif %}
    </div>
{%- endmacro -%}

{#
    EventsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro EventsList(
    listClass = '',
    itemClass = '',
    insertedItemClass = '',
    count = 8,
    cols = 3,
    mdCols = 1,
    tag = 'h3',
    imageSizes = ['282x211']
) -%}
    {% call List(
        listClass = 'events-list ' + listClass,
        itemClass = 'events-list__item ' + itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols
    ) %}
    {{ EventsItem(tag = tag, className = insertedItemClass, imageSizes = imageSizes) }}
    {% endcall %}
{%- endmacro -%}

{%- macro EventsFocusContent(
    className = 'events-focus-content',
    titleText = 'À l\'affiche',
    moreButton = true,
    proposerButton = false,
    timePlace = true,
    displayNbPlaces = true,
    teaser = false,
    type = "Lorem ipsum dolor"
    ) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>

        <div class="section__content">
            {{ EventsFocus(moreButton = moreButton, proposerButton = proposerButton, timePlace = timePlace, displayNbPlaces = displayNbPlaces, teaser = teaser, type=type) }}
        </div>
{# xxxx #}
    {% endcall %}
{%- endmacro -%}

{#
    EventsContent template.
    Template for events on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {number} colsCount - count of cols
    @param {number} mdColsCount - count of cols on tablet
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro EventsContent(
    className = 'events-content',
    titleText = 'À l\'affiche',
    itemsCount = 6,
    colsCount = 3,
    anchors = false,
    textAncre = '',
    classNameTitle = false,
    moreButton = true,
    centeredButton = false,
    imageSizes = ['211x211?1279', '285x285']
) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__title">
        {{ TitleRTE(
                text = titleText,
                className = classNameTitle,
                anchors = anchors,
                textAncre = textAncre
            ) }}
    </div>
    <div class="section__content">
        {% call CarouselWrapper(settings = {
                wrapperClassName: 'event-carousel',
                wrapperTag: 'ul',
                itemsToShow: [itemsCount, 1, 1],
                arrows: {
                    next: {
                        icon: 'far fa-chevron-right'
                    },
                    prev: {
                        icon: 'far fa-chevron-left'
                    }
                },
                enableInteractiveAlign: true,
                loop: false,
                pagination: 'outside',
                enableNavigationAlign: false,
                actions: false,
                autoplay: false
            }) %}
        {%- for item in range(itemsCount) %}
            <li class="event-carousel__item swiper-item">
                {{ EventsItem(imageSizes = imageSizes) }}
            </li>
        {%- endfor %}
        {% endcall %}
    </div>
    {% if moreButton %}
        <div class="section__more-links {{ 'is-left' if not centeredButton }}">
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Tous les événements',
                        className = 'btn is-primary',
                        icon = false
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    EventsAccount template.
    Template for events on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {number} colsCount - count of cols
    @param {number} mdColsCount - count of cols on tablet
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro EventsAccount(
    className = 'events-content',
    titleText = 'À l\'affiche',
    itemsCount = 3,
    colsCount = 3,
    moreButton = true
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ EventsList(
                count = itemsCount,
                cols = colsCount
            ) }}
        </div>
        {% if moreButton %}
            <div class="section__more-links is-left">
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Tous les événements',
                        className = 'btn is-link is-small',
                        icon = 'far fa-long-arrow-right'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    EventsHome template.
    Template for events on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro EventsHome(
    tagLinks = true,
    eventsFocus = true,
    titleText = 'À l\'affiche',
    itemsCount = 5,
    moreButton = true,
    proposerButton = true,
    banner = true,
    quicklinks = true
) -%}
    {% call Section(className = 'events-home', container = 'events-home__container') %}
    <div class="section__title events-home__title">
        {{ TitlePrimary(
                text = titleText
            ) }}
        {% if tagLinks %}
            {{ TagLinks() }}
        {% endif %}
    </div>
    <div class="section__content events-home__content">
        {% if eventsFocus %}
            {{ EventsFocus() }}
        {% endif %}
        {% call CarouselWrapper(settings = {
                wrapperClassName: 'event-carousel',
                wrapperModifier: 'no-animate',
                jsClassName: 'events-js-swiper-coverflow',
                pagination: 'outside',
                itemsToShow: [3, 1, 1],
                autoplay: false,
                actions: false,
                wrapperTag: 'ul',
                arrows: {
                    next: {
                        icon: 'far fa-chevron-right'
                    },
                    prev: {
                        icon: 'far fa-chevron-left'
                    }
                }
            }) %}
        {%- for item in range(itemsCount) %}
            <li class="event-carousel__item swiper-item">
                {{ EventsItem(imageSizes = ['211x211?1279', '285x285']) }}
            </li>
        {%- endfor %}
        {% endcall %}
        <div class="carousel-pagination-event">
            {% for i in range(itemsCount) %}
                <span class="carousel-bullet-event {% if i == 0 %}active{% endif %}"></span>
            {% endfor %}
        </div>
    </div>
    {% if moreButton or proposerButton %}
        <div class="section__more-links events-home__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un événement',
                        className = 'btn is-inverted is-sm-small',
                        icon = false
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        href = kGlobalLinks.listEvents,
                        text = 'Tous les événements',
                        className = 'btn is-primary is-sm-small is-inverted-hover',
                        icon = false
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% if quicklinks %}
        <div class="section__eventlinks events-home__eventlinks">
            {{ EventsLinksList() }}
        </div>
    {% endif %}
    {% if banner %}
        <div class="section__banner events-home__banner">
            {{ EventsBanner() }}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    EventsSidebar template.
    Template for events in sidebar.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{%- macro EventsSidebar(
    titleText = 'En ce moment',
    itemsCount = 1,
    moreButton = true,
    proposerButton = false
) -%}
    {% call Widget(className = 'events-widget') %}
    <div class="widget__title">
        {{ TitleWidget(
                className = 'is-center',
                text = titleText
            ) }}
    </div>
    <div class="widget__content">
        {{ EventsList(
                count = itemsCount,
                cols = 1,
                mdCols = 3,
                smCols = 2,
                xsCols = 1
            ) }}
    </div>
    {% if moreButton or proposerButton %}
        <div class="widget__more-links">
            {% if proposerButton %}
                {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un événement',
                        className = 'btn is-small',
                        icon = 'far fa-calendar-plus'
                    ) }}
            {% endif %}
            {% if moreButton %}
                {{ Link(
                        text = 'Tous les événements',
                        className = 'btn is-small',
                        icon = 'far fa-plus'
                    ) }}
            {% endif %}
        </div>
    {% endif %}
    {% endcall %}
{%- endmacro -%}
