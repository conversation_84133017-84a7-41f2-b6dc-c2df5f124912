{%- from 'views/core-components/icon.njk' import Icon -%}

{% set defaultSettingsItem = {
    thematique: lorem(3, 'words'),
    children: []
} %}

{#
DemarcheItem template.
    @param {object} settings - user settings object
#}
{%- macro DemarcheItem(settings = {}) -%}
    {# Default params #}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}

    {% if params.children.length > 0%}
        <a href="#" class="demarche-en-ligne-item__thematique">{{ params.thematique }}</a>
    {% else %}
        <a href="list-guides-aides.html" class="demarche-en-ligne-item__thematique">{{ params.thematique }}</a>
    {% endif %}
{%- endmacro -%}

{#
DemarcheList template.
#}
{%- macro DemarcheList(
    list = [
        {
        thematique: lorem(3, 'words'),
        children: []
        },
        {
        thematique: lorem(3, 'words'),
        children: [
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] }
        ]
        },
        {
        thematique: lorem(3, 'words'),
        children: []
        },
        {
        thematique: lorem(3, 'words'),
        children: [
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] }
        ]
        },
        {
        thematique: lorem(3, 'words'),
        children: []
        },
        {
        thematique: lorem(3, 'words'),
        children: [
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] }
        ]
        },
        {
        thematique: lorem(3, 'words'),
        children: []
        },
        {
        thematique: lorem(3, 'words'),
        children: [
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] },
            { thematique: lorem(3, 'words'), children: [] }
        ]
        },
        {
        thematique: lorem(3, 'words'),
        children: []
        }
    ]
) -%}
    <div class="demarche-en-ligne">
        <div class="demarche-en-ligne__group">
            <a href="#" class="demarche-en-ligne__category">Thématique de niveau 1 du contenu</a>
            <a href="#" class="demarche-en-ligne__return-button">
                {{ Icon('fas fa-long-arrow-left') }}
                Retour
            </a>
        </div>
        <div class="flex-row">
            {% for item in list %}
                <div class="col-lg-4 col-md-6 col-xs-12 demarche-en-ligne__container">
                    {{ DemarcheItem(item) }}
                </div>
            {% endfor %}
        </div>
    </div>
{%- endmacro -%}
