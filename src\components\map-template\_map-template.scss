$map-filter-padding: 20;
$map-z-index: (
    filterColumnToggle: 2,
    filterColumn: 3,
    searchColumn: 2
);

.map-template {
    $this: &;

    &__wrapper {
        display: flex;
        height: 100vh;
        position: relative;
    }

    &__column-toggle {
        @include trs;
        @include absolute(20px, null, null, 50px);
        @include size(40px);
        align-items: center;
        background-color: var(--color-1--1);
        border: 0;
        border-radius: 50%;
        color: $color-white;
        cursor: pointer;
        display: flex;
        font-size: 1.5rem;
        justify-content: center;
        padding: 5px;
        z-index: map-get($map-z-index, filterColumnToggle);

        @include breakpoint(medium down) {
            left: 20px;
        }

        @include on-event {
            background-color: var(--color-1--1);
        }

        > * {
            pointer-events: none;
        }

        &[aria-expanded="true"] {
            display: none;
        }
    }

    &__column {
        @include size(365px, 100%);
        bottom: 88px;
        display: flex;
        flex-direction: column;
        max-width: 100%;
        overflow: hidden;
        position: relative;
        z-index: 15;

        @include breakpoint(small down) {
            bottom: 0;
        }

        &.is-search {
            padding: 0;
            z-index: map-get($map-z-index, searchColumn);

            @include breakpoint(medium down) {
                z-index: 4;
            }
        }

        &.is-active {
            display: flex !important;
        }

        &.is-hidden {
            display: none !important;
        }

        @include breakpoint(xsmall down) {
            .is-popup-open & {
                display: none !important;
            }
        }
    }

    &__main {
        flex-grow: 1;
        width: 1%;
    }

    &__top,
    &__content,
    &__bottom {
        width: 100%;
    }

    &__content {
        flex-grow: 1;
        height: 1%;
        padding: 28px 20px 0;

        .is-search & {
            padding: 0;
        }
    }

    &__content-scroll {
        height: 100%;
        overflow-y: auto;
    }

    .filter-wrapper,
    .filter,
    .filter .form,
    .filter__dropdown,
    .filter__container,
    nav {
        height: 100%;
    }

    .filter-wrapper {
        padding: 35px 0 0 0;
    }

    .filter {
        .form {
            align-content: center;
            align-items: center;
            display: flex;
            flex-direction: column;

            &__fieldset {
                display: flex;
                flex-direction: column;
                flex-grow: 1;
                height: 100%;
            }
        }

        &__dropdown {
            @include breakpoint(small down) {
                display: block;
            }
        }

        &__container {
            padding: 0 #{$map-filter-padding}px;
        }

        &__wrapper-inner[class][class] {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            height: 1%;
            padding: 0 2.5px;
        }

        &__fields {
            flex-grow: 1;
            height: 1%;
            margin-left: -#{$map-filter-padding}px;
            margin-right: -#{$map-filter-padding}px;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 0 #{$map-filter-padding}px;
            width: calc(100% + #{$map-filter-padding * 2}px);
        }

        &__field {
            flex-basis: 100%;
            max-width: 100%;
        }

        &__buttons {
            background-color: $color-3--1;
            justify-content: center;
            margin: 0 -#{$map-filter-padding}px;
            padding: 20px #{$map-filter-padding}px;
            width: calc(100% + #{$map-filter-padding * 2}px);

            > * {
                flex-grow: 1;
                font-size: 1.2rem;
                margin-right: 10px;
                min-height: 40px;
                padding: 1em 2.1em 0.9em;

                @include fa-icon-style(false) {
                    margin-top: 0;
                }
            }
        }
    }

    &__bottom {
        background-color: $color-3--1;
        margin-top: 35px;
        padding: 5px 30px 15px;

        .is-search & {
            padding: 20px 0;
        }
    }

    .filter-submit {
        max-width: 133px;
    }

    .is-active {
        background-color: transparent !important;

        a {
            background-color: transparent !important;
        }
    }
}
