.project-focus {
    $this: &;

    @extend %link-block-context;
    background-color: var(--color-1--1);
    position: relative;

    @include breakpoint(small down) {
        background-color: transparent;
        display: flex;
        flex-direction: column-reverse;
    }

    &__picture {
        position: relative;

        &::before {
            @include absolute(null, null, 0, 0);
            @include size(100%, 70%);
            background-image: linear-gradient(180deg, rgba($color-black, 0) 0%, rgba($color-black, 0.51) 48%, rgba($color-black, 0.6) 100%);
            content: '';
            z-index: 1;
        }
    }

    &__content {
        @extend %container;
        @include absolute(null, null, 0, 50%);
        @include size(100%);
        align-items: flex-end;
        display: flex;
        transform: translateX(-50%);
        z-index: 1;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            @include relative(null, null, auto, auto);
            padding: 0 30px;
            transform: none;
        }
    }

    &__text {
        @include line-decor(100px, 16px);
        flex-grow: 1;
        max-width: 450px;
        padding: 20px 20px 50px 0;
        position: relative;

        @include breakpoint(small down) {
            padding: 55px 0 30px;
        }

        &::before {
            @include absolute(null, null, 0, 0);
            transform: translateY(50%);

            @include breakpoint(small down) {
                bottom: auto;
                top: 0;
                transform: translateY(-50%);
            }
        }
    }

    &__category[class] {
        display: block;
        margin-bottom: 20px;

        @include breakpoint(small down) {
            color: var(--color-1--1);
            text-shadow: none;
        }
    }

    &__title[class] {
        position: relative;
        width: 100%;

        @include breakpoint(small down) {
            color: $color-black;
            text-shadow: none;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }
}
