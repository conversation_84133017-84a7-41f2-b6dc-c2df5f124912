import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import OnInit from '@core/decorators/init-method.decorator';
import {
  HTMLElementArrayMap,
  HTMLElementMap,
} from '@core/interfaces/base-types';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import {
  addClass,
  removeClass,
} from '@core/utils/class.utils';

interface IStratisLocationOptions extends IGlobalOptions<StratisLocation> {
    classList: {};
}

class StratisLocation extends StratisElementAbstract {
    public options: IGlobalOptions<IStratisLocationOptions> = {
        classList: {
            area: '.location-map__region',
            link: '.location-list__link',
        },
        DOMElements: {
            wrapper: '.location__wrapper',
            map: '.location-map',
            areas: '.location-map__region:array',
            listLink: '.location-list__link:array',
            tooltip: '.tooltip-map',
        },
        dataset: {},
        onInit: () => null,
    };

    public constructor(element: HTMLElement, options: Partial<IStratisLocationOptions>) {
        super(element, options);
        if (!this.created) {
            this.init();
        }
    }

    /**
     * Init map events
     */
    @OnInit()
    public initAreaHandlers(): void {
        const { areas, listLink } = this.options.DOMElements as HTMLElementArrayMap;
        const { wrapper, tooltip } = this.options.DOMElements as HTMLElementMap;

        areas!.forEach((area: Element): void => {
            area.addEventListener('mousemove', (browserEvent): void => this.updateTooltipPosition(browserEvent));
            area.addEventListener('mouseenter', (browserEvent): void => {
                const { target } = browserEvent as any;
                const id = target.getAttribute('data-area-id');
                const activeListItem = wrapper!.querySelector(`${this.options.classList.link}[data-area-id="${id}"]`);

                if (activeListItem) {
                    activeListItem.setAttribute('data-active', 'true');
                }

                tooltip!.style.display = 'block';
                this.updateTooltipText(browserEvent);
            });
            area.addEventListener('mouseleave', (): void => {
                tooltip!.style.display = 'none';
                this.removeActiveElement(listLink!);
            });
            area.addEventListener('focus', (browserEvent): void => {
                if (this.element && typeof this.element === 'object') {
                    addClass(this.element, 'focus');
                }

                tooltip!.style.display = 'block';
                this.updateTooltipText(browserEvent);
            });
            area.addEventListener('blur', (): void => {
                if (this.element && typeof this.element === 'object') {
                    removeClass(this.element, 'focus');
                }

                tooltip!.style.display = 'none';
            });
        });
    }


    /**
     * Init list events
     */
    @OnInit()
    public initListHandlers(): void {
        const { areas, listLink } = this.options.DOMElements as HTMLElementArrayMap;
        const { wrapper, tooltip } = this.options.DOMElements as HTMLElementMap;

        listLink!.forEach((item): void => {
            ['mouseenter', 'focus'].forEach((eventType): void => {
                item.addEventListener(eventType, (browserEvent): void => {
                    const { target } = browserEvent as any;
                    const id = target.getAttribute('data-area-id');
                    const area = wrapper!.querySelector(`${this.options.classList.area}[data-area-id="${id}"]`);

                    if (area) {
                        area.setAttribute('data-active', 'true');
                    }
                });
            });

            ['mouseleave', 'blur'].forEach((eventType): void => {
                item.addEventListener(eventType, (): void => {
                    tooltip!.style.display = 'none';
                    this.removeActiveElement(areas!);
                });
            });
        });
    }

    /**
     * Remove attribute of the active element from map or list
     * @param {Element[]} listLink - array of links
     */
    public removeActiveElement = (listLink: Element[]): void => {
        listLink.forEach((item: Element): void => item.removeAttribute('data-active'));
    };

    /**
     * Update tooltip position on map
     * @param browserEvent - event in DOM
     */
    public updateTooltipPosition = (browserEvent): void => {
        const { map, tooltip } = this.options.DOMElements as HTMLElementMap;

        let coords;

        if (browserEvent.type === 'focus') {
            coords = this.getFocusOffset(map, browserEvent);
        } else {
            coords = this.getMouseOffset(map, browserEvent);
        }

        const x = coords.left;
        const y = coords.top;

        tooltip!.style.top = `${Math.floor(y) - 25}px`;
        tooltip!.style.left = `${Math.floor(x)}px`;
    };

    /**
     * Get coordinates of path center on map
     * @param {HTMLElement} el - svg map
     * @param browserEvent - event in DOM
     * @return {object} - coordinates of path center
     */
    public getFocusOffset = (el, browserEvent): object => {
        const elRect = el.getBoundingClientRect();
        const targetRect = browserEvent.target.getBoundingClientRect();

        return {
            top: targetRect.top - elRect.top + (targetRect.bottom - targetRect.top) / 2,
            left: targetRect.left - elRect.left + (targetRect.right - targetRect.left) / 2,
        };
    };

    /**
     * Get coordinates of mouse on map
     * @param {HTMLElement} el - svg map
     * @param browserEvent - event in DOM
     * @return {object} coordinates of mouse on map
     */
    public getMouseOffset = (el, browserEvent): object => {
        const rect = el.getBoundingClientRect();

        return {
            top: browserEvent.pageY - (rect.top + (window.scrollY || window.pageYOffset)),
            left: browserEvent.pageX - (rect.left + (window.scrollX || window.pageXOffset)),
        };
    };

    /**
     * update tooltip text and position
     * @param browserEvent - event in DOM
     */
    public updateTooltipText = (browserEvent): void => {
        const { tooltip } = this.options.DOMElements as HTMLElementMap;

        const { target } = browserEvent;

        if (target) {
            tooltip!.innerHTML = target.getAttribute('aria-label');
            this.updateTooltipPosition(browserEvent);
        }
    };
}

const StratisLocationFactory = StratisFactoryMixin<typeof StratisLocation, StratisLocation, IStratisLocationOptions>(StratisLocation);
export default StratisLocationFactory;
