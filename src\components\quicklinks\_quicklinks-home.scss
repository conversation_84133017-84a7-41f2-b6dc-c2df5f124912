.quicklinks-home {
    $this: &;

    &.section {
        margin-top: 10px;

        + #{$this} {
            margin-top: -114px;
            position: relative;

            @include breakpoint(medium down) {
                margin-top: -94px;
            }

            @include breakpoint(small down) {
                margin-top: -43px;
            }
        }

        .home-hospital-page & {
            margin: 0 0 150px;

            @include breakpoint(medium down) {
                margin: 0 0 100px;
            }

            @include breakpoint(small down) {
                margin: 0 0 80px;
            }

            .quicklinks {
                padding-bottom: 0;
                padding-top: 0;
            }
        }
    }

    &__container {
        @extend %container;

        @include breakpoint(small down) {
            max-width: 520px;
        }
    }

    .section__content {
        display: flex;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    .quicklinks {
        background-color: var(--color-1--2);
        padding: 60px 90px;
        width: 100%;

        @include breakpoint(medium down) {
            padding: 45px 30px;
        }

        @include breakpoint(small down) {
            padding: 19px 30px;
        }
    }

    .quicklinks-block {
        &__control {
            @include breakpoint(medium only) {
                font-size: 1.6rem;
            }
        }
    }

    .quicklink-item {
        &__svg-wrapper {
            @include breakpoint(medium only) {
                @include size(50px);
            }
        }

        &__text {
            color: var(--color-1--2);

            @include breakpoint(medium only) {
                font-size: 1.1rem;
                line-height: 1;
            }

            .underline {
                @include multiline-underline($color: var(--color-1--2));
            }
        }
    }

    &.is-fluid {
        @include breakpoint(medium) {
            background: linear-gradient(90deg, var(--color-1--2) 0%, var(--color-1--2) 50%, var(--color-1--1) 50%, var(--color-1--1) 100%);
        }

        .quicklinks-home__container {
            @include breakpoint(small down) {
                max-width: initial;
            }
        }

        .section__content {
            @include breakpoint(small down) {
                margin-left: -20px;
                margin-right: -20px;
            }
        }

        .quicklinks {
            box-shadow: none;
        }
    }

    &.has-info-block {
        .quicklinks {
            width: calc(100% - 384px);

            @include breakpoint(medium down) {
                width: calc(100% - 256px);
            }

            @include breakpoint(small down) {
                width: 100%;
            }
        }
    }

    &.is-light {
        .quicklinks {
            background-color: $color-white;
        }
    }

    &.has-box-shadow,
    .has-box-shadow {
        box-shadow: 0 0 50px rgba($color-black, 0.16);
    }
}

.hero {
    &.is-type-1 {
        + .quicklinks-home {
            margin-top: -114px;
            position: relative;

            @include breakpoint(medium down) {
                margin-top: -94px;
            }

            @include breakpoint(small down) {
                margin-top: -43px;
            }
        }
    }
}
