{%- from 'components/share/share.njk' import Share -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/utils/utils.njk' import setAttr -%}
{%- from 'views/core-components/list.njk' import List -%}
{% from Helpers.constants import kGlobalLinks %}
{%- from 'views/core-components/infos.njk' import InfoBlock, InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/link.njk' import Link -%}

{#
    GuidesAidesItem template.
    @param {object} settings - user settings object
#}
{% set defaultSettingsItem = {
    title: lorem(1),
    teaser: lorem(2),
    category: lorem(range(1, 3) | random, 'words'),
    tag: 'h3',
    role: false,
    ariaLevel: false,
    objectif : true,
    type : true,
    beneficiaire : true,
    calendrier : true,
    moreButton : true
} %}

{% macro GuidesAidesItem(settings = {}) %}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}
    <article class="guides-aides-item">
        {% if params.category %}
            <p class="guides-aides-item__category">{{ params.category }}</p>
        {% endif %}
        <div class="guides-aides-item__content">
            {% if params.title %}
            <{{ params.tag }} class="item-title guides-aides-item__title"
                {{ setAttr('role', params.role) }}
                {{ setAttr('aria-level', params.ariaLevel) }}>
                <a href="{{ kGlobalLinks.singleGuidesAides }}" class="guides-aides-item__title-link">
                    <span class="underline">{{ params.title }}</span>
                </a>
            </{{ params.tag }}>
        {% endif %}
        {% if params.objectif %}
            <div class="guides-aides-item__actions">
                {{ InfoItem(type = 'objectif') }}

            </div>
        {% endif %}
        {% if params.type %}
            <div class="guides-aides-item__actions">
                {{ InfoItem(type = 'type-aide') }}

            </div>
        {% endif %}
        {% if params.beneficiaire %}
            <div class="guides-aides-item__actions">
                {{ InfoItem(type = 'bénéficiaire') }}

            </div>
        {% endif %}
        {% if params.calendrier %}
            <div class="guides-aides-item__actions">
                {{ InfoItem(type = 'calendrier-aide') }}

            </div>
        {% endif %}
       
        {% if params.teaser %}
            <p class="item-teaser guides-aides-item__teaser">{{ params.teaser }}</p>
        {% endif %}
        {% if params.moreButton %}
            <div class="section__more-links">
                {{ Link(
                    href = kGlobalLinks.listNews,
                    text = 'EN SAVOIR PLUS',
                    className = 'btn is-link is-small',
                    icon = 'far fa-plus'
                ) }}
            </div>
        {% endif %}
        </div>
    </article>
{% endmacro %}

{#
    GuidesAidesList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
#}
{%- macro GuidesAidesList(
    itemClass = 'has-mb-6',
    cols = 3,
    mdCols = 2,
    smCols = 1,
    xsCols = 1,
    count = 10
    ) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ GuidesAidesItem() }}
    {% endcall %}
{%- endmacro -%}
