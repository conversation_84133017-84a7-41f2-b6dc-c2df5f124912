.map-search-toggle {
    $this: &;

    @include trs;
    @include font(var(--typo-1), 1.2rem, var(--fw-light));
    align-items: flex-start;
    background: none;
    border: 0;
    color: $color-black;
    cursor: pointer;
    display: flex;
    margin-left: auto;
    margin-right: 5px;
    padding: 6px;

    @include on-event {
        background-color: var(--color-1--1) !important;
        color: $color-white;

        #{$this}__icon {
            color: $color-white;
        }
    }

    &__icon {
        @include trs;
        color: var(--color-1--1);
        margin-left: 5px;

        @include icon-after($fa-var-eye);

        .is-active & {
            @include icon-after($fa-var-eye-slash);
        }
    }
}

.map-search-results {
    $this: &;

    &__list[class] {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    &__item {
        border-bottom: 1px solid $color-3--3;
        display: flex;
        flex-direction: column;
        margin-top: -1px;
    }

    &__link {
        margin-left: auto;
    }
}

.map-search-result {
    $this: &;

    @include trs;
    background: none;
    border: 0;
    cursor: pointer;
    padding: 21px 22px;
    width: 100%;

    &:hover,
    &:focus,
    &:active,
    &.is-active {
        background-color: var(--color-2--3);
        border-bottom-color: transparent;

        #{$this}__title {
            text-decoration: underline;
        }

        #{$this}__category {
            color: $color-black;
        }
    }

    &__wrap {
        @include trs;
        align-items: flex-start;

        display: flex;
    }

    &__image {
        @include size(30px, 37px);
        margin-left: 5px;

        svg {
            @include size(100%);
        }
    }

    &__content {
        color: $color-black;
        flex-grow: 1;
        width: 1%;
    }

    &__title,
    &__category {
        display: block;
        text-align: left;
    }

    &__category {
        @include font(var(--typo-1), 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        letter-spacing: 2.16px;
        margin-bottom: 5px;
        text-transform: uppercase;
    }

    &__title {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        line-height: 2.2rem;
    }
}
