.interest-results {
    $this: &;

    &__container {
        margin-bottom: 60px;

        .section {
            &__more-links {
                justify-content: flex-start;
                padding-left: 51px;

                @include breakpoint(small down) {
                    justify-content: center !important;
                    padding-left: 0 !important;
                }
            }
        }
    }

    &__content {
        background-color: $color-3--1;
        padding: 40px 30px 35px;
    }

    &__title {
        font-size: 2.4rem;
        font-weight: var(--fw-bold);
        line-height: calc(28 / 24);
        margin: 0 0 22px;

        @include breakpoint(medium down, true) {
            font-size: 2.2rem;
            line-height: calc(24 / 22);
        }

        @include breakpoint(medium down) {
            margin-bottom: 45px;
        }

        @include breakpoint(small down, true) {
            font-size: 2rem;
            margin-bottom: 30px;
            text-align: center;
        }
    }

    &__message {
        font-size: 1.8rem;
        margin: 10px 0;
    }

    &__number {
        color: $color-3--4;
        font-size: 2.2rem;
        font-weight: var(--fw-normal);
        line-height: calc(26 / 22);
        margin: 0 0 25px;

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__chart {
        display: flex;
        flex-wrap: wrap;
        margin: 20px 0 0;

        @include breakpoint(small down, true) {
            justify-content: center;
        }

        &.is-hidden {
            max-height: 0;
            overflow: hidden;
        }
    }

    &__canvas {
        @include size(180px);
        flex-shrink: 0;
        margin: 0 30px 20px 0;
        position: relative;
        z-index: 0;

        .is-width-66 & {
            @include breakpoint(large only) {
                @include size(140px);
            }
        }

        &::after {
            @include size(100%);
            @include absolute(null, null, 15px, -6px);
            border-radius: 50%;
            box-shadow: 0 200px 92px 23px rgba($color-black, 0.2);
            content: "";
            transform: scaleY(0.1) scaleX(0.6) skew(6deg);
            transform-origin: center bottom;
            z-index: -1;

            @include breakpoint(small down) {
                left: calc(50% - 6px);
                margin-left: -90px;
            }
        }
    }

    &__chart-data {
        flex-grow: 1;

        @include breakpoint(medium down) {
            margin-bottom: 30px;
        }
    }

    &.is-width-33 {
        .section {
            &__more-links {
                justify-content: center;
                padding-left: 0;

                @include breakpoint(medium down) {
                    justify-content: flex-start !important;
                    margin-left: 0 !important;
                    padding-left: 51px !important;
                }

                @include breakpoint(small down) {
                    justify-content: center !important;
                    padding-left: 0 !important;
                }
            }
        }

        #{$this}__number {
            font-size: 2rem;
        }
    }
}
