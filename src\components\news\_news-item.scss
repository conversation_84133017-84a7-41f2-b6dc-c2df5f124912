.news-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;

    &::after {
        @include breakpoint(large only) {
            @include size(201px);
            @include absolute(6px, null, null, 8px);
            background-color: transparent;
            border-radius: 50%;
            content: "";
            -webkit-mask-image: url(../images/Mask-Image-Evts.svg);
            mask-image: url(../images/Mask-Image-Evts.svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
            transition: all 0.3s;
            z-index: -2;

            .is-width-33 & {
                left: 88px;
            }
        }
    }

    @include on-event() {
        &::after {
            @include size(221px, 215px);
            background-color: var(--color-1--1);
            content: "";
            left: 17px;
            top: 0;
            z-index: 0;

            .is-width-33 & {
                left: 73px;
            }

            @include breakpoint(medium down) {
                content: none;
            }
        }

        #{$this}__image {
            z-index: 1;

            img {
                transform: translateZ(1px);
            }
        }
    }

    @include breakpoint(medium down) {
        align-items: flex-start;
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        padding-left: 63px;
    }

    @include breakpoint(small down) {
        padding-left: 0;
    }

    &__image {
        background-color: transparent;
        border-radius: 50%;
        display: block;
        position: relative;
        width: 221px;
        z-index: 1;

        .news-list__item:nth-child(3n-1) & {
            @include breakpoint(large only) {
                .home-page & {
                    width: 221px;
                }
            }
        }

        @include breakpoint(large only) {
            border-radius: 0;
            -webkit-mask-image: image("Mask-Image-Evts.svg");
            mask-image: image("Mask-Image-Evts.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
        }

        .home-page & {
            width: 221px;

            @include breakpoint(large only) {
                border-radius: 0;
                -webkit-mask-image: image("Mask-Image-Evts.svg");
                mask-image: image("Mask-Image-Evts.svg");
                -webkit-mask-repeat: no-repeat;
                mask-repeat: no-repeat;
                -webkit-mask-size: contain;
                mask-size: contain;
            }

            @include breakpoint(medium down) {
                max-width: 175px;
            }

            @include breakpoint(small down) {
                max-width: 127px;
            }

            img {
                @include breakpoint(large only) {
                    border-radius: 0;
                    -webkit-mask-image: image("Mask-Image-Evts.svg");
                    mask-image: image("Mask-Image-Evts.svg");
                    -webkit-mask-repeat: no-repeat;
                    mask-repeat: no-repeat;
                    -webkit-mask-size: contain;
                    mask-size: contain;
                }
            }
        }

        @include breakpoint(medium down) {
            border-radius: 0;
            flex-shrink: 0;
            -webkit-mask-image: image("mask-image-actus-Mobile.svg");
            mask-image: image("mask-image-actus-Mobile.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
            max-width: 197px;
        }

        @include breakpoint(small down) {
            max-width: 31.5%;
            width: 127px;
        }

        img {
            @include object-fit();
            @include size(auto, 100%);
            border-radius: 50%;

            @include breakpoint(large only) {
                border-radius: 0;
                -webkit-mask-image: image("Mask-Image-Evts.svg");
                mask-image: image("Mask-Image-Evts.svg");
                -webkit-mask-repeat: no-repeat;
                mask-repeat: no-repeat;
                -webkit-mask-size: contain;
                mask-size: contain;
            }

            @include breakpoint(medium down) {
                border-radius: 0;
                mask-image: none;
            }

            @include breakpoint(small down) {
                height: auto;
            }
        }
    }

    &__content {
        @include line-decor(25px, 4px, "after");
        background-color: $color-white;
        padding: 21px 35px 25px 0;

        .news-home & {
            background-color: transparent;

            @include breakpoint(medium down) {
                padding-top: 38px;
            }

            @include breakpoint(small down) {
                padding-top: 20px;
            }
        }

        @include breakpoint(medium down) {
            flex-grow: 1;
            margin: 0 0 0 -17px;
            max-width: calc(100% - 266px);
            padding: 48px 30px 0;
        }

        @include breakpoint(small down) {
            margin-left: -15px;
            max-width: 68.5%;
            padding: 17px 0 0 11px;
        }

        &::after {
            margin-top: 13px;
            position: absolute;
            transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);

            @include breakpoint(small down) {
                margin-top: 7px;
            }
        }

        > *:last-child {
            margin-bottom: 0;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
        letter-spacing: 0;
        line-height: 28px;
    }

    &__category {
        font-weight: var(--fw-bold);
        margin-bottom: 5px;

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }
    }

    &__commune {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
        margin-bottom: 13px;

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }
    }

    &__publish {
        @include font(var(--typo-1), 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-bottom: 5px;
        margin-top: 10px;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            font-size: 1.1rem;
            margin-top: 5px;
        }
    }

    &__update {
        @include font(var(--typo-1), 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-bottom: 5px;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            font-size: 1.1rem;
            margin-bottom: 0;
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }

    .is-sitefactory & {
        margin: 0 0 40px;

        &__category {
            background-color: var(--color-1--1);
        }

        @include breakpoint(small down) {
            margin-top: 40px;
        }
    }

    // Widget 33%
    .is-width-33 & {
        align-items: center;

        @include breakpoint(small down) {
            align-items: flex-start;
        }

        #{$this}__content {
            @include breakpoint(large) {
                margin-top: -30px;
                max-width: 293px;
                padding: 22px 35px 25px 30px;
                text-align: center;

                &::after {
                    left: calc(50% - 25px);
                    transform: matrix(0.99, 0.12, -0.12, 0.99, 0, 0);
                }
            }
        }
    }

    // Widget 100% on page content and home
    .news-home &,
    .news-content & {
        #{$this}__content {
            @include breakpoint(large) {
                margin-top: -30px;
                max-width: 306px;
                padding: 21px 0 0 15px;
            }
        }
    }
}
