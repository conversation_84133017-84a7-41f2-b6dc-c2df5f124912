{%- macro PasswordVisibilityManager(targetSelector = "#identical-field-password:global") -%}
    <button type="button"
            class="password-visibility-manager js-password-visibility-manager js-tooltip"
            data-target-input="{{ targetSelector }}"
            data-content="Afficher le mot de passe"
            data-toggle-content="Masquer le mot de passe"
    >
        <span aria-hidden="true"
              class="fas fa-eye js-icon password-visibility-manager__icon"
              data-toggled-icon-current="fa-eye"
              data-toggled-icon="fa-eye-slash"></span>
        <span class="sr-only">Afficher le mot de passe</span>
    </button>
{%- endmacro -%}
