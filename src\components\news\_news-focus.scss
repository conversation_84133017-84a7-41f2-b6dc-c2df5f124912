.news-focus {
    $this: &;
    margin-bottom: 80px;
    position: relative;

    &::before {
        @include breakpoint(large only) {
            @include size(100vw, 610px);
            @include absolute(0, 0, 0, -80px);
            background-color: var(--color-1--5);
            border-radius: 20px;
            content: "";
            transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
            z-index: -1;
        }
    }

    @include breakpoint(medium down) {
        margin-bottom: 65px;
    }

    @include breakpoint(small down) {
        margin-bottom: 30px;
    }

    &__wrapper {
        @extend %link-block-context;
        align-items: flex-start;
        display: flex;
        justify-content: flex-end;

        @include breakpoint(medium down) {
            flex-direction: column-reverse;
        }
    }

    &__picture-link {
        margin-top: -60px;
        position: relative;

        @include breakpoint(medium down) {
            margin: 47px auto 0;
        }

        @include breakpoint(small down) {
            margin-top: 24px;
        }

        &::before {
            content: "";
            z-index: 0;
            @include size(100%, 89%);
            @include absolute(94px, null, 0, 174px);
            background: {
                image: inline-svg(
                    '<svg xmlns="http://www.w3.org/2000/svg" width="546.103" height="348.524" viewBox="0 0 546.103 348.524"> <rect id="fond-actu-focus-desktop" width="518" height="296" rx="20" transform="translate(30.94) rotate(6)" fill="#164496"/> </svg>'
                );
                repeat: no-repeat;
                size: contain;
            }

            @include breakpoint(medium down) {
                @include size(644px, 375px);
                @include absolute(41px, null, 0, 189px);
                background-image: inline-svg('<svg xmlns="http://www.w3.org/2000/svg" width="538.963" height="399.128" viewBox="0 0 538.963 399.128"> <rect id="fond-actu-focus-tablette" width="505.333" height="348.214" rx="10" transform="translate(36.398) rotate(6)" fill="#164496"/> </svg>');
            }

            @include breakpoint(small down) {
                @include size(333px, 208px);
                @include absolute(0, null, 0, 83px);
                background-image: inline-svg('<svg xmlns="http://www.w3.org/2000/svg" width="269.98" height="213.182" viewBox="0 0 269.98 213.182"> <rect id="fond-actu-focus-mobile" width="251.718" height="187.899" rx="10" transform="translate(19.641) rotate(6)" fill="#164496"/> </svg>');
            }
        }

        &::after {
            content: "";
            z-index: 1;
            @include size(53px, 100px);
            @include absolute(calc(50% - 12px), null, 0, -53px);
            background: {
                image: inline-svg(
                    '<svg xmlns="http://www.w3.org/2000/svg" width="101.756" height="113.451" viewBox="0 0 101.756 113.451"> <path id="mask-image-actus-Mobile" d="M22151.371,20828.758v0h-69.289a56.912,56.912,0,0,1-20.926-56.412,56.117,56.117,0,0,1,3.3-10.662,56.912,56.912,0,0,1,30.188-30.219,56.04,56.04,0,0,1,10.648-3.3,57.089,57.089,0,0,1,22.863,0,56.3,56.3,0,0,1,20.281,8.541,56.837,56.837,0,0,1,2.93,92.057Z" transform="translate(-20727.002 22173.453) rotate(-90)" fill="#33ccff"/> </svg>'
                );
                repeat: no-repeat;
                size: cover;
            }

            @include breakpoint(medium down) {
                @include size(70px);
                @include absolute(auto, null, -35px, 42px);
                background-size: contain;
                rotate: 270deg;
                z-index: -1;
            }

            @include breakpoint(small down) {
                @include size(23px, 45px);
                @include absolute(null, null, -34px, 30px);
                background-size: cover;
            }
        }
    }

    &__picture {
        border-radius: 10px;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);
            border-radius: 10px;
        }
    }

    &__content {
        margin-right: 0;
        max-width: 485px;
        min-height: 340px;
        padding: 111px 79px 45px 0;
        width: 100%;
        z-index: 1;

        @include breakpoint(medium down) {
            margin: 0 auto;
            max-width: 644px;
            min-height: auto;
            padding: 80px 55px 45px 0;
        }

        @include breakpoint(small down) {
            margin: 0;
            max-width: 100%;
            padding: 43px 0 20px;
        }
    }

    &__title {
        font-size: 3.5rem !important;
        line-height: 40px;
        text-align: right;

        @include breakpoint(medium down) {
            font-size: 3.1rem !important;
            line-height: 32px;
            text-align: left;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem !important;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        margin-bottom: 10px;

        &.theme {
            @include font(null, 2.2rem, var(--fw-bold));
            line-height: 27px;

            @include breakpoint(small down) {
                line-height: 17px;
            }
        }

        @include breakpoint(medium down) {
            font-size: 1.8rem !important;
            line-height: 22px;
        }

        @include breakpoint(small down) {
            font-size: 1.4rem !important;
            margin-bottom: 10px;
        }
    }

    + .news-list {
        position: relative;

        &::before {
            @include breakpoint(medium down) {
                @include size(calc(100% + 70px), calc(100% + 31px));
                @include absolute(-31px, 0, 0, 0);
                background-color: var(--color-1--5);
                border-radius: 20px;
                content: "";
                transform: matrix(-1, -0.02, 0.02, -1, 0, 0);
                z-index: -1;
            }

            @include breakpoint(small down) {
                height: 100%;
            }
        }
    }
}
