.travaux-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    background-color: $color-3--1;
    border-radius: 10px;
    display: flex;
    padding: 0;
    width: 100%;

    .travaux-home & {
        background-color: transparent;
        border-radius: 0;
        display: block;
        padding: 0;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    .is-single-travaux & {
        flex-direction: column;

        &__content-info {
            flex-direction: column;
        }

        &__picture {
            margin-bottom: 20px;

            @include breakpoint(small down) {
                margin: 0 0 20px;
            }
        }

        &__content {
            @include breakpoint(small down) {
                text-align: left;
            }
        }

        &__infos {
            @include breakpoint(small down) {
                align-items: start;
            }
        }

        @include breakpoint(small down) {
            align-items: flex-start;
        }
    }

    &__picture {
        @include size(384px, 257px);
        border-radius: 0 0 10px 10px;
        display: block;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(200px);
        }

        img {
            @include size(384px, 257px);
            border-radius: 0 0 10px 10px;

            @include breakpoint(medium down) {
                @include size(200px);
            }
        }
    }

    &__content {
        flex-grow: 1;
        min-width: 1%;
        padding: 35px 142px 35px 86px;

        .travaux-home & {
            padding: 0;
            
            @include breakpoint(small down) {
                text-align: left;
            }
        }

        @include breakpoint(medium down) {
            padding: 35px 25px;
        }

        @include breakpoint(small down) {
            max-width: 480px;
            padding: 35px 25px;
            text-align: center;
        }
    }

    &__theme {
        @include font(var(--typo-1), 1.8rem, var(--fw-medium));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 3.24px;
        margin: 0 0 17px;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            letter-spacing: 2.52px;
            margin-bottom: 13px;
        }

        @include breakpoint(small down) {
            font-size: 1.2rem;
            letter-spacing: 2.16px;
        }

        .travaux-home & {
            font-size: 1.4rem;
            letter-spacing: 2.52px;
            margin-bottom: 13px;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        color: $color-black;
        line-height: calc(25 / 22);
        margin-bottom: 15px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: calc(24 / 22);
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
        }

        .travaux-home & {
            font-size: 2.4rem;
            line-height: calc(28 / 24);
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        .underline {
            @include multiline-underline(2px, $color-black);
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(19 / 16);
        margin-bottom: 13px;
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(22 / 16);
        margin: 0 0 6px;

        &.is-location {
            color: $color-3--5;
            margin-bottom: 13px;
        }

        &.is-main {
            color: var(--color-1--1);
            font-size: 1.8rem;
            font-weight: var(--fw-bold);
            line-height: calc(22 / 18);
        }

        @include breakpoint(small down) {
            margin-bottom: 2px;
        }
    }

    &__category {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
        background-color: var(--color-1--1);
        border-radius: 10px 10px 0 0;
        color: $color-white;
        display: block;
        letter-spacing: 2.52px;
        line-height: 1.2;
        padding: 20px 35px;
        text-transform: uppercase;

        .travaux-home & {
            background-color: transparent;
            border-radius: 0;
            color: var(--color-1--1);
            margin-bottom: 13px;
            padding: 0;
        }
    }

    &__content-info {
        display: flex;
        justify-content: space-between;
        margin-top: 22px;

        @include breakpoint(medium down) {
            flex-direction: column;
            margin-top: 15px;
        }

        @include breakpoint(small down) {
            margin-top: 22px;
            padding: 0 5px;
        }
    }

    &__details,
    &__infos {
        .infos {
            >*:last-child {
                margin-bottom: 0;
            }

            a {
                display: inline-flex;

                @include fa-icon-style(false) {
                    left: -33px;
                }
            }
        }

        a {
            border-radius: 40px;
            position: relative;
            z-index: 42;
        }
    }

    &__details {
        padding-right: 15px;

        @include breakpoint(medium down) {
            margin-bottom: 10px;
            padding: 0;
        }
    }

    &__infos {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        width: 164px;

        @include breakpoint(small down) {
            align-items: center;
            width: 100%;
        }

        .btn {
            min-width: 179px;
        }
    }

    &__infos-item {
        &:not(:last-child) {
            margin-bottom: 5px;
        }
    }

    .is-width-66 & {
        @include breakpoint(large) {
            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 2.52px;
                margin-bottom: 13px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2.2rem;
                line-height: calc(24 / 22);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 30px;
            }

            #{$this}__details {
                margin-bottom: 28px;
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            align-items: center;
            flex-direction: column;

            #{$this}__picture {
                margin: 0 auto 20px;
            }

            #{$this}__content {
                max-width: 480px;
                text-align: center;
            }

            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 2.52px;
                margin-bottom: 13px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2rem;
                line-height: calc(24 / 20);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 30px;
            }

            #{$this}__details {
                margin-bottom: 28px;
            }

            #{$this}__infos {
                align-items: center;
                width: 100%;
            }
        }
    }
}
