{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitlePrimary, TitleDefault, TitleWidget -%}
{%- from 'views/core-components/list.njk' import List -%}
{% from 'views/core-components/carousel.njk' import CarouselWrapper %}
{%- import 'views/core-components/secondary.njk' as Secondary -%}
{%- from 'views/utils/constants.njk' import kGlobalLinks -%}

{#
    DeliberationsHomeItem template.
    @param {object} settings - publication item settings.
#}
{%- macro DeliberationsHomeItem(
    link = 'single-deliberations.html',
    imageSize = ['282x400'],
    category = 'Lorem ipsum',
    title = 'Magazine de la ville',
    subtitle = 'Septembre 2099 - N°999',
    teaser = '160 caractères orem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumyre eirmod consetetur elitr sed diam nonumy eirmod tempor invidunt laboreet',
    documents = 1
) -%}
    <article class="deliberations-home-item">
        {{ Image({
            className: 'deliberations-home-item__image',
            sizes: imageSize,
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(100) | random
        }) }}
        <div class="deliberations-home-item__content-wrapper">
            <div class="deliberations-home-item__content">
                <h3 class="item-title is-large deliberations-home-item__title">
                    {% if category %}
                        <span class="theme is-large deliberations-home-item__category">{{ category }}</span>
                        <span class="sr-only">:</span>
                    {% endif %}
                    <a href="./{{ link }}" class="deliberations-home-item__title-link">
                        <span class="underline">{{ title }}</span>
                    </a>
                    {%- if subtitle -%}
                        <span class="deliberations-home-item__subtitle">{{ subtitle }}</span>
                    {%- endif -%}
                </h3>
                {%- if documents !== 1 -%}
                    <p class="publication is-primary deliberations-home-item__publication">
                        <span class="publication__number">{{ documents }} documents</span>
                        <span>Publié le </span>
                        <time datetime="2018-03-28">28/03/2018</time>
                    </p>
                {%- endif -%}
            </div>
            <div class="deliberations-content-item__bottom-wrapper">
                {%- if teaser -%}
                    <p class="item-teaser deliberations-home-item__teaser">{{ teaser }}</p>
                {%- endif -%}
                {%- if documents === 1 -%}
                    <div class="deliberations-home-item__actions">
                        {{ Secondary.DocumentActions() }}
                    </div>
                {% endif %}
            </div>
        </div>
    </article>
{%- endmacro -%}

{#
    DeliberationsHome template.
    Template for events on home page.
    @param {string} titleText - section title
    @param {boolean} moreButton - insert more link
#}
{% macro DeliberationsHome(
    className = 'deliberations-home',
    titleText = 'En kiosque',
    moreButton = true
) %}
    {% call Section(className = className, container = 'deliberations-home__container') %}
        <div class="section__title">
            {{ TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ DeliberationsHomeItem() }}
        </div>
        {% if moreButton %}
            <div class="section__more-links">
                {{ Link(
                    href = kGlobalLinks.listDeliberations,
                    text = 'Toutes les deliberations',
                    className = 'btn is-sm-small is-primary',
                    icon = false
                ) }}
            </div>
        {% endif %}
    {% endcall %}
{% endmacro %}
