{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/core-components/image.njk' import Image %}

{%- macro BannerItem(
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5',
    title = 'Découvrez toute la programmation culturelle!',
    imageSizes = ['320x135?479', '600x250'],
    displayText = true
    ) -%}
    <div class="banner-item {{ 'is-reverse' if isReverse }} {{ 'is-inverted' if isInverted }}" style="background-color: {{ bgColor }};">
        {% if displayText %}
            <div class="banner-item__content">
                <h2 class="banner-item__title">
                    <a href="#" class="banner-item__title-link">
                        <span class="underline">{{ title }}</span>
                    </a>
                </h2>
                {{ Icon('fas fa-long-arrow-right') }}
            </div>
        {% endif %}

        <a href="#" class="banner-item__image-container">
            {{ Image({
                className: "banner-item__image" +' full-image' if displayText == false,
                sizes: imageSizes,
                serviceID: range(100) | random,
                alt: 'image alt'
            }) }}
        </a>
    </div>
{%- endmacro -%}

{%- macro BannerHome(
    modifier = '',
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5',
    displayText = true,
    titleSection = false,
    imageSizes = ['320x270?767','648x135?1279', '1200x250']
    ) -%}
    {% call Section(className = 'banner-home', modifier = modifier, container = 'banner-home__container') %}
    {% if titleSection %}
        <div class="section__title{{ ' ghost' if titleIsHidden }}">
            {{ Title.TitlePrimary(
                text = titleSection
            ) }}
        </div>
    {% endif %}
    <div class="section__content banner-home__content">
        {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor,
                displayText = displayText,
                imageSizes = imageSizes
            ) }}
    </div>
    {% endcall %}
{%- endmacro -%}

{%- macro BannerContent(
    className = 'banner-content',
    isReverse = false,
    isInverted = false,
    bgColor = '#c5c5c5',
    imageSizes = ['320x135?479', '800x450?1279', '1200x450'],
    displayText = false
    ) -%}
    {% call Section(className = className, container = false) %}
    <div class="section__content">
        {{ BannerItem(
                isReverse = isReverse,
                isInverted = isInverted,
                bgColor = bgColor,
                imageSizes = imageSizes,
                displayText = displayText
            ) }}
    </div>
    {% endcall %}
{%- endmacro -%}