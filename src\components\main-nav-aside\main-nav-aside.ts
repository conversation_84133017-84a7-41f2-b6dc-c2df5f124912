import { key } from 'ally.js/when/_when';
import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import OnInit from '@core/decorators/init-method.decorator';
import { Dropdown, IDropdownOptions } from '@core/core-components/dropdown';
import { Toggle } from '@core/core-components/toggle';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import { extendDefaults } from '@core/utils/object.utils';

export interface IMegamenuOptions extends IGlobalOptions<Megamenu> {
    overrideDefaults?: boolean;
    containerDropdown?: Partial<IDropdownOptions>;
}

export class Megamenu extends StratisElementAbstract {
    protected options: IMegamenuOptions = {
        classList: {
            menuItem: 'main-nav__nav-item',
            menuItemLink: 'main-nav__nav-link',
            submenuToggle: 'js-main-nav-ddm-toggle',
            submenuDropdown: 'js-main-nav-ddm-block',
            bodyOpenedHelper: 'mnv-opened',
            currentLinkHelper: 'is-active',
        },
        DOMElements: {
            container: '.js-main-nav-container',
            submenus: '.js-main-nav-ddm:array',
            firstLevelItems: '.main-nav__nav-list > .main-nav__nav-item:array',
        },
        dataset: {},
        overrideDefaults: false,
        containerDropdown: {
            toggle: {
                class: 'main-nav-toggle',
                outsideID: 'main-nav-toggle',
                ignoreAttrs: true,
            },
            dropdown: {
                class: 'js-main-nav-block',
            },
            closeButton: {
                enabled: true,
                class: 'js-main-nav-close',
                ignoreAttrs: true,
            },
            overlay: {
                enabled: true,
                outsideID: 'js-main-nav-overlay',
            },
            interactions: {
                enableFocusTrap: true,
                enableKeyHandler: true,
            },
            ignoreResponsiveEventOnLoad: true,
        },
    };

    private menuDropdown: Dropdown | null = null;
    private menuSubmenus: Toggle[] | null = null;
    private lastClickedLink: Element | null = null;

    constructor(selector: HTMLElement, options?: Partial<IMegamenuOptions>) {
        super(selector, options || {});

        if (!this.created) {
            this.init();
        }
    }

    public open(): void {
        this.menuDropdown!.open();
    }

    public close(): void {
        this.menuDropdown!.close();
    }

    /**
     * Set a11y for current link.
     */
    public setCurrentLinkAccessibility(): void {
        const currentLink = this.getCurrentLink();
        const { container } = this.options.DOMElements;

        if (currentLink) {
            let parentEl = currentLink.parentElement;

            currentLink.setAttribute('aria-current', 'page');

            while (parentEl && parentEl !== container) {
                if (hasClass(parentEl, 'main-nav__nav-item')) {
                    const link = parentEl.querySelector('.main-nav__nav-link');

                    if (link && link !== currentLink) {
                        link.setAttribute('aria-current', 'true');
                    }

                    parentEl.setAttribute('data-has-current', 'true');
                    addClass(parentEl, 'is-open');
                }

                parentEl = parentEl.parentElement;
            }
        }
    }

    public menuFirstLevelItems(): HTMLElement[] {
        const { firstLevelItems } = this.options.DOMElements;
        return firstLevelItems as HTMLElement[];
    }

    /**
     * Get current link element.
     */
    private getCurrentLink(): HTMLElement | null {
        return this.element.querySelector('.is-active') || null;
    }

    /**
     * Init a11y for megamenu.
     */
    private initAccessibility(): void {
        this.setCurrentLinkAccessibility();

        this.$keyHandler = key({
            context: this.element,
            escape: () => this.close(),
        });
    }

    /**
     * Init submenus.
     */
    private initSubmenus(): void {
        const { submenus, firstLevelItems } = this.options.DOMElements;
        const { submenuToggle, submenuDropdown } = this.options.classList;

        if (Array.isArray(submenus) && submenus.length) {
            this.menuSubmenus = submenus.map(submenu => new Toggle(submenu, {
                DOMElements: {
                    toggleBtn: `.${submenuToggle}`,
                    dropdownBlock: `.${submenuDropdown}`,
                },
                beforeOpen: instance => {
                    const clickedItem = instance.element;

                    const parentDropdown = clickedItem.closest('ul');

                    const isLevel1 = parentDropdown?.classList.contains('main-nav__nav-list');
                    const isLevel2 = parentDropdown?.classList.contains('is-level-1');

                    const openedSubmenus = this.menuSubmenus!.filter(({ element }) => {
                        if (element === clickedItem || !hasClass(element, 'is-open')) return false;

                        const elParentDropdown = element.closest('ul');

                        if (isLevel1 && elParentDropdown?.classList.contains('main-nav__nav-list')) return true;
                        if (isLevel2 && elParentDropdown?.classList.contains('is-level-1')) return true;

                        return false;
                    });
                    openedSubmenus.forEach(toggle => toggle.close());
                }
            }));
        }
    }

    /**
     * Init container dropdown.
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    private initContainerDropdown(): void {
        const { container } = this.options.DOMElements;
        const { bodyOpenedHelper } = this.options.classList;
        const { beforeOpen, afterClose } = this.options.containerDropdown || {};

        if (container) {
            const containerDropdownOptions = extendDefaults(this.options.containerDropdown || {}, {
                beforeOpen: (instance, instanceElement, BrowserEvent) => {
                    if (beforeOpen) {
                        beforeOpen(instance, instanceElement, BrowserEvent);
                    }

                    if (!this.options.overrideDefaults) {
                        addClass(document.body, bodyOpenedHelper);
                        document.documentElement.style.scrollbarWidth = "none";
                    }

                    this.lastClickedLink = document.activeElement;
                },
                afterClose: (instance, instanceElement, BrowserEvent) => {
                    if (afterClose) {
                        afterClose(instance, instanceElement, BrowserEvent);
                    }

                    if (!this.options.overrideDefaults) {
                        removeClass(document.body, bodyOpenedHelper);
                        document.documentElement.style.scrollbarWidth = "auto";
                    }

                    if (this.lastClickedLink && this.lastClickedLink instanceof HTMLElement) {
                        this.lastClickedLink.focus();
                    }
                },
            });

            this.menuDropdown = new Dropdown(container as HTMLElement, containerDropdownOptions);
        }
    }

    /**
     * Handle megamenu on init.
     */
    @OnInit()
    private initHandler(): void {
        this.initContainerDropdown();
        this.initSubmenus();
        this.initAccessibility();

        const { container } = this.options.DOMElements;

        (container as HTMLElement).addEventListener('click', ({ target }) => {
            if (target === container) {
                this.close();
            }
        });
    }
}

const MegamenuFactory = StratisFactoryMixin<typeof Megamenu, Megamenu, IMegamenuOptions>(Megamenu);
export default MegamenuFactory;
