{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/media-element/media-element.njk' import MediaElement -%}
{% from 'views/core-components/secondary.njk' import Date %}
{%- from 'components/social/social.njk' import SocialLinks -%}
{%- import 'views/core-components/secondary.njk' as Secondary -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'components/key-box/key-box.njk' import KeyBoxHomeBlock -%}
{%- from 'views/utils/utils.njk' import svg -%}

{#
    macro Editorial
#}
{%- macro Editorial(
        title = lorem(5, 'words'),
        text = lorem(2),
        learnMoreText = 'En savoir plus',
        backgroundColor = "#164496",
        date = Date()
    ) -%}

    <div class="newswall-editorial">
        <div class="newswall-editorial__before-bloc" style="background-color: {{ backgroundColor }};"></div>
        <div class="newswall-editorial__content">
            <div class="newswall-editorial__title">{{title}}</div>
            <div class="newswall-editorial__text">{{text}}</div>
            {{ Link(
                href = '#',
                text = learnMoreText,
                className = 'btn is-primary is-small newswall-editorial__link',
                icon = 'fa-light fa-arrow-right'
            ) }}
        </div>
        {% if date %}
            <div class="newswall-editorial__date">{{date}}</div>
        {% endif %}
    </div>
{%- endmacro -%}

{#
    macro NewsWall
#}
{%- macro NewsWall(
    icon = false,
    svgIcon = false,
    nameIcon = '',
    imageSizes = ['320x237?1279', '563x417'],
    text = lorem(1),
    itemName = "facebook",
    accountName = lorem(1, 'words'),
    accountId = lorem(1, 'words')
) -%}
    <div class="newswall-item is-{{itemName}}">
        {% if icon %}
            <a href="#" class="newswall-item__icon">
                {{ Icon(icon) }}
            </a>
        {% endif %}
        {% if svgIcon %}
            <a href="#" class="newswall-item__icon">
                {{ svg('icons/' + nameIcon, 60, 60) }}
            </a>
        {% endif %}
        {{ Image({
            className: 'newswall-item__image',
            sizes: imageSizes,
            serviceID: range(100) | random,
            alt: 'image alt'
        }) }}
        <div class="newswall-item__text">
            {{text}}
        </div>
        </div>
    {%- endmacro -%}

    {#
    macro NewswallHome
#}

    {%- macro NewswallHome(
    items = [
        {itemName : "editorial",   icon: false, backgroundColor: "#164496", title : "Recrutement de psychologues", text : "Le Département de l'Essonne recrute des psychologues pour ses différentes structures. N’hésitez pas à consulter la page offres d’emploi."},
        {itemName : "facebook",    icon: "fab fa-facebook-f", text : "A Chevannes, Vanessa Charland camoufle des cicatrices, recrée des bulbes, des aréoles mammaires et des sourcils."},
        {itemName : "instagram",   icon: "fab fa-instagram", text : "#EssonneMagique✨🍂 On prend aujourd'hui la route de la D63 en direction de Mespuits pour profiter des jolies couleurs automnales."},
        {itemName : "editorial",   icon: false, backgroundColor: "#3cf", title : "Fermeture du pont de la RD167 ", text : "Les travaux de rénovation sur le pont de la RD 167 surplombant l'A6 et A10, sur la commune de Wissous, se poursuivent avec une nouvelle phase à partir du 23 octobre."},
        {itemName : "editorial",   icon: false, backgroundColor: "#164496", title : "Concours de fleurissement en Essonne", text : "Ce concours se décline en trois catégories : Villes et villages fleuris (VVF), Trophée fleur verte (TFV), et Prix des jardins naturels sensibles (Prix JNS)."},
        {itemName : "x",           icon: 'fa-brands fa-x-twitter', text : "Festival sport & nature VTT, randonnée, escalade, tir à l'arc, course d'orientation...on vous donne rendez-vous les 1er et 2avril au domaine départemental de #Chamarande,"},
        {itemName : "chiffre clé", icon: false},
        {itemName : "editorial",   icon: false, backgroundColor: "#3cf", title : "Portes ouvertes Sport & Science à l’université d’Evry", text : "Du 5 au 16 octobre, l’université d’Evry célèbre la Fête de la Science avec comme thème cette année « Sport & Science ». Un événement gratuit et ouvert à tous."},
        {itemName : "editorial",   icon: false, backgroundColor: "#164496", title : "Appel à projets Collèges 2023/2024", text : "Les établissements scolaires sont invités à soumettre leurs dossiers au Conseil départemental de l’Essonne. La date limite de dépôt des dossiers est fixée au 09 octobre 2023."},
        {itemName : "linkedin",    icon: "fab fa-linkedin-in", text : "🚌 Les Cars Express, une alternative à la voiture !✅ Les futurs Cars Express Régionaux amélioreront les déplacements des Franciliens, en particulier ceux de la Grande Couronne, et seront à même de concurrencer la voiture.➡️ Plus d'infos : https://lnkd.in/gEFXZndx"},
        {itemName : "youtube",     icon: "fab fa-youtube", text : "Titre Destination Essonne #9 : La forêt départementale de la Barre"},
        {itemName : "tiktok",      icon: "fab fa-tiktok", text : "🎮♻️ Moderis, le jeu vidéo immersif pour pieux comprendre la gestion des déchets. On s’est rendu au collège Mozart d’Athis-Mons pour tester cette innovation du #siredom !"}
    ]
) -%}
        {% call Section(className = 'newswall-home', container = 'newswall-home__container') %}
        <div class="section__content newswall-home__content">
            {% for item in items %}
                {% if item.itemName in "editorial" %}
                    {{ Editorial(backgroundColor=item.backgroundColor, title= item.title, text = item.text)}}
                {% elif item.itemName == "chiffre clé" %}
                    <a href="#" class="newswall-home__key-box">
                        {{ KeyBoxHomeBlock(icon = "icons/ico-En-Chiffres") }}
                    </a>
                {% else %}
                    {{ NewsWall(
                        item = item.itemName,
                        icon = item.icon,
                        itemName = item.itemName,
                        text = item.text,
                        svgIcon = item.svgIcon,
                        nameIcon = item.nameIcon
                    )}}
                {% endif %}
            {% endfor %}
        </div>
        {% endcall %}
    {%- endmacro -%}
