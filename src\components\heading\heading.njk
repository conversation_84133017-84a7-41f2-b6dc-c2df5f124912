{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- import 'views/core-components/secondary.njk' as Secondary -%}
{%- from 'views/utils/utils.njk' import setAttr, stratisImage -%}
{%- from 'components/tools/tools.njk' import Tools -%}
{%- from 'views/utils/utils.njk' import svg -%}


{#
    Insert heading links element.
    @param {boolean} useDefaults - use default content.
    @param {boolean} hasFilter - insert filter toggle button for mobile.
    @param {boolean} hasFacets - insert facets toggle button for mobile.
#}
{% macro HeadingLinks(useDefaults = true, hasFilter = false, hasFacets = false) %}
    <div class="heading__links">
        {% if useDefaults %}
            {#
                {{ Link(className = 'btn is-secondary', text = 'Accéder à la carte interactive', icon = 'fas fa-map-marker') }}
                {{ Link(className = 'btn is-secondary', text = 'Vue cartographique', icon = 'fas fa-map-marker') }}
                {{ Link(className = 'btn is-secondary', text = 'Proposer un événement', icon = 'fas fa-calendar-plus') }}
            #}
            {% call Link(className = 'btn is-secondary is-circle', icon = 'fas fa-rss', tooltip = 'Flux rss des equipements') %}
            <span class="ghost">Flux rss des equipements</span>
            {% endcall %}
        {% endif %}
        {{ caller() if caller }}
        {% if hasFilter %}
            <button type="button" class="heading-filter-toggle is-small is-sm-breakpoint js-tooltip" data-content="Filtrer la liste" data-sd-toggle="list-filter">
                {{ Icon(className = 'far fa-filter') }}
                <span class="ghost">Filtrer la liste</span>
            </button>
        {% endif %}
        {% if hasFacets %}
            <button type="button" class="heading-filter-toggle is-small js-facets-toggle js-tooltip" data-content="Afficher les facettes">
                {{ Icon(className = 'far fa-filter') }}
                <span class="ghost">Afficher les facettes</span>
            </button>
        {% endif %}
    </div>
{% endmacro %}

{#
    Insert heading partials.
    @param {string} type - partial type
    @param {string} text - text for partial
    @param {boolean} useDefaults - use default content.
#}
{% macro HeadingPartials(type = 'date', text = '', useDefaults = true, categoryText = false, subtitleText = false, langTitleAttr = false) %}
    {% set datetime = Helpers.date({
        format: ['y', 'm', 'd']
    }) %}
    {% if type === 'badge' %}
        <div class="heading__badge">
            <div class="event-badge">
                <div class="event-badge__Pictogramm">{{ svg('icons/leaf') }}</div>
                <div class="event-badge__title">RDV nature</div>
            </div>
            {{ caller() if caller }}
        </div>
    {% elif type === 'date' %}
        <div class="heading__date">
            {{ Secondary.Date() if useDefaults }}
            {{ caller() if caller }}
        </div>
    {% elif type === 'commune' %}
        <div class="heading__commune">
            {{lorem(range(1, 3) | random, 'words')}}
            {{ caller() if caller }}
        </div>
    {% elif type === 'time-place' %}
        <div class="heading__time-place">
            {{ Secondary.TimePlace() if useDefaults }}
            {{ caller() if caller }}
        </div>
    {% elif type === 'place' %}
        <div class="heading__time-place">
            {{ Secondary.TimePlace(time = false, placeLabel="Nom du Lieu") if useDefaults }}
            {{ caller() if caller }}
        </div>
    {% elif type === 'time' %}
        <div class="heading__time-place">
            {{ Secondary.TimePlace(place = false, timeSeparator= 'à') if useDefaults }}
            {{ caller() if caller }}
        </div>
    {% elif type === 'tariff' %}
        <div class="heading__tariff">
            {% if useDefaults %}
                {{ Secondary.Price('Tout public') }}
                {{ Secondary.Price() }}
            {% endif %}
            {{ caller() if caller }}
        </div>
    {% elif type === 'category' %}
        <p class="heading__category">
            {{ 'Category 1, Category 2' if useDefaults else text }}
            {{ caller() if caller }}
        </p>
    {% elif type === 'title' %}
        <h1 class="heading__title">
            <span class="heading__title-text" {{ 'lang=fr' if langTitleAttr }}>{{ 'Default title' if useDefaults else text }}</span>
            {{ caller() if caller }}
        </h1>
    {% elif type === 'titleWithCategory' %}
        <h1 class="heading__title">
            <span class="heading__category">{{ 'Category 1, Category 2' if useDefaults else categoryText }}</span>
            <span class="sr-only">
                :
            </span>
            <span class="heading__title-text" {{ 'lang=fr' if langTitleAttr }}>{{ 'Default title' if useDefaults else text }}</span>
            {{ caller() if caller }}
        </h1>
    {% elif type === 'titleWithCategoryAndSubtitle' %}
        <h1 class="heading__title">
            <span class="heading__category">{{ 'Category 1, Category 2' if useDefaults else categoryText }}</span>
            <span class="sr-only">
                :
            </span>
            <span class="heading__title-text" {{ 'lang=fr' if langTitleAttr }}>{{ 'Default title' if useDefaults else text }}</span>
            <span class="heading__subtitle">{{ 'Lorem ipsum dolor sit amet' if useDefaults else subtitleText }}</span>
            {{ caller() if caller }}
        </h1>
    {% elif type === 'publicationLarge' %}
        <p class="publication is-large heading__publication">
            {% if useDefaults %}
                <span>Postuler avant le</span>
                <time datetime="26-03-2022">26 mars 2022</time>
            {% endif %}
            {{ caller() if caller }}
        </p>
    {% elif type === 'teaser' %}
        <p class="teaser heading__teaser">
            {{ lorem(2) if useDefaults else text }}
            {{ caller() if caller }}
        </p>
    {% elif type === 'quote' %}
        <blockquote class="teaser heading__quote">
            {{ lorem(3) if useDefaults else text }}
            {{ caller() if caller }}
        </blockquote>
    {% elif type === 'synonyms' %}
        <p class="heading__synonyms">
            {{ 'Synonyme(s): Synonyme 1, Synonyme 2' if useDefaults else text }}
            {{ caller() if caller }}
        </p>
         {% elif type === 'type-structure' %}
        <p class="heading__synonyms">
            {{ 'Type 1, Type 2' if useDefaults else text }}
            {{ caller() if caller }}
        </p>
    {% elif type === 'publication' %}
        <p class="publication heading__publication">
            {% if useDefaults %}
                <span>Publié le</span>
                <time datetime="{{ datetime }}">27/11/2021</time>
                <span>- Mise à jour le</span>
                <time datetime="{{ datetime }}">13/01/2022</time>
            {% endif %}
            {{ caller() if caller }}
        </p>
        {% elif type === 'date-deliberations' %}
          <p class="heading__date-deliberations">
           <span>Publié le</span>
                <time datetime="{{ datetime }}">27/11/2021.</time>
                <span class= "heading__deliberations-text"> Mise à jour le</span>
                <span><time datetime="{{ datetime }}">13/01/2022</time></span>
            {{ caller() if caller }}
            </p>
            {% elif type === 'commune-deliberations' %}
          <p class="heading__date-deliberations">
                <span class= "heading__deliberations-title"> commune</span>
                <span class= "heading__deliberations-text"> Lorem ipsum</span>
            {{ caller() if caller }}
            </p>
             {% elif type === 'enregistrement-deliberations' %}
                <p class="heading__date-deliberations">
                    <span class= "heading__deliberations-title"> N'enregistrement</span>
                    <span class= "heading__deliberations-text"> XXXX-XXXX-XXXX</span>
                    {{ caller() if caller }}
                </p>
                {% elif type === 'enregistrement-presse' %}
                <p class="heading__date-deliberations">
                    <span class= "heading__deliberations-title"> Numéro</span>
                    <span class= "heading__deliberations-text"> XXX</span>
                    {{ caller() if caller }}
                </p>
                {% elif type === 'rendu-deliberations' %}
                <p class="heading__date-deliberations">
                    <span class= "heading__deliberations-title">Date du compte rendu</span>
                     <span><time datetime="{{ datetime }}">13/01/2022</time></span>
                    {{ caller() if caller }}
                </p>
    {% elif type === 'infos' %}
        <div class="heading__infos">
            {{ infoBlock() if useDefaults }}
            {{ caller() if caller }}
        </div>
    {% elif type === 'name' %}
        <h1 class="heading__name">
            <span class="heading__name-text">{{ 'Prénomlorem <span>Nomsitamet</span>' if useDefaults else text }}</span>
            {{ caller() if caller }}
        </h1>
    {% elif type === 'functions' %}
        {% if useDefaults %}
            <p class="heading__function is-main">Fonction loremsipm dolor</p>
            <p class="heading__function">Fonction2 dolor dit amet</p>
            <p class="heading__function">Fonction3 consectur elis passam filis</p>
        {% endif %}
        {{ caller() if caller }}
        {% elif type === 'contact-groupe' %}
        {% if useDefaults %}
            <p class="heading__contact-groupe"><span class = "heading__title-contact-groupe">Parti politique</span> : Lorem ipsum dolor </p>
            <p class="heading__contact-groupe"><span class = "heading__title-contact-groupe">Groupe politique</span> : Lorem ipsum dolor</p>
        {% endif %}
    {% elif type === 'content-buttons' %}
        <div class="heading__content-buttons">
            {% if useDefaults %}
                {% call Link(className = 'heading__content-btn', icon = false) %}
                <span class="menu-cross__button-text">S’inscrire à l’annuaire</span>
                {% endcall %}
            {% endif %}
            {{ caller() if caller }}
        </div>
    {% elif type === 'status' %}
        <div class="heading__status">
            {% if useDefaults %}
                {{ Secondary.Status('new') }}
                {{ Secondary.Status('in-progress') }}
                {{ Secondary.Status('assign') }}
            {% endif %}
            {{ caller() if caller }}
        </div>
    {% elif type === 'deadline' %}
        <div class="heading__deadline">
            {% if useDefaults %}
                {{ Secondary.Deadline(className = 'is-new') }}
            {% endif %}
            {{ caller() if caller }}
        </div>
    {% elif type === 'content-state' %}
        <div class="heading__content-state">
            {{ Secondary.Status('new') }}
            {{ Secondary.Deadline('is-new') }}
        </div>
    {% elif type === 'tools' %}
        <div class="heading__tools">
            {{ Tools() }}
        </div>
    {% endif %}
{% endmacro %}

{#
    Insert heading image wrapper.
    @param {string[]} sizes - image sizes array.
    @param {string} className - custom class name for image wrapper.
    @param {string} href - image fancybox href path.
    @param {string} srcset - image fancybox data-srcset value.
    @param {string} caption - image fancybox data-caption value.
#}
{% macro HeadingImage(
    sizes = ['408x272'],
    className = '',
    imageClassName = '',
    href = stratisImage('1280x768').val,
    srcset = stratisImage('1920x1080').val + ' 2000w, ' + stratisImage('1280x768').val + ' 1280w, ' + stratisImage('768x476').val + ' 768w',
    caption = '[FIGCAPTION CONTENT]',
    alt = '[IMAGE ALT] - Agrandir l\'image, fenêtre modale' if href else '[IMAGE ALT]',
    dialog = 'Image agrandie',
    useSecondary = false,
    hasLazyLoad = false
) %}
    <div class="heading__image {{ className }}">
        <figure class="heading__figure" role="figure"
            {% if caption %}
                {{ setAttr('aria-label', caption) }}
            {% else %}
                {{ setAttr('aria-label', "[FIGCAPTION CONTENT]") }}
            {% endif %} >
            {% if href %}
                <a
                    href="{{ href }}"
                    {{ setAttr('data-srcset', srcset) }}
                    {{ setAttr('data-caption', caption) }}
                    {% if caption %}
                        {{ setAttr('data-caption', caption) }}
                    {% else %}
                        {{ setAttr('data-caption', "[FIGCAPTION CONTENT]") }}
                    {% endif %}
                    {{ setAttr('data-alt', '[IMAGE ALT]') }}
                    {{ setAttr('data-dialog-label', dialog) }}
                    {{ setAttr('aria-haspopup', 'dialog') }}
                    data-fancybox
                    class="heading__image-link"
                >
                    {{ Image({
                        sizes: sizes,
                        alt: alt,
                        className: imageClassName,
                        hasLazyLoad: hasLazyLoad
                    }) }}
                </a>
            {% else %}
                {{ Image({
                    sizes: sizes,
                    alt: alt,
                    className: imageClassName,
                    hasLazyLoad: hasLazyLoad
                }) }}
            {% endif %}
            {% if caption %}
                <figcaption class="heading__caption"><span class="heading__caption-text">{{ caption }}</span></figcaption>
            {% endif %}
        </figure>
        {% if caller %}
            {{ caller() }}
        {% elif useSecondary %}
            {{ HeadingPartials('date') }}
            {{ HeadingPartials('time-place') }}
            {{ HeadingPartials('tariff') }}
        {% endif %}
    </div>
{% endmacro %}

{#
    Insert heading content wrapper.
    @param {string} className - add custom class name for heading content wrapper.
#}
{% macro HeadingContent(className = '') %}
    <div class="heading__content {{ className }}">
        {{ caller() if caller }}
    </div>
{% endmacro %}

{#
    Insert heading content top wrapper.
    @param {string} className - add custom class name for heading content top wrapper.
#}
{% macro HeadingContentTop(className = '') %}
    <div class="heading__content-top {{ className }}">
        {{ caller() if caller }}
    </div>
{% endmacro %}

{#
    Insert heading group wrapper.
    @param {string} className - add custom class name for heading content wrapper.
#}
{% macro HeadingEventsGroup(className = '') %}
    <div class="heading__events-group {{ className }}">
        {{ caller() if caller }}
    </div>
{% endmacro %}

{#
    Insert heading group wrapper.
    @param {string} className - add custom class name for heading content wrapper.
#}
{% macro HeadingWorksGroup(className = '') %}
    <div class="heading__works-group {{ className }}">
        {{ caller() if caller }}
    </div>
{% endmacro %}

{#
    Insert heading content bottom wrapper.
    @param {string} className - add custom class name for heading content bottom wrapper.
#}
{% macro HeadingContentBottom(className = '') %}
    <div class="heading__content-bottom {{ className }}">
        {{ caller() if caller }}
    </div>
{% endmacro %}

{#
    Insert heading element.
    @param {string} title - add heading title text.
    @param {string} teaser - add heading teaeser text.
    @param {string} category - add heading category text.
    @param {boolean} overwriteContent - overwrite content zone (caller).
    @param {boolean} overwriteWrapper - overwrite wrapper zone (caller).
    @param {boolean} appendContent - append content zone (caller).
    @param {boolean} appendWrapper - append wrapper zone (caller).
    @param {object} config - provide site config.
#}
{% macro Heading(
    className = '',
    title = '',
    teaser = lorem(),
    category = 'Category 1, Category 2',
    overwriteContent = false,
    overwriteWrapper = false,
    appendContent = true,
    appendWrapper = false,
    config = config
) %}
    {% if overwriteWrapper %}
        {% set overwriteContent = false %}
        {% set appendWrapper = false %}
        {% set appendContent = false %}
    {% elif appendWrapper %}
        {% set overwriteContent = false %}
        {% set overwriteWrapper = false %}
        {% set appendContent = false %}
    {% elif overwriteContent %}
        {% set overwriteWrapper = false %}
        {% set appendWrapper = false %}
        {% set appendContent = false %}
    {% elif appendContent %}
        {% set overwriteContent = false %}
        {% set overwriteWrapper = false %}
        {% set appendWrapper = false %}
    {% endif %}

    {% set datetime = Helpers.date({ format: ['y', 'm', 'd'] }) %}
    <header class="heading {{ className }}">
        <div class="heading__wrapper">
            {% if overwriteWrapper and caller %}
                {{ caller() }}
            {% else %}
                <div class="heading__content">
                    {% if overwriteContent and caller %}
                        {{ caller() }}
                    {% else %}
                        {{ HeadingPartials('category', category, false) if category }}
                        {{ HeadingPartials('title', title or config.title or 'Default title', false) }}
                        {% if config.hasFilter %}
                            {{ HeadingLinks(false, config.hasFilter) }}
                        {% endif %}
                        {% if config.hasFacets %}
                            {{ HeadingLinks(false, config.hasFacets) }}
                        {% endif %}
                        {{ HeadingPartials('teaser', teaser) if teaser }}
                        {{ caller() if caller and appendContent }}
                    {% endif %}
                </div>
                {{ caller() if caller and appendWrapper }}
            {% endif %}
        </div>
    </header>
{% endmacro %}
