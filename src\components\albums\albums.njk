{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitlePrimary -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{% from 'views/utils/utils.njk' import wrapper with context %}
{%- from 'views/core-components/list.njk' import List -%}

{#
    AlbumsItem template.
#}
{%- macro AlbumsItem(
    className = '',
    imageSizes = ['480x320'],
    category = 'Dolor sit amet',
    title = 'Nam aliquet felis eu tincidunt',
    media = true,
    video = [true, false] | random,
    addIconAfter = false
) -%}
    <article class="album-item {{ className }}">
        <div class="album-item__content">
            <h3 class="item-title album-item__title">
                {% if category %}
                    <span class="theme album-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {% endif %}
                <a href="{{ kGlobalLinks.singleAlbum }}" class="album-item__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
            </h3>
            {% if media %}
                <p class="album-item__media">{{ range(1, 38) | random }} {{ 'vidéos' if video else 'photos' }}</p>
            {% endif %}
            {% if addIconAfter %}
                <div class="album-item__type{{ ' is-video' if video }}">
                    {{ Icon('far fa-play' if video else 'far fa-eye') }}
                    <span class="sr-only">{{ 'Vidéos' if video else 'Photos' }}</span>
                </div>
            {% endif %}
        </div>
        {{ Image({
            className: 'album-item__picture',
            sizes: imageSizes,
            serviceID: range(100) | random,
            alt: 'image alt'
        }) }}
    </article>
{%- endmacro -%}

{#
    AlbumsList template.
#}
{%- macro AlbumsList(
    itemClass = 'has-mb-5',
    count = 12,
    cols = 3,
    mdCols = 1,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ AlbumsItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    AlbumsHome template.
#}
{%- macro AlbumsHome(
    isFluid = false,
    modifier = '',
    withoutSpaces = false,
    titleText = 'Voir & revoir',
    itemsCount = 2,
    cols = 2,
    moreButton = true
) -%}
    {% call Section(
        className = 'albums-home',
        modifier = modifier,
        container = false
    ) %}
        <div class="section__title">
            {{ TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="albums-home__container {{ 'is-fluid' if isFluid }}">
            <div class="section__content">
                {% call List(
                    listClass = 'is-fluid' if modifier === 'is-moved',
                    count = itemsCount,
                    cols = cols
                ) %}
                    {{ AlbumsItem(
                        className = 'is-overlapped is-inverted',
                        imageSizes = ['480x320?479', '800x530?767', '640x425?1279', '960x640'] if isFluid else ['480x320?479', '800x530?767', '640x425?1279', '600x400'],
                        media = false,
                        addIconAfter = true
                    ) }}
                {% endcall %}
            </div>
            {% if moreButton %}
                <div class="section__more-links">
                    {% if moreButton %}
                        {{ Link(
                            href = kGlobalLinks.listAlbums,
                            text = 'Tous les albums',
                            className = 'btn is-sm-small is-primary',
                            icon = false
                        ) }}
                    {% endif %}
                </div>
            {% endif %}
        </div>
    {% endcall %}
{%- endmacro -%}
