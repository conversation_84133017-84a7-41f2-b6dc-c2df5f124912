.menu-floating {
    $this: &;

    @include fixed(auto, 0, 0, 0);
    background-color: var(--color-1--1);
    display: none;
    transition: all 400ms ease 0ms;
    z-index: 100;

    @include breakpoint(small down) {
        display: block;
    }

    &.is-hidden {
        opacity: 0;
        visibility: hidden;
    }

    &__toggle {
        display: none;
    }

    &__list {
        display: flex;
        justify-content: center;
        list-style: none;
        position: relative;
        z-index: 10000;
    }

    &__item {
        margin: 0;
        transition: transform 0.4s ease;
        will-change: transform;

        @include breakpoint(small down) {
            margin: 0 11px;
            transition: none;
            will-change: unset;
        }
    }

    &__link {
        @include trs;
        @include font(var(--typo-1), 1rem, var(--fw-light));
        @include size(65px, 60px);
        align-items: center;
        background-color: var(--color-1--1);
        color: $color-white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        position: relative;
        text-decoration: none;
        text-transform: uppercase;
        z-index: 7;

        @include breakpoint(small down) {
            width: 70px;
        }

        @include on-event() {
            background-color: var(--color-1--2);
        }
    }

    &__icon {
        align-items: center;
        background-color: inherit;
        display: flex;
        justify-content: center;
        position: relative;
        z-index: 5;

        @include breakpoint(small down) {
            font-size: 2rem;
            margin-bottom: 3px;
        }

        svg {
            @include size(20px, 21px);
            fill: $color-white;
            line-height: 60px;
        }
    }

    .click {
        @include breakpoint(small down) {
            display: block;
        }

        &__toggle {
            @include size(65px, 60px);
            background-color: var(--color-1--1);
            flex-direction: column;

            @include on-event() {
                background-color: var(--color-1--2);
            }

            &.is-open {
                position: relative;

                &::before,
                &::after {
                    @include triangle('top', $color-3--3, 50px, -11px);
                    @include absolute(-11px, null, null, 50%);
                    content: '';
                    transform: translateX(-50%);

                    @include breakpoint(medium down) {
                        border-width: 0 17px 10px 17px;
                        bottom: auto;
                        top: -10px;
                    }
                }

                &::after {
                    border-bottom-color: $color-white;
                    top: -10px;

                    @include breakpoint(medium down) {
                        border-bottom-color: var(--color-1--2);
                    }
                }
            }
        }

        &__toggle-icon {
            @include size(18px, 21px);
            color: $color-white;
            font-size: 1.8rem;

            @include breakpoint(small down) {
                font-size: 2.4rem;
            }
        }

        &__toggle-text {
            color: $color-white;
            font-size: 1rem;
            margin-left: 0;
        }

        &__block {
            @include fixed(auto, 0, 50px, 0);
            @include size(100vw, calc(100vh - 60px));
            z-index: 1000;
        }

        .js-dropdown-block {
            @include breakpoint(small down) {
                bottom: 60px;
            }
        }

        &.is-open > .js-dropdown-block {
            transform: translateX(-50%);

            @include breakpoint(small down) {
                left: 0;
                transform: unset;
                transition: none;
            }
        }
    }

    &__search {
        .search {
            &__toggle {
                @include size(65px, 60px);
                display: flex;
                flex-direction: column;
                text-transform: capitalize;

                @include on-event() {
                    background-color: var(--color-1--2);
                }
            }

            &__toggle span[class*='fa-'] {
                font-size: 1.9rem;
            }

            &__toggle-text {
                color: $color-white;
                font-size: 1rem;
                letter-spacing: 0 !important;
            }
        }
    }

    #{$this}__nav-toggle {
        .main-nav-toggle {
            @include font(var(--typo-1), 1.6rem, var(--fw-bold));
            @include size(65px, 60px);
            background-color: transparent;
            border: 0;
            box-shadow: none;
            color: $color-white;
            flex-direction: column;

            @include on-event() {
                @include size(50px, 50px);
                background-color: var(--color-2--1);
                font-size: 1.4rem;
                margin-top: 5px;
            }

            &::before {
                @include size(38px, 44px);
                background-size: 100% 38px;
            }

            &.is-open {
                background-color: var(--color-1--2);

                &::before,
                &::after {
                    @include breakpoint(medium down) {
                        @include absolute(-9px, null, null, 50%);
                        content: '';
                        transform: translateX(-50%);
                    }
                }

                &::before {
                    @include breakpoint(small down) {
                        top: 2px;
                    }
                }

                &::after {
                    @include breakpoint(medium down) {
                        border-bottom-color: var(--color-1--2);
                    }
                }
            }

            &__bar {
                background-color: $color-white;
            }

            &__text {
                color: $color-white;
                font-size: 1rem;
                position: relative;
            }
        }
    }

    .main-nav & {
        @include relative();
        display: block;
        height: 100%;
        transform: none;
        z-index: auto;

        #{$this}__toggle {
            @include font(var(--typo-1), 1.6rem, var(--fw-bold));
            @include relative(0, 0);
            @include size(100%, 50px);
            background-color: var(--color-2--1);
            border: 0;
            color: $color-white;
            display: block;
            text-transform: uppercase;
        }

        #{$this}__toggle-icon {
            @include icon-before($fa-var-hand-point-up);
            margin-right: 5px;
        }

        #{$this}__list {
            display: none;
        }

        #{$this}__item {
            border-top: 1px solid $color-white;
            margin: 0;
        }

        #{$this}__link {
            background-color: var(--color-1--1);
            width: 100%;

            @include on-event {
                background-color: var(--color-2--1);
                transform: none;
            }
        }
    }
}
