{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/utils/utils.njk' import setAttr -%}

{% macro PopupCloseBtn(
    modifier = '',
    ghostText = true,
    tooltip = false
) %}
    <button data-fancybox-close
        type="button"
        class="popup__close-btn {{ modifier }} {{ 'js-tooltip' if tooltip }}"
        {{ setAttr('data-content', tooltip) if tooltip }}
    >
        {{ Icon('far fa-times') }}
        <span class="btn__text {{ 'ghost' if ghostText }}">{{ tooltip if tooltip else 'Fermer' }}</span>
    </button>
{% endmacro %}

