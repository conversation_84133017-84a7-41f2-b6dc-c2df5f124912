.ontheweb-carousel {
    position: relative;

    &__container {
        width: calc(100% - 180px);

        &.is-home-page {
            width: calc(100% - 100px);
        }

        @include breakpoint(medium down) {
            width: calc(100% - 106px);
        }

        @include breakpoint(small down) {
            margin: 0 auto;
            width: calc(100% - 140px);
        }
    }

    &.is-home-page {
        display: flex;
        flex-direction: column-reverse;
    }

    &__pagination {
        &.is-home-page {
            display: flex;
            margin: 50px auto 96px;
            width: fit-content;

            @include breakpoint(medium down) {
                margin: 20px auto 79px;
            }

            @include breakpoint(small down) {
                margin: 30px auto 0;
            }

            .swiper-pagination {
                &__bullet {
                    @include size(10px);
                    border: 1px solid $color-3--4;
                    border-radius: 5px;
                    margin-left: 6px;
                    position: relative;

                    &:first-child {
                        margin-left: 0;
                    }

                    &.is-active {
                        background-color: var(--color-1--1);
                        border: none;
                        width: 26px;
                    }
                }

                &__bullet-btn {
                    @include absolute(0, 0, 0, 0);
                    @include size(100%);
                    opacity: 0;
                }
            }
        }
    }

    &__wrapper {
        align-items: end;
        display: flex;

        &.is-home-page {
            align-items: flex-start;
        }
    }

    &__control {
        @include absolute(50%);
        @include font(null, 2rem);
        @include size(50px);
        background: none;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        padding: 0;
        transform: translateY(-50%);
        z-index: 2;

        .is-home-page & {
            color: $color-black;
            font-size: 4rem;
            top: 27%;

            @include breakpoint(medium down) {
                top: 65px;
            }

            &.is-prev {
                left: -5px;

                @include breakpoint(medium down) {
                    left: 10px;
                }

                @include breakpoint(small down) {
                    left: -10px;
                }
            }

            &.is-next {
                right: -5px;

                @include breakpoint(medium down) {
                    right: 10px;
                }

                @include breakpoint(small down) {
                    right: -10px;
                }
            }
        }

        &.is-prev {
            left: 0;
        }

        &.is-next {
            right: 0;
        }
    }

    &__item {
        padding: 5px;
    }
}
