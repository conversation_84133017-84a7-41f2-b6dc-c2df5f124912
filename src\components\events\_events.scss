.events-home {
    overflow: visible;
    padding: 0 0 50px;
    position: relative;

    @include breakpoint(small down) {
        padding: 0;

        .home-page & {
            margin-bottom: 65px;
        }
    }

    &.section {
        @include breakpoint(large only) {
            margin-bottom: 0;
            padding-bottom: 0;
        }
    }

    &::before {
        @include size(calc(100% - 100px), 614px);
        @include absolute(121px, 0, 0, 0);
        background-color: var(--color-1--5);
        border-radius: 20px;
        content: '';
        min-width: 1475px;
        transform: matrix(1, -0.02, 0.02, 1, 0, 0);
        z-index: -1;

        @include breakpoint(medium down) {
            @include size(calc(100% + 16px), 564px);
            left: -50px;
            min-width: auto;
            top: 154px;
        }

        @include breakpoint(small down) {
            @include size(100%, 350px);
            left: -20px;
            top: 118px;
        }
    }

    &__title {
        align-items: flex-start;
        flex-direction: column;
        margin-bottom: 55px;
        padding: 0 40px;

        .title {
            margin-bottom: 20px;

            @include breakpoint(small down) {
                margin-bottom: 9px;
                margin-right: 0;
            }
        }

        @include breakpoint(medium down) {
            padding: 0;
        }

        @include breakpoint(small down) {
            align-items: center;
        }
    }

    &__container {
        @extend %container;
        padding: 0;

        @include breakpoint(medium down) {
            max-width: 1130px;
            padding: 0 40px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    & &__content {
        @include breakpoint(medium only) {
            padding: 0 22px;
        }
    }

    & &__list {
        padding-top: 20px;
        position: relative;

        &:first-child {
            padding-top: 50px;
        }

        &::before {
            @include absolute(0, null, null, 50%);
            @include size(100vw, 100%);
            background-color: var(--color-1--2);
            content: '';
            transform: translateX(-50%);
            z-index: 0;
        }

        @include breakpoint(medium down) {
            margin: 0;
        }
    }

    & &__list-item {
        margin-bottom: 20px;

        @include breakpoint(medium down) {
            margin-bottom: 10px;
            padding: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }

    & &__more-links {
        margin-top: 10px;
    }

    & &__eventlinks,
    & &__banner {
        margin: 70px 0;

        @include breakpoint(medium down) {
            margin: 60px 0 0;
        }
    }

    .home-page & {
        .event-item {
            &__title-link {
                pointer-events: none;
            }
        
            .swiper-slide-active & {
                .event-item__title-link {
                    pointer-events: all;
                }
            }
        }
    }
}

.event-badge {
    $this: &;

    @include size(57px);
    align-items: center;
    background-color: $color-2--4;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: center;
    margin-top: 40px;
    padding: 11px 14px;

    @include breakpoint(small down) {
        @include size(37px);
    }

    .single-event-focus & {
        @include size(90px);
        position: absolute !important;
        top: 170px;

        @include breakpoint(medium down) {
            @include size(57px);
            left: 50% !important;
            padding: 11px 14px;
            top: -27px;
            transform: translateX(-50%);
        }

        #{$this}__title {
            font-size: 1.5rem;

            @include breakpoint(medium down) {
                font-size: 1rem;
            }
        }

        #{$this}__Pictogramm {
            margin-bottom: 15px;

            svg {
                @include size(18px);
                fill: $color-white;
                font-size: 1.8rem;
                font-weight: var(--fw-light);

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                }
            }
        }
    }

    .heading__badge & {
        @include reset-position();
        @include size(105px);
        margin-left: -15px;
        padding: 20px 29px 27px 27px;

        @include breakpoint(small down) {
            @include size(76px);
            font-size: 1.2rem;
        }

        #{$this}__title {
            font-size: 1.7rem;
            line-height: 1.15;

            @include breakpoint(small down) {
                font-size: 1.2rem;
            }
        }

        #{$this}__Pictogramm {
            @include size(18px);

            svg {
                fill: $color-white;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                }
            }
        }
    }

    @include breakpoint(medium only) {
        margin-top: 40px;
        position: static;
    }

    @include breakpoint(small down) {
        left: 60px;
        margin-top: 40px;
        top: 15px;
    }

    &__title {
        @include font(var(--typo-1), 1rem, var(--fw-normal));
        color: $color-white;
        line-height: 11px;
        text-align: center;
    }

    &__Pictogramm {
        @include font(var(--typo-1), 1rem, var(--fw-normal));
        @include size(15px);
        fill: $color-white;
        margin-bottom: 5px;
    }
}

.is-width-33 {
    &.events-widget {
        .section {
            &__more-links {
                @include breakpoint(medium down) {
                    margin-left: 100px !important;
                }

                @include breakpoint(small down) {
                    margin-left: 50px !important;
                }
            }
        }
    }
}

.events-focus-content,
.events-widget,
.events-content {
    &.is-events-list {
        .section {
            &__title {
                @include breakpoint(small down) {
                    justify-content: center;
                    margin-bottom: 50px;
                }
            }

            &__more-links {
                .btn {
                    &.is-link {
                        font-size: 1.3rem;
                    }
                }
            }
        }
    }

    .section {
        &__more-links {
            justify-content: flex-start;
            margin-left: auto;
            margin-right: auto;
            width: fit-content;

            @include breakpoint(medium down) {
                justify-content: center;
                margin-left: auto;
                margin-right: auto;
            }

            .btn {
                &.is-link {
                    background-color: var(--color-1--1);
                    border: 1px solid var(--color-1--1);
                    border-radius: 40px;
                    color: $color-white;
                    padding: 24px 45px;
                }
            }
        }
    }
}

.list-events {
    .propose-event {
        height: 60px;
        margin-bottom: 0;
        margin-left: 12px;
        margin-top: 0;
        width: max-content !important;

        @include breakpoint(small down) {
            width: 100% !important;
        }
    }

    .events-list {
        margin-top: 70px;

        @include breakpoint(medium down) {
            justify-content: center;
        }

        &__item {
            margin-bottom: 100px;

            @include breakpoint(medium down) {
                max-width: 328px;
            }
        }
    }
}

.single-event-page {
    .event-carousel {
        margin-top: 120px;
    }
}

.carousel-pagination-event {
    display: flex;
    justify-content: center;
    margin: auto;
    z-index: 1;
    @include breakpoint(large down) {
        @include relative(null,null,4px,null);
    }

    @include breakpoint(medium down) {
        @include relative(null,null,15px,null);
    }

    .carousel-bullet-event {
        @include size(10px);
        background-color: transparent;
        border: 1px solid gray;
        border-radius: 20px;
        cursor: pointer;
        margin: 0 2px;
        transition: background-color 0.3s;

        &.active {
            background-color: var(--color-1--1);
            width: 26px;
        }
    }
}
