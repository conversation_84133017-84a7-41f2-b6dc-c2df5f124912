{%- from 'views/utils/utils.njk' import getImagePath -%}
{%- import 'views/core-components/filter.njk' as Filter -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'components/map-elements/map-elements.njk' import MapSearchResults, MapSearchResultsBottom -%}
{%- from 'components/map-header/map-header.njk' import MapHeader -%}
{%- from 'components/map/map.njk' import Map -%}

{#
    FiltersMapAgenda template.
#}
{%- macro FiltersMapAgenda() -%}
    {% call Filter.FilterWrapper(
        formClassName = 'js-validator-form',
        title = 'Cartographie de l’agenda:',
        legend = 'Filtrer les résultats',
        legendClassName = 'sr-only',
        ariaLabelText = 'Filtrer les événements'
    ) %}
        {% call Filter.FieldsWrapper(className = 'js-scrollable') %}
            {% call Filter.FieldsGroup() %}
                {% call Filter.FilterField('col-md-4') %}
                    {{ Form.FormField(label = 'Mots clés') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-4') %}
                    {{ Form.Multiselect(legend = 'Thématiques') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-4') %}
                    {{ Form.FormField(type = 'date', label = 'Date:') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-4') %}
                    {{ Form.FormField(type = 'select', label = 'Période:') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-4') %}
                    {{ Form.FormField(type = 'select', label = 'Lieux') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Thématiques', dropdown = false, modifier = 'is-group', groupButton = true) }}
                {% endcall %}

                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Thématiques', dropdown = false) }}
                {% endcall %}
            {% endcall %}
        {% endcall %}

        {{ Filter.ButtonsWrapper(buttons = [
            {
                text: 'Filtrer',
                type: 'submit',
                className: 'filter-submit',
                icon: 'fas fa-filter'
            },
            {
                text: 'Effacer les filtres',
                type: 'reset',
                className: 'filter-reset',
                icon: ''
            }
        ]) }}
    {% endcall %}
{%- endmacro -%}

{#
    FiltersMapAnnuaire template.
#}
{%- macro FiltersMapAnnuaire() -%}
    {% call Filter.FilterWrapper(
        formClassName = 'js-validator-form',
        legend = 'Filtrer les résultats',
        legendClassName = 'sr-only',
        title = 'Titre de l\'annuaire'
    ) %}
        {% call Filter.FieldsWrapper(className = 'js-scrollable') %}
            {% call Filter.FieldsGroup() %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.FormField(label = 'Rechercher', placeholder = 'Saisir un mot clé...') }}
                {% endcall %}

                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Filtrer', dropdown = false, modifier = 'is-group', groupButton = true) }}
                {% endcall %}

            {% endcall %}
        {% endcall %}

        {{ Filter.ButtonsWrapper(buttons = [
            {
                text: 'Filtrer',
                type: 'submit',
                className: 'filter-submit',
                icon: 'fas fa-filter'
            },
            {
                text: 'Effacer les filtres',
                type: 'reset',
                className: 'filter-reset',
                icon: ''
            }
        ]) }}
    {% endcall %}
{%- endmacro -%}

{#
    MapTemplate template.
    @param {boolean} useEvents - set useEvents setting for popup on map.
    @param {boolean} filtersMapAgenda - set filters for map agenda.
#}
{% macro MapTemplate(useEvents = false, filtersMapAgenda = false, filtersMapAnnuaire = false, FiltersMaptravaux = false, schedules = false, mapPopupHeading3 = false) %}
<div class="map-template">
    <div class="map-template__wrapper js-map-context">
        {{ MapHeader() }}
        <div class="map-template__column" id="map-filter-column">
            <div class="map-template__top">
                {{ MapTemplateHeading({
                    useLogo: true,
                    close: {
                        icon: 'far fa-times',
                        text: 'Fermer le filtrage',
                        attrs: {
                            'aria-controls': 'map-filter-column',
                            'data-content': 'Fermer le filtrage'
                        }
                    }
                }) }}
            </div>
            <div class="map-template__content">
                {% if filtersMapAgenda %}
                    {{ FiltersMapAgenda() }}
                {% endif %}
                {% if FiltersMaptravaux %}
                    {{ FiltersMapTravaux() }}
                {% endif %}
                {% if filtersMapAnnuaire %}
                    {{ FiltersMapCommune() }}
                {% else %}
                    {{ FiltersMapAnnuaire() }}
                {% endif %}
            </div>
            <div class="map-template__bottom">
                {{ MapSearchResults(get = 'toggle') }}
            </div>
        </div>

        <div class="map-template__column is-search is-hidden" id="map-search-column">
            <div class="map-template__top">
                {{ MapTemplateHeading({
                    modifier: 'is-results',
                    title: 'Résultats de votre recherche',
                    close: {
                        icon: 'far fa-times',
                        text: 'Fermer les résultats',
                        attrs: {
                            'aria-controls': 'map-search-column',
                            'data-content': 'Fermer les résultats'
                        }
                    }
                }) }}
            </div>
            <div class="map-template__content">
                <div class="map-template__content-scroll">
                    {{ MapSearchResults() }}
                </div>
            </div>
            <div class="map-template__bottom">
                {{ MapSearchResultsBottom() }}
            </div>
        </div>

        <div class="map-template__main">
            {{ Map({
                useContext: false,
                schedules: schedules,
                useEvents: useEvents,
                mapPopupHeading3: mapPopupHeading3
            }) }}
        </div>
    </div>
</div>
{% endmacro %}


{#
    MapTemplateHeading default settings.
    @param {string} title - set title for heading.
    @param {object} close - settings for close button.
#}
{% set MapTemplateHeadingDefaults = {
    modifier: '',
    title: '',
    useLogo: false,
    close: {
        className: 'map-template-heading__close js-map-template-toggle js-tooltip',
        icon: 'far fa-times',
        text: 'Fermer les résulats',
        attrs: {
        }
    }
} %}

{#
    MapTemplateHeading template.
    @param {object} settings - set settings for template.
#}
{% macro MapTemplateHeading(settings = {}) %}
    {% set params = Helpers.merge(MapTemplateHeadingDefaults, settings) %}

    <div class="map-template-heading {{ params.modifier }}">
        {%- if params.useLogo -%}
            <!-- <img src="{{getImagePath('map/logo-white.png')}}" alt="LOGO DU SITE" class="map-template-heading__image"> -->
            <p class="map-site-domen">
                cartographie </br>
                <a href="#" class="map-site-domen__domen">
                    <span>essonne</span>.fr
                </a>
            </p>
        {%- else -%}
            <h2 class="map-template-heading__title">{{ params.title }}</h2>
        {%- endif -%}
        {% if params.close %}
            {% call Button(
                className = params.close.className,
                icon = params.close.icon,
                attrs = params.close.attrs
                ) %}
                <span class="ghost">{{ params.close.text }}</span>
            {% endcall %}
        {% endif %}
    </div>
{% endmacro %}

{#
    FiltersMapCommune template.
#}
{%- macro FiltersMapCommune() -%}
    {% call Filter.FilterWrapper(
        formClassName = 'js-validator-form',
        legend = 'Filtrer les résultats',
        legendClassName = 'sr-only',
        title = 'Titre de l\'annuaire'
    ) %}
        {% call Filter.FieldsWrapper(className = 'js-scrollable') %}
            {% call Filter.FieldsGroup() %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.FormField(label = 'Rechercher', placeholder = 'Saisir un mot clé...') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Thématiques') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'communes') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Types') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Activités') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'cantons') }}
                {% endcall %}
            {% endcall %}
        {% endcall %}

        {{ Filter.ButtonsWrapper(buttons = [
            {
                text: 'Filtrer',
                type: 'submit',
                className: 'filter-submit',
                icon: 'fas fa-filter'
            },
            {
                text: 'Effacer les filtres',
                type: 'reset',
                className: 'filter-reset',
                icon: ''
            }
        ]) }}
    {% endcall %}
{%- endmacro -%}

{%- macro FiltersMapTravaux() -%}
    {% call Filter.FilterWrapper(
        formClassName = 'js-validator-form',
        legend = 'Filtrer les résultats',
        legendClassName = 'sr-only',
        title = 'Titre de l\'annuaire'
    ) %}
        {% call Filter.FieldsWrapper(className = 'js-scrollable') %}
            {% call Filter.FieldsGroup() %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.FormField(label = 'Mots-clés', placeholder = 'Saisir un mot clé...') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Types de travaux') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'communes') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'cantons') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.Multiselect(legend = 'Conséquences') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.FormField(type = 'date', label = 'Du:') }}
                {% endcall %}
                {% call Filter.FilterField('col-md-6') %}
                    {{ Form.FormField(type = 'date', label = 'Au:') }}
                {% endcall %}
            {% endcall %}
        {% endcall %}

        {{ Filter.ButtonsWrapper(buttons = [
            {
                text: 'Filtrer',
                type: 'submit',
                className: 'filter-submit',
                icon: 'fas fa-filter'
            },
            {
                text: 'Effacer les filtres',
                type: 'reset',
                className: 'filter-reset',
                icon: ''
            }
        ]) }}
    {% endcall %}
{%- endmacro -%}
