{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/infos.njk' import InfoBlock -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    CommuneItem template.
    @param {object} settings - organigram item settings.
#}
{%- macro CommuneListItem(
    imageSizes = ['250x250'],
    tag = 'h3',
    className = ''
) -%}
    <div class="commune-list-item js-organigramme-item {{ className }}">
        {{ Image({
            sizes: imageSizes,
            className: 'commune-list-item__picture',
            serviceID: range(50) | random,
            alt: ''
        }) }}
        <div class="commune-list-item__content">
            <div class="commune-list-item__content-top">
                <{{ tag }} class="commune-list-item__name">
                    <span class="commune-list-item__position">Thématique</span>
                    <span class="sr-only"> : </span>
                    <a href="#" class="commune-list-item__name-link" data-filter="name">
                        <span class="underline">Prénomlorem Nomsitamet</span>
                    </a>
                </{{ tag }}>
            </div>
            <div class="commune-list-item__content-info">
                <div class="commune-list-item__details">
                    {%- for item in range(3) %}
                        <p class="commune-list-item__function {{ 'is-main' if loop.first }}" data-filter="position">
                            Fonction {{ loop.index }} {{ lorem(3, 'words') }}
                        </p>
                        <p class="commune-list-item__function is-location">Commune lorem ipsum</p>
                    {%- endfor %}
                </div>
                <ul class="commune-list-item__infos">
                    {% set links = [
                        ['04 65 71 52 33', 'Téléphone', 'far fa-phone', 'tel:0465715233'],
                        ['04 65 71 52 33', 'Téléphone', 'far fa-phone', 'tel:0465715233'],
                        ['04 65 71 52 33', 'Fax', 'far fa-fax', 'tel:0465715233'],
                        ['Courriel', null, 'far fa-at', 'mailto:<EMAIL>'],
                        ['Courriel', null, 'far fa-at', 'mailto:<EMAIL>']
                    ] %}
                    {% for text, textSrOnly, icon, link in links %}
                        <li class="commune-list-item__infos-item">
                            {{ Link(
                                className = 'btn is-small',
                                text = text,
                                textSrOnly = textSrOnly,
                                href = link,
                                icon = icon
                            ) }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    OrganigrammeList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro CommuneList(
    className = "",
    listClass = '',
    itemClass = 'has-mb-6',
    tagItem = 'h3',
    cols = 3,
    mdCols = 2,
    smCols = 1,
    xsCols = 1,
    count = 6,
    primary = false
    ) -%}
    {% call List(
        itemClass = itemClass,
        tagItem = tagItem,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        listClass = listClass,
        count = count
        ) %}
        {{ CommuneListItem(className = 'is-primary' if primary, tag = tagItem) }}
    {% endcall %}
{%- endmacro -%}
