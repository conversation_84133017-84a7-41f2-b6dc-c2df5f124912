.click {
    &__toggle {
        @include size(auto, 60px);
        @include trs();
        align-items: center;
        background-color: transparent;
        border: 0;
        color: $color-white;
        display: flex;
        padding: 10px;

        @include breakpoint(small down) {
            justify-content: center;
            padding: 0;
        }
    }

    &__toggle-text {
        @include on-event() {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
            text-decoration: underline;
        }
    }

    &__toggle-icon {
        @include icon-after(\e195);
        @include size(26px, 30px);
        align-items: center;
        color: $color-white;
        display: flex;
        font-size: 3rem;
    }

    &__toggle-text {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        letter-spacing: 1.2px;
        margin-left: 8px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            letter-spacing: 0;
        }
    }

    &__block {
        background-color: $color-white;
        border-radius: 10px;
        left: -240px;
        min-width: 504px;
        padding: 0;

        @include breakpoint(small down) {
            border-radius: 0;
        }

        &::before {
            border-color: rgba(0, 0, 0, 0);
            border-bottom-color: $color-white;
            border-style: solid;
            border-width: 0 20px 20px 20px;
            content: '';
            position: absolute;
            top: 2px;
            transform: translate(24rem, -100%);
            z-index: 1;

            @include breakpoint(small down) {
                content: none;
            }
        }

        &:has(.js-return-btn) {
            &::before {
                border-bottom-color: $color-3--2;
            }
        }

        @include breakpoint(medium down) {
            min-width: 360px;
        }

        @include breakpoint(small down) {
            min-width: 100%;
        }

        &[aria-hidden='false'] {
            opacity: 1;
            visibility: visible;
        }

        .js-toggle-one-click-item,
        .js-return-btn {
            cursor: pointer;
        }
    }

    &__top-components {
        border-radius: 10px 10px 0 0;
        display: flex;
        margin-bottom: 28px;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
            padding: 10px 0 10px 30px;
        }

        @include breakpoint(small down) {
            border-radius: 0;
        }

        &:has(.js-return-btn) {
            background-color: $color-3--2;
        }

        @include breakpoint(small down) {
            background-color: $color-3--2;
        }
    }

    &__close-wrap {
        @include breakpoint(large down) {
            margin: 5px 5px 10px auto;

            button {
                border: none;

                span[class*='fa-'] {
                    font-size: 2rem;
                }
            }
        }

        @include breakpoint(small down) {
            margin: 10px 20px 10px auto;

            button {
                border: 1px solid var(--color-1--1);
            }
        }
    }

    &__logo {
        @include breakpoint(medium down) {
            @include size(70px, 70px);
            margin: 0 auto;
        }

        @include breakpoint(small down) {
            @include size(41px, 40px);
        }

        .is-subsite & {
            @include breakpoint(medium down) {
                @include size(83px, 30px);
                margin: auto;
            }
        }

        .logo {
            &__image-default {
                .is-subsite & {
                    @include breakpoint(medium down) {
                        display: block;
                    }
                }
            }

            &__image-secondary {
                .is-subsite & {
                    @include breakpoint(medium down) {
                        display: none;
                    }
                }
            }
        }
    }

    .list[class] {
        margin-left: 0;
        margin-right: 0;
    }

    &__item {
        min-height: 104px;
        width: calc(100% / 3);

        .quicklink-item {
            flex-direction: column;
            padding: 5px;

            &__svg-wrapper {
                @include size(44px, 42px);
            }

            &__text {
                font-size: 1.1rem;
                text-transform: none;
            }
        }
    }

    @include breakpoint(small down) {
        display: none;
    }

    .js-one-click-return-wrap {
        padding: 20px;
    }

    .one-click-return__button {
        background-color: transparent;
        border: none;
        color: var(--color-1--1);
        display: flex;

        .fa-long-arrow-left {
            padding-right: 15px;
            padding-top: 3px;
        }

        .btn__svg {
            @include size(22px);

            .cls-1 {
                fill: var(--color-1--1);
            }
        }
    }

    .swiper-pagination__bullet {
        &:only-child {
            display: none;
        }
    }
}
