.news-home {
    $this: &;

    &.is-type-2 {
        .section {
            &__content {
                display: flex;
                flex-wrap: wrap;
                margin: 0 -12px;

                @include breakpoint(medium down) {
                    margin: 0 -6px;
                }

                @include breakpoint(small down) {
                    flex-direction: column;
                    margin: 0;
                }
            }

            &__title {
                align-content: start;
                flex-wrap: wrap;
                margin: 0 auto 0 12px;
                width: 30%;

                @include breakpoint(medium down) {
                    flex-wrap: nowrap;
                    margin: 0 -16px 30px;
                    width: 100%;
                }

                @include breakpoint(small down) {
                    margin: 0 0 30px;
                }

                .tag-links {
                    margin: 40px 0 0;

                    @include breakpoint(medium down) {
                        margin: 0 -52px 0 20px;
                    }

                    @include breakpoint(small down) {
                        margin: 28px 0 0;
                    }
                }
            }

            &__more-links {
                margin-top: 40px;
            }
        }

        .news-focus {
            margin: 0 12px 24px;

            @include breakpoint(medium down) {
                margin: 0 6px 12px;
            }

            @include breakpoint(small down) {
                margin: 0 0 30px;
            }

            &::before {
                display: none;
            }

            &__content {
                @include absolute(null, null, 0, 0);
                background-color: transparent;
                max-width: none;
                min-height: auto;
                padding: 0 0 40px 0;
                width: auto;

                @include breakpoint(small down) {
                    @include reset-position();
                    padding: 5px 10px 10px 0;
                }

                &::after,
                &::before {
                    display: none;
                }
            }

            &__picture-link {
                flex-grow: 1;
            }

            &__category {
                @include breakpoint(small down) {
                    margin-top: -35px;
                }
            }
        }

        .news-list {
            justify-content: flex-start;
            margin: 0;

            @include breakpoint(small down) {
                margin: 0 -6px;
            }

            &::before {
                content: none;
            }

            &__item {
                margin-bottom: 24px;

                @include breakpoint(medium down) {
                    margin-bottom: 12px;
                }

                @include breakpoint(small down) {
                    margin-bottom: 20px;
                }

                .news-item {
                    @include size(auto);
                    margin: 0;
                }
            }
        }

        .news-item {
            height: 100%;
            margin: 0 12px 24px;
            width: calc((100% / 3) - 24px);

            @include breakpoint(medium down) {
                margin: 0 6px 12px;
                width: calc(50% - 12px);
            }

            @include breakpoint(small down) {
                margin: 0 0 20px;
                width: 100%;
            }

            &__content[class] {
                @include absolute(null, null, 0, 0);
                @include size(100%);
                align-items: flex-end;
                background-color: transparent;
                display: flex;
                max-width: none;
                padding: 0 0 40px 0;

                @include breakpoint(small down) {
                    @include reset-position();
                    max-width: 68.5%;
                    padding: 0 0 0 10px;
                }

                &::after {
                    display: none;
                }
            }

            &__image {
                @include breakpoint(medium  down) {
                    flex-shrink: 1;
                    max-width: none;
                }

                @include breakpoint(small down) {
                    max-width: 32.5%;
                }
            }
        }

        // Because we need the same styles for titles in news-focus and in news-item
        .item-title {
            .theme {
                background-color: $color-white;
                display: table;
                margin-bottom: 0;
                padding: 10px 24px 10px 0;
            }

            .js-is-inlined {
                span {
                    background-color: $color-white;
                    display: table;
                    padding: 0 24px 0 0;

                    @include breakpoint(small down) {
                        display: initial;
                        padding: 0;
                    }
                }
            }
        }

        .shortnews {
            margin: 0 12px;
            width: calc((100% / 3) - 24px);

            @include breakpoint(medium down) {
                margin: 30px 6px 0;
                width: 100%;
            }

            @include breakpoint(small down) {
                margin: 30px 0 0;
            }

            .list__item {
                margin-bottom: 0;
            }

            .shortnews-item {
                padding: 10px 24px 10px 30px;

                @include breakpoint(medium down) {
                    padding: 15px 50px 15px 30px;
                }

                &__category {
                    margin-bottom: 5px;
                }
            }
        }
    }

    // Styles for news type-2 without focus and shortnews
    &.is-grid {
        .section__title {
            @include breakpoint(large) {
                @include absolute(0, null, null, 28px);
            }
        }

        .news-list {
            @include breakpoint(large) {
                display: grid;
                grid-template-areas:
                    '. i1 i2'
                    'i3 i4 i5';
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(2, 1fr);

                &__item {
                    @for $i from 1 through 5 {
                        &:nth-child(#{$i}) {
                            grid-area: i#{$i};
                        }
                    }
                }
            }
        }
    }
}
