.ontheweb-item {
    $this: &;

    @extend %link-block-context;
    flex-grow: 1;

    &__content {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: end;
        margin: 0 auto;
        min-height: 155px;
        padding: 35px 29px;
        text-align: center;

        @include breakpoint(medium down) {
            min-height: 150px;
        }
    }

    &__title {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--2);
        line-height: 1.25;

        a {
            @extend %link-block;
            @extend %underline-context;
        }

        .far {
            text-decoration: none;
        }

        @include fa-icon-style(false) {
            @include font(null, 2.7rem, var(--fw-normal));
            color: $color-black;
            display: block;
            margin-top: 14px;

            @include breakpoint(small down) {
                margin-top: 10px;
            }
        }

        .underline {
            @include multiline-underline($color: $color-black);

            @include on-event {
                &::after {
                    outline-offset: -4px;
                }
            }
        }
    }

    &.is-reverse {
        @include on-event() {
            #{$this}__picture {
                img {
                    border-radius: 50%;
                    height: 170px;
                    mask-image: none;
                    transform: translateZ(-2px);
                }
            }
        }

        #{$this}__title {
            color: $color-black;
            display: flex;
            flex-direction: column-reverse;
            font-size: 1.8rem;
            font-weight: var(--fw-bold);
            padding: 0 40px;

            @include fa-icon-style(false) {
                font-size: 1.8rem;
                font-weight: var(--fw-light);
                margin: 7px 0;
            }
        }

        #{$this}__content {
            align-items: center;
            padding: 0;
        }

        #{$this}__picture {
            @include size(170px, 147px);
            border-radius: 0;
            -webkit-mask-image: image("Mask-Image-Evts.svg");
            mask-image: image("Mask-Image-Evts.svg");
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: contain;
            mask-size: contain;
            transition: all 0.8s;

            img {
                @include size(170px);
                border-radius: 46%;
                mask-image: none;
            }
        }
    }
}
