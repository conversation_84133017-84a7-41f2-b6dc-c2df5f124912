.media-element {
    display: block;
    margin: 70px auto;
    width: 100%;

    @include breakpoint(medium down) {
        margin: 50px auto;
    }

    audio,
    iframe,
    video {
        display: block;
        width: 100%;
    }

    video {
        @include focus-outline;
        height: auto;
    }

    audio {
        @include focus-outline($color: $color-white);
    }

    div.iframe,
    iframe {
        @include absolute(0, null, null, 0);
        @include size(100%);
        border: 0;
        margin: 0;
    }

    &__title {
        @include font(var(--typo-1), 2.2rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 1.18;
        margin-bottom: 20px;
        margin-top: 0;

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__wrapper {
        margin: 0 auto;
        max-width: 996px;
        position: relative;
        width: 100%;
    }

    &__ratio {
        display: block;
        height: auto;
        width: 100%;
    }

    .tac_float {
        padding-left: 10px;
        padding-right: 10px;

        button {
            margin: 5px;
        }
    }

    .click-and-roll {
        $this: ".click-and-roll";
        border-top: 0;
        margin: 2px 0 0;

        &__toggle {
            min-height: 53px;
            padding: 0;

            @include breakpoint(small down) {
                align-items: flex-start;
            }

            @include on-event {
                background-color: var(--color-1--1);

                #{$this}__toggle-text {
                    color: $color-white;
                }
            }

            &::after {
                content: none;
            }
        }

        &__toggle-icon {
            @include icon-before($fa-var-angle-down);
            @include min-size(45px, 40px);
            padding: 17px 20px;

            @include breakpoint(small down) {
                padding: 15px 10px;
            }

            &::before {
                color: var(--color-1--1);
                font-size: 1.9rem;
                font-weight: var(--fw-normal);
            }
        }

        &__toggle-text {
            @include font(var(--typo-1), 1.4rem, var(--fw-normal));
            @include trs;
            color: $color-3--4;
            padding: 17px 20px;
            width: 100%;

            @include breakpoint(small down) {
                line-height: 1.3;
                padding: 15px 0 15px 15px;
            }
        }

        &.is-open {
            #{$this}__toggle {
                background-color: var(--color-1--1);
            }

            #{$this}__toggle-icon {
                &::before {
                    color: $color-white;
                    content: fa-content($fa-var-angle-down);
                    transform: scaleY(-1);
                }
            }

            #{$this}__toggle-text {
                color: $color-white;
            }
        }
    }
}

h2.gallery__title {
    @include font(null, 45px, var(--fw-black));
    color: var(--color-1--1);

    @include breakpoint(medium down) {
        font-size: 38px;
    }

    @include breakpoint(small down) {
        font-size: 28px;
        margin: 50px 0 15px;
    }
}

h3.gallery__title {
    @include font(null, 40px, var(--fw-bold));
    color: var(--color-1--4);

    @include breakpoint(medium down) {
        font-size: 34px;
    }

    @include breakpoint(small down) {
        font-size: 26px;
        margin: 40px 0 15px;
    }
}

h4.gallery__title {
    @include font(null, 32px, var(--fw-bold));
    color: var(--color-1--2);

    @include breakpoint(medium down) {
        font-size: 26px;
    }

    @include breakpoint(small down) {
        font-size: 23px;
        margin: 35px 0 10px;
    }
}

h5.gallery__title {
    @include font(null, 24px, var(--fw-medium));

    @include breakpoint(medium down) {
        font-size: 20px;
    }

    @include breakpoint(small down) {
        margin: 50px 0 20px;
    }
}

h6.gallery__title {
    color: $color-3--4;
    font-size: 20px;

    @include breakpoint(small down) {
        font-size: 16px;
        margin: 10px 0 5px;
    }
}
