.publications-content {
    $this: &;

    .section {
        &__content {
            padding: 0;

            @include breakpoint(medium down, true) {
                padding: 0 17px;
            }

            @include breakpoint(small down, true) {
                padding: 0;
            }
        }

        &__more-links {
            justify-content: flex-end;
            margin-top: 42px;
            width: 83%;

            .btn {
                background-color: var(--color-1--1);
                border-radius: 40px;
                color: $color-white;
                padding: 24px 45px;
            }

            @include breakpoint(medium down, true) {
                padding-left: 230px;
            }

            @include breakpoint(medium down) {
                justify-content: center;
                margin-left: 0;
                margin-top: 130px;
                padding-left: 0;
                width: 100%;
            }

            @include breakpoint(small down, true) {
                justify-content: center;
                padding-left: 0;
            }
        }
    }

    &.is-full-width {
        .list {
            display: block;

            .list__item {
                width: 830px;

                &:not(:last-child) {
                    margin-bottom: 270px;

                    @include breakpoint(medium down) {
                        margin-bottom: 150px;
                    }
                }
            }
        }
    }

    &.is-width-66 {
        .section__more-links {
            justify-content: flex-start;
            margin-top: 150px;
            padding-left: 40px;
            width: 100%;

            @include breakpoint(medium down) {
                justify-content: center;
                margin-top: -44px;
                padding: 0;
            }

            @include breakpoint(small down) {
                margin-top: 0;
            }
        }

        .list__item {
            position: relative;

            @include breakpoint(large only) {
                &:not(:last-child) {
                    margin-bottom: 200px;
                }
            }

            @include breakpoint(medium down) {
                margin-bottom: 200px;
            }

            &::before {
                @include size(75px, calc(100% + 130px));
                @include absolute(-20px, null, 0, -80px);
                background-color: $color-white;
                content: "";

                @include breakpoint(medium down) {
                    height: calc(100% + 150px);
                    left: -25px;
                    top: -30px;
                }
            }
        }
    }

    &.is-width-33 {
        .section__more-links {
            margin-left: 30px;
            margin-top: 110px;
            width: 100%;

            @include breakpoint(medium down) {
                justify-content: center;
                margin-left: 0 !important;
                padding: 0;
            }
        }

        .list__item {
            position: relative;

            @include breakpoint(large only) {
                &:not(&:last-child) {
                    margin-bottom: 140px;
                }
            }

            @include breakpoint(medium down) {
                &:not(:first-child) {
                    margin-top: 100px;
                }
            }

            &::before {
                @include size(55px, calc(100% + 140px));
                @include absolute(0, null, 0, -20px);
                background-color: $color-white;
                content: "";

                @include breakpoint(medium down) {
                    left: -40px;
                    width: 75px;
                }
            }
        }
    }

    .list {
        display: block;

        .list__item {
            width: 830px;

            @include breakpoint(small down) {
                @include size(100% !important);
                margin: 120px 0;

                &::before {
                    content: none;
                }
            }
        }
    }
}

.publications-home {
    &__container {
        @extend %container;
    }

    .section {
        &__content {
            padding: 0 37px 0 42px;

            @include breakpoint(medium down) {
                padding: 0 37px 0 33px;
            }

            @include breakpoint(small down) {
                padding: 0;
            }
        }

        &__more-links {
            justify-content: flex-start;
            padding-left: 412px;

            @include breakpoint(medium down) {
                padding-left: 240px;
            }

            @include breakpoint(small down) {
                justify-content: center;
                padding-left: 0;
            }

            .btn {
                margin-top: -45px;
                position: relative;
                z-index: 5;

                @include breakpoint(medium down) {
                    margin-top: 35px;
                }

                @include breakpoint(small down) {
                    margin-top: 38px;
                }
            }
        }
    }
}
