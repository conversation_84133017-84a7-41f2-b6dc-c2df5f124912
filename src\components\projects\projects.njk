{% from 'views/core-components/section.njk' import Section %}
{% from 'views/core-components/title.njk' import TitlePrimary %}
{% from 'views/core-components/image.njk' import Image %}
{% from 'views/core-components/link.njk' import Link %}
{% from 'views/utils/utils.njk' import svg with context %}
{%- from 'components/next-council/next-council.njk' import NextCouncil -%}

{#
    ProjectItem template.
#}
{% macro ProjectItem(
    icon = 'projects1',
    title = 'Nos projets',
    subtitle = 'pour aujourd\'hui <br>et pour demain'
) %}
    <div class="project-item">
        <div class="project-item__content">
            <div class="project-item__image" aria-hidden="true">
                {{ svg('icons/' + icon, 58, 58) }}
            </div>
            <div class="project-item__text">
                <p class="project-item__title">
                    <a href="#" class="project-item__title-link">
                        <span class="underline">{{ title | safe }}</span>
                        <span class="project-item__subtitle">{{ subtitle | safe }}</span>
                    </a>
                </p>
            </div>
        </div>
    </div>
{% endmacro %}

{#
    ProjectFocusInfo template.
#}
{% macro ProjectFocusInfo(
    category = 'Lorem ipsum',
    title = 'Maecenas mollis, augue vitae sagittis'
) %}
    <div class="project-focus__content is-inverted">
        <div class="project-focus__text">
            {% if title %}
                <h3 class="item-title is-large project-focus__title">
                    {% if category %}
                        <span class="theme is-large project-focus__category">{{ category }}</span>
                        <span class="sr-only"> : </span>
                    {% endif %}
                    <a href="#" class="project-focus__title-link">
                        <span class="underline">{{ title | capitalize }}</span>
                    </a>
                </h3>
            {% endif %}
        </div>
    </div>
{% endmacro %}

{#
    ProjectFocus template.
#}
{% macro ProjectFocus(
    class = '',
    imageSizes = ['479x257?479', '767x411?767', '1279x684?1279', '1920x823'],
    index = 0
) %}
    <article class="project-focus {{ class }}">
        {{ ProjectFocusInfo() }}
        {{ Image({
            sizes: imageSizes,
            className: 'project-focus__picture',
            serviceID: range(50) | random,
            alt: 'image alt'
        }) }}
    </article>
{% endmacro %}

{% set projectsHomeDefaults = {
    modifier: '',
    titleText: 'Avancer ensemble',
    listModifier: '',
    itemsData: [
        ['projects1', 'Nos projets', 'pour aujourd\'hui <br>et pour demain'],
        ['projects2', 'Que fait ma ville', 'pour moi et pour <br>mon quotidien ?'],
        ['projects3', 'J’ai une idée', 'pour mon quartier, <br>ma ville, ...']
    ],
    moreButton: false
} %}

{#
    ProjectsHome template.
#}
{% macro ProjectsHome(settings = {}) %}
    {% set settings = Helpers.merge(projectsHomeDefaults, settings) %}
    {% call Section(className = 'projects ' + settings.modifier, container = 'projects__container') %}
        <div class="section__title projects__title">
            {{ TitlePrimary(
                text = settings.titleText
            ) }}
        </div>
        <div class="section__content projects__content">
            {{ ProjectFocus() }}
            <ul class="project-list {{ settings.listModifier }}">
                {% for icon, title, subtitle in settings.itemsData %}
                    <li class="project-list__item">
                        {{ ProjectItem(icon, title, subtitle) }}
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="section__council">
            {{ NextCouncil() }}
            {{ NextCouncil(isSecondary = true) }}
        </div>
        {% if settings.moreButton %}
            <div class="section__more-links">
                {{ Link(
                    href = '#',
                    text = 'Tous les grands projets',
                    className = 'btn',
                    icon = 'far fa-plus'
                ) }}
            </div>
        {% endif %}
    {% endcall %}
{% endmacro %}
